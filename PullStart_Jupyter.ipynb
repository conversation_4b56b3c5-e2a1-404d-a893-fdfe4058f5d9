{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8ba8ae66", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["测算日期： 2025-08-13\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:381: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, bottom_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:565: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Cumret_List = pd.concat([Cumret_List, cumret_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:636: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Rise_List = pd.concat([Rise_List, rise_temp_df], ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["行业: 电子, 回撤转折日期: 2025-08-08, 转折日期: 2025-08-07, 转折回升幅度: 4.65, 下行天数: 29, 下行幅度: -20.58\n", "行业: 医药生物, 回撤转折日期: 2025-08-07, 转折日期: 2025-08-04, 转折回升幅度: 1.25, 下行天数: 272, 下行幅度: -32.81\n", "行业: 通信, 回撤转折日期: 2025-08-04, 转折日期: 2025-07-31, 转折回升幅度: 10.18, 下行天数: 32, 下行幅度: -22.60\n", "行业: 有色金属, 回撤转折日期: 2025-07-31, 转折日期: 2025-07-25, 转折回升幅度: 5.65, 下行天数: 177, 下行幅度: -26.39\n", "行业: 电力设备, 回撤转折日期: 2025-07-31, 转折日期: 2024-11-25, 转折回升幅度: 2.59, 下行天数: 221, 下行幅度: -24.94\n", "行业: 汽车, 回撤转折日期: 2025-07-30, 转折日期: 2025-03-20, 转折回升幅度: 3.75, 下行天数: 50, 下行幅度: -20.71\n", "行业: 综合, 回撤转折日期: 2025-07-25, 转折日期: 2025-07-17, 转折回升幅度: 4.03, 下行天数: 124, 下行幅度: -41.90\n", "行业: 传媒, 回撤转折日期: 2025-07-22, 转折日期: 2025-02-14, 转折回升幅度: 4.38, 下行天数: 146, 下行幅度: -38.62\n", "行业: 机械设备, 回撤转折日期: 2025-04-08, 转折日期: 2025-03-10, 转折回升幅度: 13.51, 下行天数: 27, 下行幅度: -19.81\n", "行业: 计算机, 回撤转折日期: 2025-04-08, 转折日期: 2025-03-06, 转折回升幅度: 7.95, 下行天数: 174, 下行幅度: -33.64\n", "处理股票 300280.SZ 时出错: single positional indexer is out-of-bounds\n", "行业排序结果:\n", "1. 机械设备: 106.79, 涨停数/总数:13/520\n", "2. 计算机: 85.78, 涨停数/总数:7/333\n", "3. 汽车: 34.78, 涨停数/总数:12/266\n", "4. 电力设备: 30.84, 涨停数/总数:6/349\n", "5. 电子: 28.17, 涨停数/总数:8/452\n", "6. 医药生物: 27.99, 涨停数/总数:2/469\n", "7. 通信: 16.06, 涨停数/总数:5/125\n", "8. 传媒: 14.41, 涨停数/总数:1/128\n", "9. 有色金属: 10.77, 涨停数/总数:8/131\n", "10. 综合: -3.53, 涨停数/总数:0/17\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2176: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  pull_start_list['pullstart_sort_position'] = pull_start_list.groupby('industry').cumcount() + 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-08-13 筛选数据存储 \n", "存储PullStart股票条目： 26\n", "存储成功\n", "PullStart股票行业分布：\n", "           count\n", "industry       \n", "机械设备          5\n", "有色金属          5\n", "计算机           4\n", "汽车            4\n", "电力设备          4\n", "电子            3\n", "通信            1\n", "医药生物          0\n", "传媒            0\n", "综合            0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2399: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  pull_indus_count = pull_start_list[['ts_code', 'industry']].groupby('industry').count()\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2425: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  by=['industry', 'stk_checkperiod_sumratio'], ascending=[True, False]).groupby('industry').head(20)\n"]}], "source": ["from function_ai.StkPick_ModelFunc import track_pullstart_stocks\n", "from function_ai.Func_Base import get_trade_date\n", "# trade_date = get_trade_date(start_date='2025-06-20', end_date='2025-07-25')\n", "# trade_date = trade_date[::-1]\n", "trade_date = ['2025-08-13']\n", "for date in trade_date:\n", "    # industry_list = ['房地产', '纺织服饰', '建筑装饰', '建筑材料', '轻工制造', '家用电器', '交通运输', '公用事业', \n", "                    #  '煤炭', '农林牧渔']\n", "    # trend_industry_list = ['非银金融', '通信', '有色金属', '汽车', '环保', '纺织服饰', '家用电器', '综合', '基础化工', '传媒', '国防军工', '机械设备', '计算机']\n", "    print('测算日期：', date)\n", "    trend_industry_list = None\n", "    # recent_industry_list = ['通信', '环保', '基础化工']\n", "    recent_industry_list = None\n", "    # industry_list = None\n", "    # '2025-03-24', '2025-03-10', '2025-02-28', '2025-02-18', \n", "    end_date, trend_startdate = date, '2025-04-07'\n", "    recent_turndate = ['2025-05-23', '2025-06-10', '2025-06-19', '2025-06-27', '2025-07-07', '2025-07-15', '2025-07-23', '2025-07-31', '2025-08-07']\n", "    # recent_turndate = ['2025-07-15', '2025-08-01']\n", "    result_df_head, result_df_turn, pull_start_list, pull_start_list_core, result_df_recentindus = track_pullstart_stocks(\n", "        end_date=end_date, trend_startdate=trend_startdate,\n", "        recent_turndate=recent_turndate,\n", "        rise_stop_signal=True,\n", "        industry_list=trend_industry_list,\n", "        limit_num=80, store_mode=True,\n", "        recent_indus=recent_industry_list,\n", "        recentindus_calstartdate='2025-03-18')"]}, {"cell_type": "code", "execution_count": 3, "id": "6ae2b7d4", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "ts_code", "rawType": "object", "type": "string"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "industry", "rawType": "category", "type": "unknown"}, {"name": "Period_TurnDate", "rawType": "object", "type": "string"}, {"name": "Now_SecDate", "rawType": "object", "type": "string"}, {"name": "recent_bottom_date", "rawType": "object", "type": "string"}, {"name": "Now_PRA_Rate", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PRA_MaxRate_BreachCount", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_BandDiff", "rawType": "float64", "type": "float"}, {"name": "PRA2Cls_Percentile_Diff", "rawType": "float64", "type": "float"}, {"name": "PostNowSecMaxPRA_PostTurnRank5PRA_Diff", "rawType": "float64", "type": "float"}, {"name": "NowSec_PRA2Close_CoverDays_Diff", "rawType": "float64", "type": "float"}, {"name": "PRA2Cls_Percentile_Ratio", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_MaxPGV_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "NowSec_MaxPRA_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MaxCls_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "PostTurn_VGVRank5_NearNowDate", "rawType": "object", "type": "string"}, {"name": "PostTurn_VGVRank5_NearNowDate_Days", "rawType": "float64", "type": "float"}, {"name": "NowSec_PGV_MaxRollAvg_UpCoverDays", "rawType": "float64", "type": "float"}, {"name": "NowSec_MaxClose_UpCoverDays", "rawType": "float64", "type": "float"}, {"name": "prepost_recentbottom_daysdiff", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_PRA_MaxRate_CoverDays", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_VRA_MaxRate_CoverDays", "rawType": "float64", "type": "float"}, {"name": "peak2recentbottom_lastdays", "rawType": "float64", "type": "float"}, {"name": "recentbottom2now_lastdays", "rawType": "int64", "type": "integer"}, {"name": "PostNowSec_PGV_MaxRollAvg", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_LowBand", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_UpBand", "rawType": "float64", "type": "float"}, {"name": "PostNowSec2PreNowPeak_PRV2Price_RatioDiff", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak_PRV_Top3Mean", "rawType": "float64", "type": "float"}, {"name": "PullStart_Score", "rawType": "float64", "type": "float"}], "ref": "72070bfc-4460-4bbd-8462-e929c0fa903b", "rows": [["1311", "603158.SH", "腾龙股份", "汽车", "2025-04-08", "2025-07-31", "2025-07-31", "135.091", "2.0", "7.742", "-1.139", "0.977", "-32.0", "0.293", "0.562", "0.473", "1.612", "2025-07-07", "27.0", "58.0", "90.0", "-3.0", "99.0", "98.0", "13.0", "10", "0.129", "0.031", "0.24", "188.623", "0.043", "121.5"], ["2597", "300561.SZ", "*ST汇科", "计算机", "2025-07-07", "2025-07-22", "2025-07-14", "100.022", "3.0", "1.86", "-1.063", "2.19", "5.0", "0.802", "3.788", "4.293", "5.356", "2025-08-07", "4.0", "81.0", "76.0", "19.0", "10.0", "8.0", "4.0", "23", "0.911", "0.15", "0.279", "167.769", "0.304", "91.5"], ["764", "601002.SH", "晋亿实业", "机械设备", "2025-04-08", "2025-07-31", "2025-07-18", "20.769", "2.0", "9.545", "-0.948", "1.023", "-1128.0", "0.382", "0.546", "0.586", "1.534", "2025-08-04", "7.0", "81.0", "1209.0", "15.0", "25.0", "2.0", "4.0", "19", "0.148", "0.022", "0.21", "181.283", "0.05", "83.5"], ["2366", "603528.SH", "多伦科技", "计算机", "2025-04-08", "2025-08-08", "2025-06-20", "146.774", "1.0", "7.625", "-0.901", "0.573", "-113.0", "0.291", "0.629", "0.37", "1.271", "2025-07-24", "14.0", "4.0", "117.0", "24.0", "13.0", "12.0", "15.0", "39", "0.153", "0.04", "0.305", "-21.126", "0.178", "103.0"], ["2413", "688291.SH", "金橙子", "计算机", "2025-04-07", "2025-07-23", "2025-07-23", null, null, "15.718", "-0.708", "0.455", "5.0", "0.272", "0.802", "0.264", "0.972", "2025-08-06", "5.0", "22.0", "17.0", "-4.0", null, null, "11.0", "7", "0.951", "0.174", "2.735", "-66.126", "2.953", null], ["675", "000962.SZ", "东方钽业", "有色金属", "2024-12-25", "2025-07-31", "2025-06-20", "74.043", "1.0", "8.0", "-0.671", "0.723", "-1156.0", "0.406", "0.64", "0.459", "1.13", "2025-07-08", "26.0", "53.0", "1209.0", "21.0", "61.0", "4.0", "18.0", "39", "0.414", "0.092", "0.736", "-47.751", "0.736", "91.0"], ["1561", "603031.SH", "安孚科技", "电力设备", "2025-01-10", "2025-07-30", "2025-07-30", "30.036", "1.0", "9.397", "-0.667", "0.872", "-188.0", "0.472", "0.724", "0.596", "1.263", "2025-07-24", "14.0", "114.0", "302.0", "-7.0", "3.0", "9.0", "18.0", "11", "0.766", "0.121", "1.137", "93.041", "0.38", "82.5"], ["1451", "300237.SZ", "美晨科技", "汽车", "2025-04-07", "2025-08-01", "2025-06-20", "89.783", "2.0", "10.5", "-0.596", "1.008", "-117.0", "0.441", "0.667", "0.47", "1.066", "2025-08-05", "6.0", "41.0", "158.0", "33.0", "35.0", "43.0", "6.0", "39", "0.087", "0.014", "0.147", "-44.416", "0.147", "95.0"], ["1687", "002249.SZ", "大洋电机", "电力设备", "2025-01-13", "2025-07-31", "2025-06-20", "195.574", "3.0", "9.071", "-0.588", "0.807", "-789.0", "0.51", "0.969", "0.611", "1.199", "2025-08-04", "7.0", "84.0", "873.0", "11.0", "95.0", "3.0", "28.0", "39", "0.18", "0.028", "0.254", "47.763", "0.112", "107.0"], ["1418", "002454.SZ", "松芝股份", "汽车", "2025-04-08", "2025-07-31", "2025-07-31", "29.495", "1.0", "5.513", "-0.524", "1.293", "-659.0", "0.64", "0.785", "0.931", "1.455", "2025-07-08", "26.0", "57.0", "716.0", "-3.0", "21.0", "149.0", "13.0", "10", "0.228", "0.039", "0.215", "163.199", "0.082", "78.5"], ["1422", "002516.SZ", "旷达科技", "汽车", "2025-04-08", "2025-07-31", "2025-07-31", "160.126", "2.0", "6.304", "-0.516", "1.277", "-749.0", "0.682", "1.025", "1.107", "1.623", "2025-08-12", "1.0", "106.0", "855.0", "-12.0", "31.0", "1.0", "22.0", "10", "0.166", "0.023", "0.145", "2.143", "0.143", "58.0"], ["800", "603273.SH", "天元智能", "机械设备", "2025-04-08", "2025-08-12", "2025-08-12", "8.547", "0.0", "7.264", "-0.461", "0.78", "-151.0", "0.585", "0.623", "0.649", "1.11", "2025-07-17", "19.0", "7.0", "158.0", "-7.0", "8.0", "12.0", "9.0", "2", "0.448", "0.087", "0.632", "-25.045", "0.572", "131.0"], ["2158", "002938.SZ", "鹏鼎控股", "电子", "2025-04-09", "2025-08-06", "2025-08-06", "-5.799", "1.0", "7.212", "-0.46", "0.943", "-1200.0", "0.581", "0.613", "0.637", "1.097", "2025-06-04", "50.0", "9.0", "1209.0", "-4.0", "1.0", "4.0", "10.0", "6", "1.242", "0.226", "1.63", "-35.829", "1.791", "97.5"], ["1042", "002833.SZ", "弘亚数控", "机械设备", "2025-04-08", "2025-07-31", "2025-07-31", "76.149", "3.0", "8.333", "-0.432", "1.381", "-115.0", "0.723", "1.116", "1.13", "1.562", "2025-08-01", "8.0", "177.0", "292.0", "-3.0", "99.0", "209.0", "13.0", "10", "0.487", "0.051", "0.425", "532.785", "0.075", "86.5"], ["1733", "002879.SZ", "长缆科技", "电力设备", "2025-01-03", "2025-07-31", "2025-07-15", "56.935", "2.0", "12.877", "-0.431", "0.461", "-44.0", "0.467", "0.496", "0.378", "0.809", "2025-08-06", "5.0", "55.0", "99.0", "18.0", "3.0", "16.0", "4.0", "22", "0.312", "0.057", "0.734", "10.725", "0.257", "98.5"], ["2681", "603083.SH", "剑桥科技", "通信", "2025-04-08", "2025-08-08", "2025-08-08", "78.393", "2.0", "9.212", "-0.412", "0.92", "-475.0", "0.629", "0.779", "0.699", "1.111", "2025-05-08", "68.0", "19.0", "494.0", "-5.0", "27.0", "2.0", "9.0", "4", "1.588", "0.226", "2.082", "20.175", "1.27", "87.0"], ["623", "601020.SH", "华钰矿业", "有色金属", "2025-01-06", "2025-08-12", "2025-07-31", "124.122", "1.0", "9.119", "-0.408", "0.453", "-80.0", "0.458", "0.631", "0.345", "0.753", "2025-06-11", "45.0", "14.0", "94.0", "-4.0", "86.0", "2.0", "14.0", "10", "0.389", "0.101", "0.921", "-38.523", "0.613", "149.5"], ["812", "603337.SH", "杰克股份", "机械设备", "2025-04-08", "2025-07-30", "2025-07-30", "46.318", "2.0", "6.243", "-0.39", "1.458", "-716.0", "0.733", "1.586", "1.068", "1.458", "2025-07-11", "23.0", "493.0", "1209.0", "-2.0", "2.0", "21.0", "13.0", "11", "1.691", "0.23", "1.436", "32.643", "1.115", "88.5"], ["1917", "603920.SH", "世运电路", "电子", "2025-04-08", "2025-08-08", "2025-08-08", "72.676", "2.0", "6.163", "-0.35", "1.216", "-32.0", "0.687", "0.686", "0.767", "1.117", "2025-07-08", "26.0", "84.0", "116.0", "-7.0", "15.0", "2.0", "11.0", "4", "1.038", "0.19", "1.171", "20.683", "0.831", "97.0"], ["2550", "300290.SZ", "荣科科技", "计算机", "2025-04-07", "2025-08-08", "2025-07-07", "182.561", "2.0", "5.316", "-0.309", "1.352", "-9.0", "0.807", "1.409", "1.289", "1.598", "2025-08-13", "0.0", "114.0", "123.0", "-6.0", "317.0", "2.0", "34.0", "28", "0.92", "0.133", "0.707", "100.989", "0.421", "33.0"], ["2124", "002463.SZ", "沪电股份", "电子", "2025-04-08", "2025-08-08", "2025-08-08", "64.894", "1.0", "7.39", "-0.308", "0.957", "-1200.0", "0.71", "0.843", "0.755", "1.063", "2025-06-03", "51.0", "9.0", "1209.0", "-5.0", "12.0", "4.0", "9.0", "4", "1.318", "0.213", "1.574", "-18.32", "1.546", "88.0"], ["630", "601609.SH", "金田股份", "有色金属", "2025-04-08", "2025-08-01", "2025-08-01", "78.866", "2.0", "8.69", "-0.133", "1.305", "-782.0", "0.887", "1.47", "1.04", "1.173", "2025-07-08", "26.0", "162.0", "944.0", "-7.0", "18.0", "1.0", "16.0", "9", "0.29", "0.029", "0.252", "1.238", "0.269", "104.5"], ["602", "600301.SH", "华锡有色", "有色金属", "2025-01-03", "2025-08-01", "2025-07-17", "73.43", "1.0", "6.161", "-0.12", "0.914", "-91.0", "0.859", "0.955", "0.732", "0.852", "2025-07-08", "26.0", "12.0", "103.0", "17.0", "86.0", "2.0", "3.0", "20", "0.561", "0.112", "0.69", "-9.553", "0.59", "122.5"], ["1839", "301217.SZ", "铜冠铜箔", "电力设备", "2025-04-07", "2025-08-07", "2025-07-16", "68.09", "1.0", "29.811", "0.011", "1.354", "-361.0", "1.009", "1.14", "1.184", "1.173", "2025-06-03", "51.0", "493.0", "854.0", "16.0", "12.0", "9.0", "5.0", "21", "1.874", "0.053", "1.58", "6.883", "1.58", "47.0"], ["933", "688648.SH", "中邮科技", "机械设备", "2025-04-07", "2025-07-31", "2025-07-31", "138.901", "3.0", "17.285", "0.116", "1.0", "8.0", "1.149", "1.075", "0.894", "0.778", "2025-05-08", "68.0", "51.0", "43.0", "-9.0", "73.0", "16.0", "19.0", "10", "3.951", "0.246", "4.252", "75.802", "2.058", "116.5"], ["632", "601702.SH", "华峰铝业", "有色金属", "2024-11-18", "2025-07-31", "2025-07-09", "144.91", "1.0", "6.775", "0.127", "0.88", "-13.0", "1.233", "0.862", "0.671", "0.544", "2025-08-06", "5.0", "76.0", "89.0", "18.0", "473.0", "2.0", "8.0", "26", "0.409", "0.08", "0.542", "130.654", "0.17", "78.5"]], "shape": {"columns": 31, "rows": 26}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>name</th>\n", "      <th>industry</th>\n", "      <th>Period_TurnDate</th>\n", "      <th>Now_SecDate</th>\n", "      <th>recent_bottom_date</th>\n", "      <th>Now_PRA_Rate</th>\n", "      <th>PostNowSec_PRA_MaxRate_BreachCount</th>\n", "      <th>Turn2NowSec_PRA_BandDiff</th>\n", "      <th>PRA2Cls_Percentile_Diff</th>\n", "      <th>...</th>\n", "      <th>Recent3Day_PRA_MaxRate_CoverDays</th>\n", "      <th>Recent3Day_VRA_MaxRate_CoverDays</th>\n", "      <th>peak2recentbottom_lastdays</th>\n", "      <th>recentbottom2now_lastdays</th>\n", "      <th>PostNowSec_PGV_MaxRollAvg</th>\n", "      <th>Turn2NowSec_PRA_LowBand</th>\n", "      <th>Turn2NowSec_PRA_UpBand</th>\n", "      <th>PostNowSec2PreNowPeak_PRV2Price_RatioDiff</th>\n", "      <th>PreNowPeak_PRV_Top3Mean</th>\n", "      <th>PullStart_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1311</th>\n", "      <td>603158.SH</td>\n", "      <td>腾龙股份</td>\n", "      <td>汽车</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-07-31</td>\n", "      <td>2025-07-31</td>\n", "      <td>135.091</td>\n", "      <td>2.0</td>\n", "      <td>7.742</td>\n", "      <td>-1.139</td>\n", "      <td>...</td>\n", "      <td>99.0</td>\n", "      <td>98.0</td>\n", "      <td>13.0</td>\n", "      <td>10</td>\n", "      <td>0.129</td>\n", "      <td>0.031</td>\n", "      <td>0.240</td>\n", "      <td>188.623</td>\n", "      <td>0.043</td>\n", "      <td>121.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2597</th>\n", "      <td>300561.SZ</td>\n", "      <td>*ST汇科</td>\n", "      <td>计算机</td>\n", "      <td>2025-07-07</td>\n", "      <td>2025-07-22</td>\n", "      <td>2025-07-14</td>\n", "      <td>100.022</td>\n", "      <td>3.0</td>\n", "      <td>1.860</td>\n", "      <td>-1.063</td>\n", "      <td>...</td>\n", "      <td>10.0</td>\n", "      <td>8.0</td>\n", "      <td>4.0</td>\n", "      <td>23</td>\n", "      <td>0.911</td>\n", "      <td>0.150</td>\n", "      <td>0.279</td>\n", "      <td>167.769</td>\n", "      <td>0.304</td>\n", "      <td>91.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>764</th>\n", "      <td>601002.SH</td>\n", "      <td>晋亿实业</td>\n", "      <td>机械设备</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-07-31</td>\n", "      <td>2025-07-18</td>\n", "      <td>20.769</td>\n", "      <td>2.0</td>\n", "      <td>9.545</td>\n", "      <td>-0.948</td>\n", "      <td>...</td>\n", "      <td>25.0</td>\n", "      <td>2.0</td>\n", "      <td>4.0</td>\n", "      <td>19</td>\n", "      <td>0.148</td>\n", "      <td>0.022</td>\n", "      <td>0.210</td>\n", "      <td>181.283</td>\n", "      <td>0.050</td>\n", "      <td>83.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2366</th>\n", "      <td>603528.SH</td>\n", "      <td>多伦科技</td>\n", "      <td>计算机</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-08-08</td>\n", "      <td>2025-06-20</td>\n", "      <td>146.774</td>\n", "      <td>1.0</td>\n", "      <td>7.625</td>\n", "      <td>-0.901</td>\n", "      <td>...</td>\n", "      <td>13.0</td>\n", "      <td>12.0</td>\n", "      <td>15.0</td>\n", "      <td>39</td>\n", "      <td>0.153</td>\n", "      <td>0.040</td>\n", "      <td>0.305</td>\n", "      <td>-21.126</td>\n", "      <td>0.178</td>\n", "      <td>103.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2413</th>\n", "      <td>688291.SH</td>\n", "      <td>金橙子</td>\n", "      <td>计算机</td>\n", "      <td>2025-04-07</td>\n", "      <td>2025-07-23</td>\n", "      <td>2025-07-23</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>15.718</td>\n", "      <td>-0.708</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>11.0</td>\n", "      <td>7</td>\n", "      <td>0.951</td>\n", "      <td>0.174</td>\n", "      <td>2.735</td>\n", "      <td>-66.126</td>\n", "      <td>2.953</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>675</th>\n", "      <td>000962.SZ</td>\n", "      <td>东方钽业</td>\n", "      <td>有色金属</td>\n", "      <td>2024-12-25</td>\n", "      <td>2025-07-31</td>\n", "      <td>2025-06-20</td>\n", "      <td>74.043</td>\n", "      <td>1.0</td>\n", "      <td>8.000</td>\n", "      <td>-0.671</td>\n", "      <td>...</td>\n", "      <td>61.0</td>\n", "      <td>4.0</td>\n", "      <td>18.0</td>\n", "      <td>39</td>\n", "      <td>0.414</td>\n", "      <td>0.092</td>\n", "      <td>0.736</td>\n", "      <td>-47.751</td>\n", "      <td>0.736</td>\n", "      <td>91.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1561</th>\n", "      <td>603031.SH</td>\n", "      <td>安孚科技</td>\n", "      <td>电力设备</td>\n", "      <td>2025-01-10</td>\n", "      <td>2025-07-30</td>\n", "      <td>2025-07-30</td>\n", "      <td>30.036</td>\n", "      <td>1.0</td>\n", "      <td>9.397</td>\n", "      <td>-0.667</td>\n", "      <td>...</td>\n", "      <td>3.0</td>\n", "      <td>9.0</td>\n", "      <td>18.0</td>\n", "      <td>11</td>\n", "      <td>0.766</td>\n", "      <td>0.121</td>\n", "      <td>1.137</td>\n", "      <td>93.041</td>\n", "      <td>0.380</td>\n", "      <td>82.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1451</th>\n", "      <td>300237.SZ</td>\n", "      <td>美晨科技</td>\n", "      <td>汽车</td>\n", "      <td>2025-04-07</td>\n", "      <td>2025-08-01</td>\n", "      <td>2025-06-20</td>\n", "      <td>89.783</td>\n", "      <td>2.0</td>\n", "      <td>10.500</td>\n", "      <td>-0.596</td>\n", "      <td>...</td>\n", "      <td>35.0</td>\n", "      <td>43.0</td>\n", "      <td>6.0</td>\n", "      <td>39</td>\n", "      <td>0.087</td>\n", "      <td>0.014</td>\n", "      <td>0.147</td>\n", "      <td>-44.416</td>\n", "      <td>0.147</td>\n", "      <td>95.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1687</th>\n", "      <td>002249.SZ</td>\n", "      <td>大洋电机</td>\n", "      <td>电力设备</td>\n", "      <td>2025-01-13</td>\n", "      <td>2025-07-31</td>\n", "      <td>2025-06-20</td>\n", "      <td>195.574</td>\n", "      <td>3.0</td>\n", "      <td>9.071</td>\n", "      <td>-0.588</td>\n", "      <td>...</td>\n", "      <td>95.0</td>\n", "      <td>3.0</td>\n", "      <td>28.0</td>\n", "      <td>39</td>\n", "      <td>0.180</td>\n", "      <td>0.028</td>\n", "      <td>0.254</td>\n", "      <td>47.763</td>\n", "      <td>0.112</td>\n", "      <td>107.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1418</th>\n", "      <td>002454.SZ</td>\n", "      <td>松芝股份</td>\n", "      <td>汽车</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-07-31</td>\n", "      <td>2025-07-31</td>\n", "      <td>29.495</td>\n", "      <td>1.0</td>\n", "      <td>5.513</td>\n", "      <td>-0.524</td>\n", "      <td>...</td>\n", "      <td>21.0</td>\n", "      <td>149.0</td>\n", "      <td>13.0</td>\n", "      <td>10</td>\n", "      <td>0.228</td>\n", "      <td>0.039</td>\n", "      <td>0.215</td>\n", "      <td>163.199</td>\n", "      <td>0.082</td>\n", "      <td>78.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1422</th>\n", "      <td>002516.SZ</td>\n", "      <td>旷达科技</td>\n", "      <td>汽车</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-07-31</td>\n", "      <td>2025-07-31</td>\n", "      <td>160.126</td>\n", "      <td>2.0</td>\n", "      <td>6.304</td>\n", "      <td>-0.516</td>\n", "      <td>...</td>\n", "      <td>31.0</td>\n", "      <td>1.0</td>\n", "      <td>22.0</td>\n", "      <td>10</td>\n", "      <td>0.166</td>\n", "      <td>0.023</td>\n", "      <td>0.145</td>\n", "      <td>2.143</td>\n", "      <td>0.143</td>\n", "      <td>58.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>800</th>\n", "      <td>603273.SH</td>\n", "      <td>天元智能</td>\n", "      <td>机械设备</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-08-12</td>\n", "      <td>2025-08-12</td>\n", "      <td>8.547</td>\n", "      <td>0.0</td>\n", "      <td>7.264</td>\n", "      <td>-0.461</td>\n", "      <td>...</td>\n", "      <td>8.0</td>\n", "      <td>12.0</td>\n", "      <td>9.0</td>\n", "      <td>2</td>\n", "      <td>0.448</td>\n", "      <td>0.087</td>\n", "      <td>0.632</td>\n", "      <td>-25.045</td>\n", "      <td>0.572</td>\n", "      <td>131.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2158</th>\n", "      <td>002938.SZ</td>\n", "      <td>鹏鼎控股</td>\n", "      <td>电子</td>\n", "      <td>2025-04-09</td>\n", "      <td>2025-08-06</td>\n", "      <td>2025-08-06</td>\n", "      <td>-5.799</td>\n", "      <td>1.0</td>\n", "      <td>7.212</td>\n", "      <td>-0.460</td>\n", "      <td>...</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>10.0</td>\n", "      <td>6</td>\n", "      <td>1.242</td>\n", "      <td>0.226</td>\n", "      <td>1.630</td>\n", "      <td>-35.829</td>\n", "      <td>1.791</td>\n", "      <td>97.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1042</th>\n", "      <td>002833.SZ</td>\n", "      <td>弘亚数控</td>\n", "      <td>机械设备</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-07-31</td>\n", "      <td>2025-07-31</td>\n", "      <td>76.149</td>\n", "      <td>3.0</td>\n", "      <td>8.333</td>\n", "      <td>-0.432</td>\n", "      <td>...</td>\n", "      <td>99.0</td>\n", "      <td>209.0</td>\n", "      <td>13.0</td>\n", "      <td>10</td>\n", "      <td>0.487</td>\n", "      <td>0.051</td>\n", "      <td>0.425</td>\n", "      <td>532.785</td>\n", "      <td>0.075</td>\n", "      <td>86.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1733</th>\n", "      <td>002879.SZ</td>\n", "      <td>长缆科技</td>\n", "      <td>电力设备</td>\n", "      <td>2025-01-03</td>\n", "      <td>2025-07-31</td>\n", "      <td>2025-07-15</td>\n", "      <td>56.935</td>\n", "      <td>2.0</td>\n", "      <td>12.877</td>\n", "      <td>-0.431</td>\n", "      <td>...</td>\n", "      <td>3.0</td>\n", "      <td>16.0</td>\n", "      <td>4.0</td>\n", "      <td>22</td>\n", "      <td>0.312</td>\n", "      <td>0.057</td>\n", "      <td>0.734</td>\n", "      <td>10.725</td>\n", "      <td>0.257</td>\n", "      <td>98.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2681</th>\n", "      <td>603083.SH</td>\n", "      <td>剑桥科技</td>\n", "      <td>通信</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-08-08</td>\n", "      <td>2025-08-08</td>\n", "      <td>78.393</td>\n", "      <td>2.0</td>\n", "      <td>9.212</td>\n", "      <td>-0.412</td>\n", "      <td>...</td>\n", "      <td>27.0</td>\n", "      <td>2.0</td>\n", "      <td>9.0</td>\n", "      <td>4</td>\n", "      <td>1.588</td>\n", "      <td>0.226</td>\n", "      <td>2.082</td>\n", "      <td>20.175</td>\n", "      <td>1.270</td>\n", "      <td>87.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>623</th>\n", "      <td>601020.SH</td>\n", "      <td>华钰矿业</td>\n", "      <td>有色金属</td>\n", "      <td>2025-01-06</td>\n", "      <td>2025-08-12</td>\n", "      <td>2025-07-31</td>\n", "      <td>124.122</td>\n", "      <td>1.0</td>\n", "      <td>9.119</td>\n", "      <td>-0.408</td>\n", "      <td>...</td>\n", "      <td>86.0</td>\n", "      <td>2.0</td>\n", "      <td>14.0</td>\n", "      <td>10</td>\n", "      <td>0.389</td>\n", "      <td>0.101</td>\n", "      <td>0.921</td>\n", "      <td>-38.523</td>\n", "      <td>0.613</td>\n", "      <td>149.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>812</th>\n", "      <td>603337.SH</td>\n", "      <td>杰克股份</td>\n", "      <td>机械设备</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-07-30</td>\n", "      <td>2025-07-30</td>\n", "      <td>46.318</td>\n", "      <td>2.0</td>\n", "      <td>6.243</td>\n", "      <td>-0.390</td>\n", "      <td>...</td>\n", "      <td>2.0</td>\n", "      <td>21.0</td>\n", "      <td>13.0</td>\n", "      <td>11</td>\n", "      <td>1.691</td>\n", "      <td>0.230</td>\n", "      <td>1.436</td>\n", "      <td>32.643</td>\n", "      <td>1.115</td>\n", "      <td>88.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1917</th>\n", "      <td>603920.SH</td>\n", "      <td>世运电路</td>\n", "      <td>电子</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-08-08</td>\n", "      <td>2025-08-08</td>\n", "      <td>72.676</td>\n", "      <td>2.0</td>\n", "      <td>6.163</td>\n", "      <td>-0.350</td>\n", "      <td>...</td>\n", "      <td>15.0</td>\n", "      <td>2.0</td>\n", "      <td>11.0</td>\n", "      <td>4</td>\n", "      <td>1.038</td>\n", "      <td>0.190</td>\n", "      <td>1.171</td>\n", "      <td>20.683</td>\n", "      <td>0.831</td>\n", "      <td>97.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2550</th>\n", "      <td>300290.SZ</td>\n", "      <td>荣科科技</td>\n", "      <td>计算机</td>\n", "      <td>2025-04-07</td>\n", "      <td>2025-08-08</td>\n", "      <td>2025-07-07</td>\n", "      <td>182.561</td>\n", "      <td>2.0</td>\n", "      <td>5.316</td>\n", "      <td>-0.309</td>\n", "      <td>...</td>\n", "      <td>317.0</td>\n", "      <td>2.0</td>\n", "      <td>34.0</td>\n", "      <td>28</td>\n", "      <td>0.920</td>\n", "      <td>0.133</td>\n", "      <td>0.707</td>\n", "      <td>100.989</td>\n", "      <td>0.421</td>\n", "      <td>33.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2124</th>\n", "      <td>002463.SZ</td>\n", "      <td>沪电股份</td>\n", "      <td>电子</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-08-08</td>\n", "      <td>2025-08-08</td>\n", "      <td>64.894</td>\n", "      <td>1.0</td>\n", "      <td>7.390</td>\n", "      <td>-0.308</td>\n", "      <td>...</td>\n", "      <td>12.0</td>\n", "      <td>4.0</td>\n", "      <td>9.0</td>\n", "      <td>4</td>\n", "      <td>1.318</td>\n", "      <td>0.213</td>\n", "      <td>1.574</td>\n", "      <td>-18.320</td>\n", "      <td>1.546</td>\n", "      <td>88.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>630</th>\n", "      <td>601609.SH</td>\n", "      <td>金田股份</td>\n", "      <td>有色金属</td>\n", "      <td>2025-04-08</td>\n", "      <td>2025-08-01</td>\n", "      <td>2025-08-01</td>\n", "      <td>78.866</td>\n", "      <td>2.0</td>\n", "      <td>8.690</td>\n", "      <td>-0.133</td>\n", "      <td>...</td>\n", "      <td>18.0</td>\n", "      <td>1.0</td>\n", "      <td>16.0</td>\n", "      <td>9</td>\n", "      <td>0.290</td>\n", "      <td>0.029</td>\n", "      <td>0.252</td>\n", "      <td>1.238</td>\n", "      <td>0.269</td>\n", "      <td>104.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>602</th>\n", "      <td>600301.SH</td>\n", "      <td>华锡有色</td>\n", "      <td>有色金属</td>\n", "      <td>2025-01-03</td>\n", "      <td>2025-08-01</td>\n", "      <td>2025-07-17</td>\n", "      <td>73.430</td>\n", "      <td>1.0</td>\n", "      <td>6.161</td>\n", "      <td>-0.120</td>\n", "      <td>...</td>\n", "      <td>86.0</td>\n", "      <td>2.0</td>\n", "      <td>3.0</td>\n", "      <td>20</td>\n", "      <td>0.561</td>\n", "      <td>0.112</td>\n", "      <td>0.690</td>\n", "      <td>-9.553</td>\n", "      <td>0.590</td>\n", "      <td>122.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1839</th>\n", "      <td>301217.SZ</td>\n", "      <td>铜冠铜箔</td>\n", "      <td>电力设备</td>\n", "      <td>2025-04-07</td>\n", "      <td>2025-08-07</td>\n", "      <td>2025-07-16</td>\n", "      <td>68.090</td>\n", "      <td>1.0</td>\n", "      <td>29.811</td>\n", "      <td>0.011</td>\n", "      <td>...</td>\n", "      <td>12.0</td>\n", "      <td>9.0</td>\n", "      <td>5.0</td>\n", "      <td>21</td>\n", "      <td>1.874</td>\n", "      <td>0.053</td>\n", "      <td>1.580</td>\n", "      <td>6.883</td>\n", "      <td>1.580</td>\n", "      <td>47.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>933</th>\n", "      <td>688648.SH</td>\n", "      <td>中邮科技</td>\n", "      <td>机械设备</td>\n", "      <td>2025-04-07</td>\n", "      <td>2025-07-31</td>\n", "      <td>2025-07-31</td>\n", "      <td>138.901</td>\n", "      <td>3.0</td>\n", "      <td>17.285</td>\n", "      <td>0.116</td>\n", "      <td>...</td>\n", "      <td>73.0</td>\n", "      <td>16.0</td>\n", "      <td>19.0</td>\n", "      <td>10</td>\n", "      <td>3.951</td>\n", "      <td>0.246</td>\n", "      <td>4.252</td>\n", "      <td>75.802</td>\n", "      <td>2.058</td>\n", "      <td>116.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>632</th>\n", "      <td>601702.SH</td>\n", "      <td>华峰铝业</td>\n", "      <td>有色金属</td>\n", "      <td>2024-11-18</td>\n", "      <td>2025-07-31</td>\n", "      <td>2025-07-09</td>\n", "      <td>144.910</td>\n", "      <td>1.0</td>\n", "      <td>6.775</td>\n", "      <td>0.127</td>\n", "      <td>...</td>\n", "      <td>473.0</td>\n", "      <td>2.0</td>\n", "      <td>8.0</td>\n", "      <td>26</td>\n", "      <td>0.409</td>\n", "      <td>0.080</td>\n", "      <td>0.542</td>\n", "      <td>130.654</td>\n", "      <td>0.170</td>\n", "      <td>78.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>26 rows × 31 columns</p>\n", "</div>"], "text/plain": ["        ts_code   name industry Period_TurnDate Now_SecDate  \\\n", "1311  603158.SH   腾龙股份       汽车      2025-04-08  2025-07-31   \n", "2597  300561.SZ  *ST汇科      计算机      2025-07-07  2025-07-22   \n", "764   601002.SH   晋亿实业     机械设备      2025-04-08  2025-07-31   \n", "2366  603528.SH   多伦科技      计算机      2025-04-08  2025-08-08   \n", "2413  688291.<PERSON>    <PERSON>橙子      计算机      2025-04-07  2025-07-23   \n", "675   000962.SZ   东方钽业     有色金属      2024-12-25  2025-07-31   \n", "1561  603031.<PERSON>   安孚科技     电力设备      2025-01-10  2025-07-30   \n", "1451  300237.SZ   美晨科技       汽车      2025-04-07  2025-08-01   \n", "1687  002249.SZ   大洋电机     电力设备      2025-01-13  2025-07-31   \n", "1418  002454.SZ   松芝股份       汽车      2025-04-08  2025-07-31   \n", "1422  002516.SZ   旷达科技       汽车      2025-04-08  2025-07-31   \n", "800   603273.SH   天元智能     机械设备      2025-04-08  2025-08-12   \n", "2158  002938.SZ   鹏鼎控股       电子      2025-04-09  2025-08-06   \n", "1042  002833.SZ   弘亚数控     机械设备      2025-04-08  2025-07-31   \n", "1733  002879.SZ   长缆科技     电力设备      2025-01-03  2025-07-31   \n", "2681  603083.SH   剑桥科技       通信      2025-04-08  2025-08-08   \n", "623   601020.SH   华钰矿业     有色金属      2025-01-06  2025-08-12   \n", "812   603337.SH   杰克股份     机械设备      2025-04-08  2025-07-30   \n", "1917  603920.SH   世运电路       电子      2025-04-08  2025-08-08   \n", "2550  300290.SZ   荣科科技      计算机      2025-04-07  2025-08-08   \n", "2124  002463.SZ   沪电股份       电子      2025-04-08  2025-08-08   \n", "630   601609.<PERSON>   金田股份     有色金属      2025-04-08  2025-08-01   \n", "602   600301.SH   华锡有色     有色金属      2025-01-03  2025-08-01   \n", "1839  301217.SZ   铜冠铜箔     电力设备      2025-04-07  2025-08-07   \n", "933   688648.<PERSON>   <PERSON>邮科技     机械设备      2025-04-07  2025-07-31   \n", "632   601702.SH   华峰铝业     有色金属      2024-11-18  2025-07-31   \n", "\n", "     recent_bottom_date  Now_PRA_Rate  PostNowSec_PRA_MaxRate_BreachCount  \\\n", "1311         2025-07-31       135.091                                 2.0   \n", "2597         2025-07-14       100.022                                 3.0   \n", "764          2025-07-18        20.769                                 2.0   \n", "2366         2025-06-20       146.774                                 1.0   \n", "2413         2025-07-23           NaN                                 NaN   \n", "675          2025-06-20        74.043                                 1.0   \n", "1561         2025-07-30        30.036                                 1.0   \n", "1451         2025-06-20        89.783                                 2.0   \n", "1687         2025-06-20       195.574                                 3.0   \n", "1418         2025-07-31        29.495                                 1.0   \n", "1422         2025-07-31       160.126                                 2.0   \n", "800          2025-08-12         8.547                                 0.0   \n", "2158         2025-08-06        -5.799                                 1.0   \n", "1042         2025-07-31        76.149                                 3.0   \n", "1733         2025-07-15        56.935                                 2.0   \n", "2681         2025-08-08        78.393                                 2.0   \n", "623          2025-07-31       124.122                                 1.0   \n", "812          2025-07-30        46.318                                 2.0   \n", "1917         2025-08-08        72.676                                 2.0   \n", "2550         2025-07-07       182.561                                 2.0   \n", "2124         2025-08-08        64.894                                 1.0   \n", "630          2025-08-01        78.866                                 2.0   \n", "602          2025-07-17        73.430                                 1.0   \n", "1839         2025-07-16        68.090                                 1.0   \n", "933          2025-07-31       138.901                                 3.0   \n", "632          2025-07-09       144.910                                 1.0   \n", "\n", "      Turn2NowSec_PRA_BandDiff  PRA2Cls_Percentile_Diff  ...  \\\n", "1311                     7.742                   -1.139  ...   \n", "2597                     1.860                   -1.063  ...   \n", "764                      9.545                   -0.948  ...   \n", "2366                     7.625                   -0.901  ...   \n", "2413                    15.718                   -0.708  ...   \n", "675                      8.000                   -0.671  ...   \n", "1561                     9.397                   -0.667  ...   \n", "1451                    10.500                   -0.596  ...   \n", "1687                     9.071                   -0.588  ...   \n", "1418                     5.513                   -0.524  ...   \n", "1422                     6.304                   -0.516  ...   \n", "800                      7.264                   -0.461  ...   \n", "2158                     7.212                   -0.460  ...   \n", "1042                     8.333                   -0.432  ...   \n", "1733                    12.877                   -0.431  ...   \n", "2681                     9.212                   -0.412  ...   \n", "623                      9.119                   -0.408  ...   \n", "812                      6.243                   -0.390  ...   \n", "1917                     6.163                   -0.350  ...   \n", "2550                     5.316                   -0.309  ...   \n", "2124                     7.390                   -0.308  ...   \n", "630                      8.690                   -0.133  ...   \n", "602                      6.161                   -0.120  ...   \n", "1839                    29.811                    0.011  ...   \n", "933                     17.285                    0.116  ...   \n", "632                      6.775                    0.127  ...   \n", "\n", "      Recent3Day_PRA_MaxRate_CoverDays  Recent3Day_VRA_MaxRate_CoverDays  \\\n", "1311                              99.0                              98.0   \n", "2597                              10.0                               8.0   \n", "764                               25.0                               2.0   \n", "2366                              13.0                              12.0   \n", "2413                               NaN                               NaN   \n", "675                               61.0                               4.0   \n", "1561                               3.0                               9.0   \n", "1451                              35.0                              43.0   \n", "1687                              95.0                               3.0   \n", "1418                              21.0                             149.0   \n", "1422                              31.0                               1.0   \n", "800                                8.0                              12.0   \n", "2158                               1.0                               4.0   \n", "1042                              99.0                             209.0   \n", "1733                               3.0                              16.0   \n", "2681                              27.0                               2.0   \n", "623                               86.0                               2.0   \n", "812                                2.0                              21.0   \n", "1917                              15.0                               2.0   \n", "2550                             317.0                               2.0   \n", "2124                              12.0                               4.0   \n", "630                               18.0                               1.0   \n", "602                               86.0                               2.0   \n", "1839                              12.0                               9.0   \n", "933                               73.0                              16.0   \n", "632                              473.0                               2.0   \n", "\n", "      peak2recentbottom_lastdays  recentbottom2now_lastdays  \\\n", "1311                        13.0                         10   \n", "2597                         4.0                         23   \n", "764                          4.0                         19   \n", "2366                        15.0                         39   \n", "2413                        11.0                          7   \n", "675                         18.0                         39   \n", "1561                        18.0                         11   \n", "1451                         6.0                         39   \n", "1687                        28.0                         39   \n", "1418                        13.0                         10   \n", "1422                        22.0                         10   \n", "800                          9.0                          2   \n", "2158                        10.0                          6   \n", "1042                        13.0                         10   \n", "1733                         4.0                         22   \n", "2681                         9.0                          4   \n", "623                         14.0                         10   \n", "812                         13.0                         11   \n", "1917                        11.0                          4   \n", "2550                        34.0                         28   \n", "2124                         9.0                          4   \n", "630                         16.0                          9   \n", "602                          3.0                         20   \n", "1839                         5.0                         21   \n", "933                         19.0                         10   \n", "632                          8.0                         26   \n", "\n", "      PostNowSec_PGV_MaxRollAvg  Turn2NowSec_PRA_LowBand  \\\n", "1311                      0.129                    0.031   \n", "2597                      0.911                    0.150   \n", "764                       0.148                    0.022   \n", "2366                      0.153                    0.040   \n", "2413                      0.951                    0.174   \n", "675                       0.414                    0.092   \n", "1561                      0.766                    0.121   \n", "1451                      0.087                    0.014   \n", "1687                      0.180                    0.028   \n", "1418                      0.228                    0.039   \n", "1422                      0.166                    0.023   \n", "800                       0.448                    0.087   \n", "2158                      1.242                    0.226   \n", "1042                      0.487                    0.051   \n", "1733                      0.312                    0.057   \n", "2681                      1.588                    0.226   \n", "623                       0.389                    0.101   \n", "812                       1.691                    0.230   \n", "1917                      1.038                    0.190   \n", "2550                      0.920                    0.133   \n", "2124                      1.318                    0.213   \n", "630                       0.290                    0.029   \n", "602                       0.561                    0.112   \n", "1839                      1.874                    0.053   \n", "933                       3.951                    0.246   \n", "632                       0.409                    0.080   \n", "\n", "     Turn2NowSec_PRA_UpBand  PostNowSec2PreNowPeak_PRV2Price_RatioDiff  \\\n", "1311                  0.240                                    188.623   \n", "2597                  0.279                                    167.769   \n", "764                   0.210                                    181.283   \n", "2366                  0.305                                    -21.126   \n", "2413                  2.735                                    -66.126   \n", "675                   0.736                                    -47.751   \n", "1561                  1.137                                     93.041   \n", "1451                  0.147                                    -44.416   \n", "1687                  0.254                                     47.763   \n", "1418                  0.215                                    163.199   \n", "1422                  0.145                                      2.143   \n", "800                   0.632                                    -25.045   \n", "2158                  1.630                                    -35.829   \n", "1042                  0.425                                    532.785   \n", "1733                  0.734                                     10.725   \n", "2681                  2.082                                     20.175   \n", "623                   0.921                                    -38.523   \n", "812                   1.436                                     32.643   \n", "1917                  1.171                                     20.683   \n", "2550                  0.707                                    100.989   \n", "2124                  1.574                                    -18.320   \n", "630                   0.252                                      1.238   \n", "602                   0.690                                     -9.553   \n", "1839                  1.580                                      6.883   \n", "933                   4.252                                     75.802   \n", "632                   0.542                                    130.654   \n", "\n", "      PreNowPeak_PRV_Top3Mean  PullStart_Score  \n", "1311                    0.043            121.5  \n", "2597                    0.304             91.5  \n", "764                     0.050             83.5  \n", "2366                    0.178            103.0  \n", "2413                    2.953              NaN  \n", "675                     0.736             91.0  \n", "1561                    0.380             82.5  \n", "1451                    0.147             95.0  \n", "1687                    0.112            107.0  \n", "1418                    0.082             78.5  \n", "1422                    0.143             58.0  \n", "800                     0.572            131.0  \n", "2158                    1.791             97.5  \n", "1042                    0.075             86.5  \n", "1733                    0.257             98.5  \n", "2681                    1.270             87.0  \n", "623                     0.613            149.5  \n", "812                     1.115             88.5  \n", "1917                    0.831             97.0  \n", "2550                    0.421             33.0  \n", "2124                    1.546             88.0  \n", "630                     0.269            104.5  \n", "602                     0.590            122.5  \n", "1839                    1.580             47.0  \n", "933                     2.058            116.5  \n", "632                     0.170             78.5  \n", "\n", "[26 rows x 31 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list_sorted = pull_start_list[['ts_code', 'name', 'industry', 'Period_TurnDate', 'Now_SecDate', 'recent_bottom_date','Now_PRA_Rate','PostNowSec_PRA_MaxRate_BreachCount', 'Turn2NowSec_PRA_BandDiff', \n", "                                          'PRA2Cls_Percentile_Diff', 'PostNowSecMaxPRA_PostTurnRank5PRA_Diff',\n", "                                          'NowSec_PRA2Close_CoverDays_Diff', \n", "                                          'PRA2Cls_Percentile_Ratio', 'Recent3Day_MaxPGV_Percentile_PostTurn', 'NowSec_MaxPRA_Percentile_PostTurn', \n", "                                          'PostNowSec_MaxCls_Percentile_PostTurn', \n", "                                          'PostTurn_VGVRank5_NearNowDate',\n", "                                          'PostTurn_VGVRank5_NearNowDate_Days',\n", "                                          'NowSec_PGV_MaxRollAvg_UpCoverDays',\n", "                                          'NowSec_MaxClose_UpCoverDays',\n", "                                          'prepost_recentbottom_daysdiff',\n", "                                          'Recent3Day_PRA_MaxRate_CoverDays', 'Recent3Day_VRA_MaxRate_CoverDays',\n", "                                          'peak2recentbottom_lastdays',\n", "                                          'recentbottom2now_lastdays','PostNowSec_PGV_MaxRollAvg', 'Turn2NowSec_PRA_LowBand', 'Turn2NowSec_PRA_UpBand', \n", "                                          'PostNowSec2PreNowPeak_PRV2Price_RatioDiff', 'PreNowPeak_PRV_Top3Mean', 'PullStart_Score']\n", "                                         ].sort_values(by='PRA2Cls_Percentile_Diff', ascending=True)\n", "pull_start_list_sorted"]}, {"cell_type": "code", "execution_count": 5, "id": "136fec49", "metadata": {}, "outputs": [], "source": ["neg_threshold = -20\n", "pull_start_list_risestop = pull_start_list.query('industry==\"房地产\"').query('rise_stop_flag == 1')"]}, {"cell_type": "code", "execution_count": 3, "id": "7e56ef79", "metadata": {}, "outputs": [{"data": {"text/plain": ["['605006.SH',\n", " '600678.SH',\n", " '002491.SZ',\n", " '000989.SZ',\n", " '002598.SZ',\n", " '688159.SH',\n", " '002342.SZ',\n", " '603350.SH',\n", " '605588.SH',\n", " '002871.SZ',\n", " '603036.SH',\n", " '603222.SH',\n", " '688767.SH']"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list_sorted['ts_code'].values.tolist()"]}, {"cell_type": "code", "execution_count": 4, "id": "0d522835", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "PostSecStart_MaxValleyGapValue", "rawType": "float64", "type": "float"}], "ref": "b5dd6801-599d-4ccd-8625-59da9b954e73", "rows": [["416", "维康药业", "0.443"], ["148", "成都先导", "0.825"], ["1461", "长飞光纤", "1.016"], ["644", "应流股份", "0.929"], ["51", "汉商集团", "0.424"], ["1245", "建设工业", "1.426"], ["55", "人民同泰", "1.012"]], "shape": {"columns": 2, "rows": 7}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>PostSecStart_MaxValleyGapValue</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>416</th>\n", "      <td>维康药业</td>\n", "      <td>0.443</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148</th>\n", "      <td>成都先导</td>\n", "      <td>0.825</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1461</th>\n", "      <td>长飞光纤</td>\n", "      <td>1.016</td>\n", "    </tr>\n", "    <tr>\n", "      <th>644</th>\n", "      <td>应流股份</td>\n", "      <td>0.929</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>汉商集团</td>\n", "      <td>0.424</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1245</th>\n", "      <td>建设工业</td>\n", "      <td>1.426</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>人民同泰</td>\n", "      <td>1.012</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      name  PostSecStart_MaxValleyGapValue\n", "416   维康药业                           0.443\n", "148   成都先导                           0.825\n", "1461  长飞光纤                           1.016\n", "644   应流股份                           0.929\n", "51    汉商集团                           0.424\n", "1245  建设工业                           1.426\n", "55    人民同泰                           1.012"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list[['name', 'PostSecStart_MaxValleyGapValue']]"]}, {"cell_type": "code", "execution_count": null, "id": "132653dc", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "PostSecStart_MaxTurnover", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_AvgTurnover", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_MaxTurnover_Date", "rawType": "object", "type": "string"}], "ref": "ca8c0c6e-95d7-457d-9f7f-790110123adf", "rows": [["1060", "大智慧", "11.62", "1.73", "2025-06-25"], ["1064", "恒银科技", "18.936", "4.801", "2025-06-25"], ["1069", "顶点软件", "7.727", "1.435", "2025-06-25"], ["809", "好上好", "36.101", "13.776", "2025-06-06"], ["326", "野马电池", "6.695", "2.139", "2025-06-25"], ["319", "龙蟠科技", "16.073", "11.292", "2025-06-25"], ["6", "航发科技", "16.166", "6.793", "2025-05-30"], ["160", "市北高新", "3.732", "1.259", "2025-06-25"]], "shape": {"columns": 4, "rows": 8}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>PostSecStart_MaxTurnover</th>\n", "      <th>PostSecStart_AvgTurnover</th>\n", "      <th>PostSecStart_MaxTurnover_Date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1060</th>\n", "      <td>大智慧</td>\n", "      <td>11.620</td>\n", "      <td>1.730</td>\n", "      <td>2025-06-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1064</th>\n", "      <td>恒银科技</td>\n", "      <td>18.936</td>\n", "      <td>4.801</td>\n", "      <td>2025-06-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1069</th>\n", "      <td>顶点软件</td>\n", "      <td>7.727</td>\n", "      <td>1.435</td>\n", "      <td>2025-06-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>809</th>\n", "      <td>好上好</td>\n", "      <td>36.101</td>\n", "      <td>13.776</td>\n", "      <td>2025-06-06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>326</th>\n", "      <td>野马电池</td>\n", "      <td>6.695</td>\n", "      <td>2.139</td>\n", "      <td>2025-06-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>319</th>\n", "      <td>龙蟠科技</td>\n", "      <td>16.073</td>\n", "      <td>11.292</td>\n", "      <td>2025-06-25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>航发科技</td>\n", "      <td>16.166</td>\n", "      <td>6.793</td>\n", "      <td>2025-05-30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>160</th>\n", "      <td>市北高新</td>\n", "      <td>3.732</td>\n", "      <td>1.259</td>\n", "      <td>2025-06-25</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      name  PostSecStart_MaxTurnover  PostSecStart_AvgTurnover  \\\n", "1060   大智慧                    11.620                     1.730   \n", "1064  恒银科技                    18.936                     4.801   \n", "1069  顶点软件                     7.727                     1.435   \n", "809    好上好                    36.101                    13.776   \n", "326   野马电池                     6.695                     2.139   \n", "319   龙蟠科技                    16.073                    11.292   \n", "6     航发科技                    16.166                     6.793   \n", "160   市北高新                     3.732                     1.259   \n", "\n", "     PostSecStart_MaxTurnover_Date  \n", "1060                    2025-06-25  \n", "1064                    2025-06-25  \n", "1069                    2025-06-25  \n", "809                     2025-06-06  \n", "326                     2025-06-25  \n", "319                     2025-06-25  \n", "6                       2025-05-30  \n", "160                     2025-06-25  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list_risestop[['name', 'PostSecStart_MaxTurnover', 'PostSecStart_AvgTurnover', 'PostSecStart_MaxTurnover_Date']]"]}, {"cell_type": "code", "execution_count": null, "id": "3e7d3417", "metadata": {}, "outputs": [], "source": ["turn_col = ['PostSec_PGV_TurnP_Date',\n", "            'PostSec_PGV_TurnP_AbsChange',\n", "            'PostSec_PGV_TurnP_RelaChange',\n", "            'PostSec_PGV_PostTurnP_LastDays',    \n", "            'PostSec_TO_TurnP_Date',\n", "            'PostSec_TO_TurnP_AbsChange',\n", "            'PostSec_TO_TurnP_RelaChange',\n", "            'PostSec_TO_PostTurnP_LastDays']\n", "col_list = pull_start_list.columns.tolist()\n", "col_list = [col for col in col_list if col not in turn_col]\n", "pull_start_list_adj = pull_start_list[turn_col + col_list].copy()\n", "pull_start_list_adj['PostSec_PGV_TurnP_RelaChange_abs'] = pull_start_list_adj['PostSec_PGV_TurnP_RelaChange'].abs()\n", "pull_start_list_adj['PostSec_TO_TurnP_RelaChange_abs'] = pull_start_list_adj['PostSec_TO_TurnP_RelaChange'].abs()\n", "pull_start_list_adj = pull_start_list_adj.sort_values(\n", "    by='PostSec_PGV_TurnP_RelaChange_abs', ascending=False)\n"]}, {"cell_type": "code", "execution_count": null, "id": "cb047038", "metadata": {}, "outputs": [], "source": ["pull_start_list_adj2 = pull_start_list_adj.query('PostSec_PGV_TurnP_RelaChange<0')\n", "# pull_start_list_adj2['ts_code'].iloc[:20].values.tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "c8677c2d", "metadata": {}, "outputs": [], "source": ["industry_list = list(set(recent_industry_list + trend_industry_list))\n", "pull_start_list_core_temp = pull_start_list.query('industry in @industry_list & Now_PRA_Rate>=50 & PostNowSec_PRA_MaxRate_BreachCount==1 & Peak_Pres_Num>=2'\n", "                                             ).sort_values(by=['PostSecStart_PGV_Max2Mean_Ratio'], ascending=[False]).copy()\n", "if 'pullstart_sort_position' in pull_start_list_core_temp.columns:\n", "            cols = pull_start_list_core_temp.columns.tolist()\n", "            cols.remove('pullstart_sort_position')\n", "            pull_start_list_core_temp = pull_start_list_core_temp[['pullstart_sort_position'] + cols]\n"]}, {"cell_type": "code", "execution_count": null, "id": "9515f847", "metadata": {}, "outputs": [], "source": ["pull_start_list['Max2SecPeak_PRV_Diff_Rank'] = pull_start_list['PostSecStart_Max2SecPeak_PRV_Diff'].rank(ascending=False)\n", "pull_start_list['PostSecStart_PGV_Max2Mean_Ratio_Rank'] = pull_start_list['PostSecStart_PGV_Max2Mean_Ratio'].rank(ascending=False)\n", "pull_start_list['BreakPreNowPeak_Ratio_Rank'] = pull_start_list['BreakPreNowPeak_Ratio'].rank(ascending=False)\n", "pull_start_list['SecStart_PRA_Breach_Count_Rank'] = pull_start_list['SecStart_PRA_Breach_Count'].rank(ascending=False)\n", "pull_start_list['SecValley_GapRatio_Rank'] = pull_start_list['SecValley_GapRatio'].rank(ascending=False)\n", "pull_start_list['pullstart_sort_position_Rank'] = pull_start_list['pullstart_sort_position'].rank(ascending=True)\n", "pull_start_list['PostSecStart_AvgRatio_Rank'] = pull_start_list['PostSecStart_AvgRatio'].rank(ascending=True)\n", "pull_start_list['PullStart_Score'] = pull_start_list['Max2SecPeak_PRV_Diff_Rank'] + pull_start_list['PostSecStart_PGV_Max2Mean_Ratio_Rank'] + \\\n", "                                          pull_start_list['BreakPreNowPeak_Ratio_Rank'] + pull_start_list['SecStart_PRA_Breach_Count_Rank'] + \\\n", "                                          pull_start_list['SecValley_GapRatio_Rank'] + pull_start_list['pullstart_sort_position_Rank'] + \\\n", "                                          pull_start_list['PostSecStart_AvgRatio_Rank']\n", "pull_start_list2 = pull_start_list.sort_values(by=['industry', 'PullStart_Score'], ascending=[True, True])\n", "pull_start_list = pull_start_list.drop(columns=['Max2SecPeak_PRV_Diff_Rank', 'PostSecStart_PGV_Max2Mean_Ratio_Rank', 'BreakPreNowPeak_Ratio_Rank', 'SecStart_PRA_Breach_Count_Rank', 'SecValley_GapRatio_Rank', 'pullstart_sort_position_Rank', 'PostSecStart_AvgRatio_Rank'])"]}, {"cell_type": "code", "execution_count": null, "id": "fdd35b45", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"['华海诚科', '隆扬电子', '佰维存储', '美迪凯', '芯瑞达', '方邦股份', '智动力', '盛美上海', '*ST华微', '骏亚科技']\""]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["indus = \"电子\"\n", "str(pull_start_list.query('industry==@indus & BreakPreNowPeak_Ratio<3')['name'].iloc[:10].tolist())"]}, {"cell_type": "code", "execution_count": null, "id": "bc30ef99", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"['中芯国际', '工业富联', '海光信息', '寒武纪-U', '立讯精密', '北方华创', '韦尔股份', '京东方A', '中微公司', '蓝思科技']\""]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["str(result_df_head.query('industry==@indus')['name'].iloc[:10].tolist())"]}, {"cell_type": "code", "execution_count": 8, "id": "903f2930", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "now_overpre1day", "rawType": "int64", "type": "integer"}, {"name": "recentbottom_downcoverdays", "rawType": "int64", "type": "integer"}, {"name": "postcumret_consecutive_over7num", "rawType": "int64", "type": "integer"}, {"name": "nowsec_recent_diff", "rawType": "int64", "type": "integer"}, {"name": "recent2bottom_days", "rawType": "int64", "type": "integer"}, {"name": "postnowsec_risestop_num", "rawType": "int64", "type": "integer"}, {"name": "Now_SecDate", "rawType": "object", "type": "string"}, {"name": "recent_bottom_date", "rawType": "object", "type": "string"}, {"name": "PostSecStart_PGV_Max2Mean_Ratio", "rawType": "float64", "type": "float"}, {"name": "Peak_Pres_Num", "rawType": "float64", "type": "float"}, {"name": "postnowsec_risestop_num", "rawType": "int64", "type": "integer"}, {"name": "recentbottom2now_lastdays", "rawType": "int64", "type": "integer"}, {"name": "PostNowSecMaxPRA_PostTurnRank5PRA_Diff", "rawType": "float64", "type": "float"}], "ref": "542f7f1c-0343-450f-b9c8-a579843b1706", "rows": [["1864", "1", "12", "1", "6", "6", "1", "2025-07-07", "2025-07-07", "3.788", "2.0", "1", "9", "0.607"]], "shape": {"columns": 13, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>now_overpre1day</th>\n", "      <th>recentbottom_downcoverdays</th>\n", "      <th>postcumret_consecutive_over7num</th>\n", "      <th>nowsec_recent_diff</th>\n", "      <th>recent2bottom_days</th>\n", "      <th>postnowsec_risestop_num</th>\n", "      <th>Now_SecDate</th>\n", "      <th>recent_bottom_date</th>\n", "      <th>PostSecStart_PGV_Max2Mean_Ratio</th>\n", "      <th>Peak_Pres_Num</th>\n", "      <th>postnowsec_risestop_num</th>\n", "      <th>recentbottom2now_lastdays</th>\n", "      <th>PostNowSecMaxPRA_PostTurnRank5PRA_Diff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1864</th>\n", "      <td>1</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>2025-07-07</td>\n", "      <td>2025-07-07</td>\n", "      <td>3.788</td>\n", "      <td>2.0</td>\n", "      <td>1</td>\n", "      <td>9</td>\n", "      <td>0.607</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      now_overpre1day  recentbottom_downcoverdays  \\\n", "1864                1                          12   \n", "\n", "      postcumret_consecutive_over7num  nowsec_recent_diff  recent2bottom_days  \\\n", "1864                                1                   6                   6   \n", "\n", "      postnowsec_risestop_num Now_SecDate recent_bottom_date  \\\n", "1864                        1  2025-07-07         2025-07-07   \n", "\n", "      PostSecStart_PGV_Max2Mean_Ratio  Peak_Pres_Num  postnowsec_risestop_num  \\\n", "1864                            3.788            2.0                        1   \n", "\n", "      recentbottom2now_lastdays  PostNowSecMaxPRA_PostTurnRank5PRA_Diff  \n", "1864                          9                                   0.607  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["stk_temp = result_df_head.query('ts_code==\"601869.SH\"')\n", "stk_temp[['now_overpre1day',\n", "            'recentbottom_downcoverdays',\n", "            'postcumret_consecutive_over7num',\n", "            'nowsec_recent_diff', 'recent2bottom_days',\n", "            'postnowsec_risestop_num', 'Now_SecDate', 'recent_bottom_date','PostSecStart_PGV_Max2Mean_Ratio', 'Peak_Pres_Num', 'postnowsec_risestop_num', 'recentbottom2now_lastdays', 'PostNowSecMaxPRA_PostTurnRank5PRA_Diff']]"]}, {"cell_type": "code", "execution_count": 1, "id": "11ea2de4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:381: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, bottom_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:565: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Cumret_List = pd.concat([Cumret_List, cumret_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:636: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Rise_List = pd.concat([Rise_List, rise_temp_df], ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["行业: 综合, 回撤转折日期: 2025-03-25, 转折日期: 2024-12-16, 转折回升幅度: 0.89, 下行天数: 124, 下行幅度: -39.86\n", "行业: 医药生物, 回撤转折日期: 2025-03-24, 转折日期: 2025-03-04, 转折回升幅度: 1.89, 下行天数: 272, 下行幅度: -28.57\n", "行业: 交通运输, 回撤转折日期: 2025-03-14, 转折日期: 2025-02-28, 转折回升幅度: 2.14, 下行天数: 131, 下行幅度: -16.17\n", "行业: 煤炭, 回撤转折日期: 2025-03-06, 转折日期: 2025-03-26, 转折回升幅度: 6.19, 下行天数: 251, 下行幅度: -47.55\n", "行业: 有色金属, 回撤转折日期: 2025-02-27, 转折日期: 2025-01-13, 转折回升幅度: 7.45, 下行天数: 177, 下行幅度: -25.00\n", "行业: 银行, 回撤转折日期: 2025-02-26, 转折日期: 2025-01-10, 转折回升幅度: 5.25, 下行天数: 48, 下行幅度: -28.18\n", "行业: 家用电器, 回撤转折日期: 2025-02-21, 转折日期: 2025-01-09, 转折回升幅度: 6.12, 下行天数: 29, 下行幅度: -15.98\n", "行业: 食品饮料, 回撤转折日期: 2025-02-11, 转折日期: 2025-02-28, 转折回升幅度: 5.39, 下行天数: 471, 下行幅度: -28.02\n", "行业: 社会服务, 回撤转折日期: 2025-01-10, 转折日期: 2024-12-16, 转折回升幅度: 6.96, 下行天数: 311, 下行幅度: -34.11\n", "行业: 环保, 回撤转折日期: 2025-01-10, 转折日期: 2024-12-16, 转折回升幅度: 6.13, 下行天数: 19, 下行幅度: -22.88\n", "行业: 基础化工, 回撤转折日期: 2024-12-25, 转折日期: 2024-12-11, 转折回升幅度: 5.36, 下行天数: 395, 下行幅度: -23.12\n", "行业排序结果:\n", "1. 基础化工: 66.35, 涨停数/总数:24/394\n", "2. 环保: 30.31, 涨停数/总数:0/133\n", "3. 有色金属: 14.47, 涨停数/总数:1/131\n", "4. 社会服务: 13.30, 涨停数/总数:0/75\n", "5. 家用电器: 12.90, 涨停数/总数:2/95\n", "6. 医药生物: 11.24, 涨停数/总数:4/469\n", "7. 食品饮料: 8.50, 涨停数/总数:2/123\n", "8. 交通运输: 7.27, 涨停数/总数:0/123\n", "9. 煤炭: 6.95, 涨停数/总数:0/37\n", "10. 银行: 2.49, 涨停数/总数:0/42\n", "11. 综合: 1.17, 涨停数/总数:0/17\n", "未存储筛选股票数据\n", "PullStart股票行业分布：\n", "           count\n", "industry       \n", "基础化工         11\n", "医药生物          2\n", "家用电器          1\n", "食品饮料          1\n", "环保            0\n", "有色金属          0\n", "社会服务          0\n", "交通运输          0\n", "煤炭            0\n", "银行            0\n", "综合            0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2176: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  pull_start_list['pullstart_sort_position'] = pull_start_list.groupby('industry').cumcount() + 1\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2399: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  pull_indus_count = pull_start_list[['ts_code', 'industry']].groupby('industry').count()\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2425: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  by=['industry', 'stk_checkperiod_sumratio'], ascending=[True, False]).groupby('industry').head(20)\n"]}], "source": ["from function_ai.StkPick_ModelFunc import track_pullstart_stocks\n", "from function_ai.Func_Base import get_trade_date\n", "trade_date = ['2025-03-27']\n", "for date in trade_date:\n", "    # industry_list = ['房地产', '纺织服饰', '建筑装饰', '建筑材料', '轻工制造', '家用电器', '交通运输', '公用事业', \n", "                    #  '煤炭', '农林牧渔']\n", "    # trend_industry_list = ['有色金属', '房地产', '建筑材料', '纺织服饰', '国防军工', '电力设备', '医药生物', '钢铁', '环保', '基础化工']\n", "    # recent_industry_list = ['房地产', '商贸零售']\n", "    trend_industry_list = None\n", "    recent_industry_list = None\n", "    # industry_list = None\n", "    # '2025-03-24', '2025-03-10', '2025-02-28', '2025-02-18', \n", "    end_date, trend_startdate = date, '2025-01-10'\n", "    recent_turndate = ['2025-01-24', '2025-02-18', '2025-02-28', '2025-03-13', '2025-03-21']\n", "    result_df_head, result_df_turn, pull_start_list, pull_start_list_core, result_df_recentindus = track_pullstart_stocks(\n", "        end_date=end_date, trend_startdate=trend_startdate,\n", "        recent_turndate=recent_turndate,\n", "        industry_list=trend_industry_list,\n", "        limit_num=80, store_mode=False,\n", "        recent_indus=recent_industry_list,\n", "        recentindus_calstartdate='2025-01-10',\n", "        rise_stop_signal=True)"]}, {"cell_type": "code", "execution_count": 2, "id": "620f3218", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "ts_code", "rawType": "object", "type": "string"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "industry", "rawType": "category", "type": "unknown"}, {"name": "Period_TurnDate", "rawType": "object", "type": "string"}, {"name": "Now_SecDate", "rawType": "object", "type": "string"}, {"name": "recent_bottom_date", "rawType": "object", "type": "string"}, {"name": "Now_PRA_Rate", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_PRA_MaxRate_BreachCount", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_BandDiff", "rawType": "float64", "type": "float"}, {"name": "PRA2Cls_Percentile_Diff", "rawType": "float64", "type": "float"}, {"name": "NowSec_PRA2Close_CoverDays_Diff", "rawType": "float64", "type": "float"}, {"name": "PostNowSecMaxPRA_PostTurnRank5PRA_Diff", "rawType": "float64", "type": "float"}, {"name": "PRA2Cls_Percentile_Ratio", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_MaxPGV_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "NowSec_MaxPRA_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "PostNowSec_MaxCls_Percentile_PostTurn", "rawType": "float64", "type": "float"}, {"name": "PostTurn_VGVRank5_NearNowDate", "rawType": "object", "type": "string"}, {"name": "PostTurn_VGVRank5_NearNowDate_Days", "rawType": "float64", "type": "float"}, {"name": "NowSec_PGV_MaxRollAvg_UpCoverDays", "rawType": "float64", "type": "float"}, {"name": "NowSec_MaxClose_UpCoverDays", "rawType": "float64", "type": "float"}, {"name": "prepost_recentbottom_daysdiff", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_PRA_MaxRate_CoverDays", "rawType": "float64", "type": "float"}, {"name": "Recent3Day_VRA_MaxRate_CoverDays", "rawType": "float64", "type": "float"}, {"name": "peak2recentbottom_lastdays", "rawType": "float64", "type": "float"}, {"name": "recentbottom2now_lastdays", "rawType": "int64", "type": "integer"}, {"name": "PostNowSec_PGV_MaxRollAvg", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_LowBand", "rawType": "float64", "type": "float"}, {"name": "Turn2NowSec_PRA_UpBand", "rawType": "float64", "type": "float"}, {"name": "PostNowSec2PreNowPeak_PRV2Price_RatioDiff", "rawType": "float64", "type": "float"}, {"name": "PreNowPeak_PRV_Top3Mean", "rawType": "float64", "type": "float"}, {"name": "PullStart_Score", "rawType": "float64", "type": "float"}], "ref": "da647c56-89ed-4da7-a362-486f45d375eb", "rows": [["432", "002773.SZ", "康弘药业", "医药生物", "2024-09-23", "2025-03-24", "2025-02-19", "195.386", "1.0", "6.726", "-0.674", "-947.0", "0.68", "0.412", "0.862", "0.472", "1.146", "2025-02-06", "35.0", "9.0", "956.0", "21.0", "99.0", "3.0", "6.0", "27", "0.41", "0.106", "0.713", "-23.636", "0.5", "44.5"], ["794", "002054.SZ", "德美化工", "基础化工", "2024-09-18", "2025-02-28", "2025-01-03", "59.263", "1.0", "11.559", "-0.605", "-58.0", "0.622", "0.317", "0.317", "0.281", "0.886", "2024-11-26", "80.0", "22.0", "80.0", "27.0", "24.0", "79.0", "27.0", "54", "0.151", "0.034", "0.393", "-56.251", "0.387", "44.0"], ["962", "301118.SZ", "恒光股份", "基础化工", "2024-09-18", "2025-03-24", "2025-03-24", "54.779", "2.0", "10.262", "-0.587", "-36.0", "0.682", "0.402", "0.317", "0.394", "0.981", "2025-03-26", "1.0", "29.0", "65.0", "-24.0", "29.0", "3.0", "28.0", "4", "0.64", "0.122", "1.252", "-22.598", "0.694", "65.0"], ["659", "603125.SH", "常青科技", "基础化工", "2024-09-23", "2025-03-24", "2025-03-24", "196.618", "2.0", "7.506", "-0.459", "-130.0", "1.001", "0.609", "1.007", "0.715", "1.174", "2025-02-19", "26.0", "72.0", "202.0", "-14.0", "99.0", "1.0", "18.0", "4", "0.544", "0.089", "0.668", "101.37", "0.258", "46.0"], ["782", "001217.SZ", "华尔泰", "基础化工", "2024-09-18", "2025-03-21", "2025-03-21", "-22.989", "2.0", "13.299", "-0.248", "-8.0", "0.567", "0.623", "0.403", "0.41", "0.658", "2025-03-27", "0.0", "43.0", "51.0", "-9.0", "12.0", "2.0", "14.0", "5", "0.435", "0.067", "0.891", "16.623", "0.333", "69.0"], ["373", "000963.SZ", "华东医药", "医药生物", "2024-09-13", "2025-03-21", "2025-03-21", "187.695", "1.0", "5.862", "-0.224", "8.0", "0.961", "0.735", "1.137", "0.622", "0.846", "2025-03-27", "0.0", "76.0", "68.0", "-11.0", "99.0", "1.0", "16.0", "5", "0.645", "0.145", "0.85", "23.143", "0.506", "57.5"], ["842", "002549.SZ", "凯美特气", "基础化工", "2024-08-27", "2025-03-24", "2025-03-24", "126.538", "1.0", "11.158", "-0.191", "27.0", "0.522", "0.646", "0.576", "0.348", "0.539", "2025-03-20", "5.0", "89.0", "62.0", "-14.0", "37.0", "1.0", "18.0", "4", "0.177", "0.038", "0.424", "109.023", "0.083", "78.0"], ["601", "600227.SH", "赤天化", "基础化工", "2024-08-23", "2025-03-24", "2025-03-24", "65.962", "2.0", "8.118", "-0.12", "0.0", "0.756", "0.802", "0.486", "0.486", "0.606", "2025-02-20", "25.0", "60.0", "60.0", "-26.0", "28.0", "1.0", "30.0", "4", "0.086", "0.017", "0.138", "26.614", "0.066", "84.0"], ["966", "301190.SZ", "善水科技", "基础化工", "2024-09-18", "2025-02-28", "2025-02-28", "162.5", "4.0", "7.515", "-0.098", "-111.0", "1.569", "0.957", "2.342", "2.185", "2.283", "2025-02-19", "26.0", "493.0", "604.0", "18.0", "122.0", "1.0", "2.0", "20", "1.101", "0.068", "0.511", "198.608", "0.329", "25.5"], ["989", "600619.SH", "海立股份", "家用电器", "2024-08-27", "2025-03-24", "2025-02-28", "52.26", "2.0", "46.5", "-0.093", "7.0", "0.667", "0.834", "0.437", "0.466", "0.559", "2024-11-05", "95.0", "16.0", "9.0", "8.0", "16.0", "1.0", "12.0", "20", "0.62", "0.026", "1.209", "8.328", "0.599", "71.5"], ["1601", "002329.SZ", "皇氏集团", "食品饮料", "2024-09-18", "2025-03-24", "2025-03-07", "42.333", "0.0", "9.333", "-0.046", "-2.0", "0.635", "0.922", "0.615", "0.54", "0.586", "2025-03-17", "8.0", "5.0", "7.0", "9.0", "7.0", "5.0", "6.0", "15", "0.151", "0.027", "0.252", "-14.438", "0.177", "60.5"], ["783", "001218.SZ", "丽臣实业", "基础化工", "2024-09-18", "2025-03-24", "2025-02-18", "154.872", "1.0", "6.855", "-0.012", "62.0", "1.279", "0.989", "1.615", "1.099", "1.111", "2024-12-11", "69.0", "267.0", "205.0", "23.0", "99.0", "1.0", "5.0", "28", "0.497", "0.062", "0.425", "299.739", "0.122", "37.0"], ["771", "000859.SZ", "国风新材", "基础化工", "2024-09-13", "2025-03-24", "2025-03-24", "149.278", "1.0", "15.5", "-0.008", "-1.0", "0.598", "0.985", "0.609", "0.543", "0.551", "2024-12-18", "64.0", "60.0", "61.0", "-17.0", "62.0", "1.0", "21.0", "4", "0.155", "0.018", "0.279", "56.374", "0.098", "78.0"], ["629", "600746.SH", "江苏索普", "基础化工", "2024-06-24", "2025-02-25", "2025-02-25", "89.465", "2.0", "4.375", "0.185", "-292.0", "1.711", "1.124", "1.458", "1.682", "1.497", "2025-03-11", "12.0", "215.0", "507.0", "13.0", "99.0", "3.0", "10.0", "23", "0.223", "0.032", "0.14", "76.444", "0.116", "41.0"], ["704", "603938.SH", "三孚股份", "基础化工", "2024-08-23", "2025-03-24", "2025-02-28", "84.165", "2.0", "4.169", "0.743", "178.0", "1.692", "1.803", "1.711", "1.668", "0.925", "2025-02-26", "21.0", "269.0", "91.0", "14.0", "475.0", "1.0", "6.0", "20", "0.387", "0.059", "0.246", "218.454", "0.118", "38.5"]], "shape": {"columns": 31, "rows": 15}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>name</th>\n", "      <th>industry</th>\n", "      <th>Period_TurnDate</th>\n", "      <th>Now_SecDate</th>\n", "      <th>recent_bottom_date</th>\n", "      <th>Now_PRA_Rate</th>\n", "      <th>PostNowSec_PRA_MaxRate_BreachCount</th>\n", "      <th>Turn2NowSec_PRA_BandDiff</th>\n", "      <th>PRA2Cls_Percentile_Diff</th>\n", "      <th>...</th>\n", "      <th>Recent3Day_PRA_MaxRate_CoverDays</th>\n", "      <th>Recent3Day_VRA_MaxRate_CoverDays</th>\n", "      <th>peak2recentbottom_lastdays</th>\n", "      <th>recentbottom2now_lastdays</th>\n", "      <th>PostNowSec_PGV_MaxRollAvg</th>\n", "      <th>Turn2NowSec_PRA_LowBand</th>\n", "      <th>Turn2NowSec_PRA_UpBand</th>\n", "      <th>PostNowSec2PreNowPeak_PRV2Price_RatioDiff</th>\n", "      <th>PreNowPeak_PRV_Top3Mean</th>\n", "      <th>PullStart_Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>432</th>\n", "      <td>002773.SZ</td>\n", "      <td>康弘药业</td>\n", "      <td>医药生物</td>\n", "      <td>2024-09-23</td>\n", "      <td>2025-03-24</td>\n", "      <td>2025-02-19</td>\n", "      <td>195.386</td>\n", "      <td>1.0</td>\n", "      <td>6.726</td>\n", "      <td>-0.674</td>\n", "      <td>...</td>\n", "      <td>99.0</td>\n", "      <td>3.0</td>\n", "      <td>6.0</td>\n", "      <td>27</td>\n", "      <td>0.410</td>\n", "      <td>0.106</td>\n", "      <td>0.713</td>\n", "      <td>-23.636</td>\n", "      <td>0.500</td>\n", "      <td>44.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>794</th>\n", "      <td>002054.SZ</td>\n", "      <td>德美化工</td>\n", "      <td>基础化工</td>\n", "      <td>2024-09-18</td>\n", "      <td>2025-02-28</td>\n", "      <td>2025-01-03</td>\n", "      <td>59.263</td>\n", "      <td>1.0</td>\n", "      <td>11.559</td>\n", "      <td>-0.605</td>\n", "      <td>...</td>\n", "      <td>24.0</td>\n", "      <td>79.0</td>\n", "      <td>27.0</td>\n", "      <td>54</td>\n", "      <td>0.151</td>\n", "      <td>0.034</td>\n", "      <td>0.393</td>\n", "      <td>-56.251</td>\n", "      <td>0.387</td>\n", "      <td>44.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>962</th>\n", "      <td>301118.SZ</td>\n", "      <td>恒光股份</td>\n", "      <td>基础化工</td>\n", "      <td>2024-09-18</td>\n", "      <td>2025-03-24</td>\n", "      <td>2025-03-24</td>\n", "      <td>54.779</td>\n", "      <td>2.0</td>\n", "      <td>10.262</td>\n", "      <td>-0.587</td>\n", "      <td>...</td>\n", "      <td>29.0</td>\n", "      <td>3.0</td>\n", "      <td>28.0</td>\n", "      <td>4</td>\n", "      <td>0.640</td>\n", "      <td>0.122</td>\n", "      <td>1.252</td>\n", "      <td>-22.598</td>\n", "      <td>0.694</td>\n", "      <td>65.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>659</th>\n", "      <td>603125.SH</td>\n", "      <td>常青科技</td>\n", "      <td>基础化工</td>\n", "      <td>2024-09-23</td>\n", "      <td>2025-03-24</td>\n", "      <td>2025-03-24</td>\n", "      <td>196.618</td>\n", "      <td>2.0</td>\n", "      <td>7.506</td>\n", "      <td>-0.459</td>\n", "      <td>...</td>\n", "      <td>99.0</td>\n", "      <td>1.0</td>\n", "      <td>18.0</td>\n", "      <td>4</td>\n", "      <td>0.544</td>\n", "      <td>0.089</td>\n", "      <td>0.668</td>\n", "      <td>101.370</td>\n", "      <td>0.258</td>\n", "      <td>46.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>782</th>\n", "      <td>001217.SZ</td>\n", "      <td>华尔泰</td>\n", "      <td>基础化工</td>\n", "      <td>2024-09-18</td>\n", "      <td>2025-03-21</td>\n", "      <td>2025-03-21</td>\n", "      <td>-22.989</td>\n", "      <td>2.0</td>\n", "      <td>13.299</td>\n", "      <td>-0.248</td>\n", "      <td>...</td>\n", "      <td>12.0</td>\n", "      <td>2.0</td>\n", "      <td>14.0</td>\n", "      <td>5</td>\n", "      <td>0.435</td>\n", "      <td>0.067</td>\n", "      <td>0.891</td>\n", "      <td>16.623</td>\n", "      <td>0.333</td>\n", "      <td>69.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>373</th>\n", "      <td>000963.SZ</td>\n", "      <td>华东医药</td>\n", "      <td>医药生物</td>\n", "      <td>2024-09-13</td>\n", "      <td>2025-03-21</td>\n", "      <td>2025-03-21</td>\n", "      <td>187.695</td>\n", "      <td>1.0</td>\n", "      <td>5.862</td>\n", "      <td>-0.224</td>\n", "      <td>...</td>\n", "      <td>99.0</td>\n", "      <td>1.0</td>\n", "      <td>16.0</td>\n", "      <td>5</td>\n", "      <td>0.645</td>\n", "      <td>0.145</td>\n", "      <td>0.850</td>\n", "      <td>23.143</td>\n", "      <td>0.506</td>\n", "      <td>57.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>842</th>\n", "      <td>002549.SZ</td>\n", "      <td>凯美特气</td>\n", "      <td>基础化工</td>\n", "      <td>2024-08-27</td>\n", "      <td>2025-03-24</td>\n", "      <td>2025-03-24</td>\n", "      <td>126.538</td>\n", "      <td>1.0</td>\n", "      <td>11.158</td>\n", "      <td>-0.191</td>\n", "      <td>...</td>\n", "      <td>37.0</td>\n", "      <td>1.0</td>\n", "      <td>18.0</td>\n", "      <td>4</td>\n", "      <td>0.177</td>\n", "      <td>0.038</td>\n", "      <td>0.424</td>\n", "      <td>109.023</td>\n", "      <td>0.083</td>\n", "      <td>78.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>601</th>\n", "      <td>600227.SH</td>\n", "      <td>赤天化</td>\n", "      <td>基础化工</td>\n", "      <td>2024-08-23</td>\n", "      <td>2025-03-24</td>\n", "      <td>2025-03-24</td>\n", "      <td>65.962</td>\n", "      <td>2.0</td>\n", "      <td>8.118</td>\n", "      <td>-0.120</td>\n", "      <td>...</td>\n", "      <td>28.0</td>\n", "      <td>1.0</td>\n", "      <td>30.0</td>\n", "      <td>4</td>\n", "      <td>0.086</td>\n", "      <td>0.017</td>\n", "      <td>0.138</td>\n", "      <td>26.614</td>\n", "      <td>0.066</td>\n", "      <td>84.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>966</th>\n", "      <td>301190.SZ</td>\n", "      <td>善水科技</td>\n", "      <td>基础化工</td>\n", "      <td>2024-09-18</td>\n", "      <td>2025-02-28</td>\n", "      <td>2025-02-28</td>\n", "      <td>162.500</td>\n", "      <td>4.0</td>\n", "      <td>7.515</td>\n", "      <td>-0.098</td>\n", "      <td>...</td>\n", "      <td>122.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>20</td>\n", "      <td>1.101</td>\n", "      <td>0.068</td>\n", "      <td>0.511</td>\n", "      <td>198.608</td>\n", "      <td>0.329</td>\n", "      <td>25.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>989</th>\n", "      <td>600619.SH</td>\n", "      <td>海立股份</td>\n", "      <td>家用电器</td>\n", "      <td>2024-08-27</td>\n", "      <td>2025-03-24</td>\n", "      <td>2025-02-28</td>\n", "      <td>52.260</td>\n", "      <td>2.0</td>\n", "      <td>46.500</td>\n", "      <td>-0.093</td>\n", "      <td>...</td>\n", "      <td>16.0</td>\n", "      <td>1.0</td>\n", "      <td>12.0</td>\n", "      <td>20</td>\n", "      <td>0.620</td>\n", "      <td>0.026</td>\n", "      <td>1.209</td>\n", "      <td>8.328</td>\n", "      <td>0.599</td>\n", "      <td>71.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1601</th>\n", "      <td>002329.SZ</td>\n", "      <td>皇氏集团</td>\n", "      <td>食品饮料</td>\n", "      <td>2024-09-18</td>\n", "      <td>2025-03-24</td>\n", "      <td>2025-03-07</td>\n", "      <td>42.333</td>\n", "      <td>0.0</td>\n", "      <td>9.333</td>\n", "      <td>-0.046</td>\n", "      <td>...</td>\n", "      <td>7.0</td>\n", "      <td>5.0</td>\n", "      <td>6.0</td>\n", "      <td>15</td>\n", "      <td>0.151</td>\n", "      <td>0.027</td>\n", "      <td>0.252</td>\n", "      <td>-14.438</td>\n", "      <td>0.177</td>\n", "      <td>60.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>783</th>\n", "      <td>001218.SZ</td>\n", "      <td>丽臣实业</td>\n", "      <td>基础化工</td>\n", "      <td>2024-09-18</td>\n", "      <td>2025-03-24</td>\n", "      <td>2025-02-18</td>\n", "      <td>154.872</td>\n", "      <td>1.0</td>\n", "      <td>6.855</td>\n", "      <td>-0.012</td>\n", "      <td>...</td>\n", "      <td>99.0</td>\n", "      <td>1.0</td>\n", "      <td>5.0</td>\n", "      <td>28</td>\n", "      <td>0.497</td>\n", "      <td>0.062</td>\n", "      <td>0.425</td>\n", "      <td>299.739</td>\n", "      <td>0.122</td>\n", "      <td>37.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>771</th>\n", "      <td>000859.SZ</td>\n", "      <td>国风新材</td>\n", "      <td>基础化工</td>\n", "      <td>2024-09-13</td>\n", "      <td>2025-03-24</td>\n", "      <td>2025-03-24</td>\n", "      <td>149.278</td>\n", "      <td>1.0</td>\n", "      <td>15.500</td>\n", "      <td>-0.008</td>\n", "      <td>...</td>\n", "      <td>62.0</td>\n", "      <td>1.0</td>\n", "      <td>21.0</td>\n", "      <td>4</td>\n", "      <td>0.155</td>\n", "      <td>0.018</td>\n", "      <td>0.279</td>\n", "      <td>56.374</td>\n", "      <td>0.098</td>\n", "      <td>78.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>629</th>\n", "      <td>600746.SH</td>\n", "      <td>江苏索普</td>\n", "      <td>基础化工</td>\n", "      <td>2024-06-24</td>\n", "      <td>2025-02-25</td>\n", "      <td>2025-02-25</td>\n", "      <td>89.465</td>\n", "      <td>2.0</td>\n", "      <td>4.375</td>\n", "      <td>0.185</td>\n", "      <td>...</td>\n", "      <td>99.0</td>\n", "      <td>3.0</td>\n", "      <td>10.0</td>\n", "      <td>23</td>\n", "      <td>0.223</td>\n", "      <td>0.032</td>\n", "      <td>0.140</td>\n", "      <td>76.444</td>\n", "      <td>0.116</td>\n", "      <td>41.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>704</th>\n", "      <td>603938.SH</td>\n", "      <td>三孚股份</td>\n", "      <td>基础化工</td>\n", "      <td>2024-08-23</td>\n", "      <td>2025-03-24</td>\n", "      <td>2025-02-28</td>\n", "      <td>84.165</td>\n", "      <td>2.0</td>\n", "      <td>4.169</td>\n", "      <td>0.743</td>\n", "      <td>...</td>\n", "      <td>475.0</td>\n", "      <td>1.0</td>\n", "      <td>6.0</td>\n", "      <td>20</td>\n", "      <td>0.387</td>\n", "      <td>0.059</td>\n", "      <td>0.246</td>\n", "      <td>218.454</td>\n", "      <td>0.118</td>\n", "      <td>38.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>15 rows × 31 columns</p>\n", "</div>"], "text/plain": ["        ts_code  name industry Period_TurnDate Now_SecDate recent_bottom_date  \\\n", "432   002773.SZ  康弘药业     医药生物      2024-09-23  2025-03-24         2025-02-19   \n", "794   002054.SZ  德美化工     基础化工      2024-09-18  2025-02-28         2025-01-03   \n", "962   301118.SZ  恒光股份     基础化工      2024-09-18  2025-03-24         2025-03-24   \n", "659   603125.SH  常青科技     基础化工      2024-09-23  2025-03-24         2025-03-24   \n", "782   001217.SZ   华尔泰     基础化工      2024-09-18  2025-03-21         2025-03-21   \n", "373   000963.SZ  华东医药     医药生物      2024-09-13  2025-03-21         2025-03-21   \n", "842   002549.SZ  凯美特气     基础化工      2024-08-27  2025-03-24         2025-03-24   \n", "601   600227.SH   赤天化     基础化工      2024-08-23  2025-03-24         2025-03-24   \n", "966   301190.SZ  善水科技     基础化工      2024-09-18  2025-02-28         2025-02-28   \n", "989   600619.SH  海立股份     家用电器      2024-08-27  2025-03-24         2025-02-28   \n", "1601  002329.<PERSON><PERSON>  皇氏集团     食品饮料      2024-09-18  2025-03-24         2025-03-07   \n", "783   001218.SZ  丽臣实业     基础化工      2024-09-18  2025-03-24         2025-02-18   \n", "771   000859.SZ  国风新材     基础化工      2024-09-13  2025-03-24         2025-03-24   \n", "629   600746.SH  江苏索普     基础化工      2024-06-24  2025-02-25         2025-02-25   \n", "704   603938.SH  三孚股份     基础化工      2024-08-23  2025-03-24         2025-02-28   \n", "\n", "      Now_PRA_Rate  PostNowSec_PRA_MaxRate_BreachCount  \\\n", "432        195.386                                 1.0   \n", "794         59.263                                 1.0   \n", "962         54.779                                 2.0   \n", "659        196.618                                 2.0   \n", "782        -22.989                                 2.0   \n", "373        187.695                                 1.0   \n", "842        126.538                                 1.0   \n", "601         65.962                                 2.0   \n", "966        162.500                                 4.0   \n", "989         52.260                                 2.0   \n", "1601        42.333                                 0.0   \n", "783        154.872                                 1.0   \n", "771        149.278                                 1.0   \n", "629         89.465                                 2.0   \n", "704         84.165                                 2.0   \n", "\n", "      Turn2NowSec_PRA_BandDiff  PRA2Cls_Percentile_Diff  ...  \\\n", "432                      6.726                   -0.674  ...   \n", "794                     11.559                   -0.605  ...   \n", "962                     10.262                   -0.587  ...   \n", "659                      7.506                   -0.459  ...   \n", "782                     13.299                   -0.248  ...   \n", "373                      5.862                   -0.224  ...   \n", "842                     11.158                   -0.191  ...   \n", "601                      8.118                   -0.120  ...   \n", "966                      7.515                   -0.098  ...   \n", "989                     46.500                   -0.093  ...   \n", "1601                     9.333                   -0.046  ...   \n", "783                      6.855                   -0.012  ...   \n", "771                     15.500                   -0.008  ...   \n", "629                      4.375                    0.185  ...   \n", "704                      4.169                    0.743  ...   \n", "\n", "      Recent3Day_PRA_MaxRate_CoverDays  Recent3Day_VRA_MaxRate_CoverDays  \\\n", "432                               99.0                               3.0   \n", "794                               24.0                              79.0   \n", "962                               29.0                               3.0   \n", "659                               99.0                               1.0   \n", "782                               12.0                               2.0   \n", "373                               99.0                               1.0   \n", "842                               37.0                               1.0   \n", "601                               28.0                               1.0   \n", "966                              122.0                               1.0   \n", "989                               16.0                               1.0   \n", "1601                               7.0                               5.0   \n", "783                               99.0                               1.0   \n", "771                               62.0                               1.0   \n", "629                               99.0                               3.0   \n", "704                              475.0                               1.0   \n", "\n", "      peak2recentbottom_lastdays  recentbottom2now_lastdays  \\\n", "432                          6.0                         27   \n", "794                         27.0                         54   \n", "962                         28.0                          4   \n", "659                         18.0                          4   \n", "782                         14.0                          5   \n", "373                         16.0                          5   \n", "842                         18.0                          4   \n", "601                         30.0                          4   \n", "966                          2.0                         20   \n", "989                         12.0                         20   \n", "1601                         6.0                         15   \n", "783                          5.0                         28   \n", "771                         21.0                          4   \n", "629                         10.0                         23   \n", "704                          6.0                         20   \n", "\n", "      PostNowSec_PGV_MaxRollAvg  Turn2NowSec_PRA_LowBand  \\\n", "432                       0.410                    0.106   \n", "794                       0.151                    0.034   \n", "962                       0.640                    0.122   \n", "659                       0.544                    0.089   \n", "782                       0.435                    0.067   \n", "373                       0.645                    0.145   \n", "842                       0.177                    0.038   \n", "601                       0.086                    0.017   \n", "966                       1.101                    0.068   \n", "989                       0.620                    0.026   \n", "1601                      0.151                    0.027   \n", "783                       0.497                    0.062   \n", "771                       0.155                    0.018   \n", "629                       0.223                    0.032   \n", "704                       0.387                    0.059   \n", "\n", "     Turn2NowSec_PRA_UpBand  PostNowSec2PreNowPeak_PRV2Price_RatioDiff  \\\n", "432                   0.713                                    -23.636   \n", "794                   0.393                                    -56.251   \n", "962                   1.252                                    -22.598   \n", "659                   0.668                                    101.370   \n", "782                   0.891                                     16.623   \n", "373                   0.850                                     23.143   \n", "842                   0.424                                    109.023   \n", "601                   0.138                                     26.614   \n", "966                   0.511                                    198.608   \n", "989                   1.209                                      8.328   \n", "1601                  0.252                                    -14.438   \n", "783                   0.425                                    299.739   \n", "771                   0.279                                     56.374   \n", "629                   0.140                                     76.444   \n", "704                   0.246                                    218.454   \n", "\n", "      PreNowPeak_PRV_Top3Mean  PullStart_Score  \n", "432                     0.500             44.5  \n", "794                     0.387             44.0  \n", "962                     0.694             65.0  \n", "659                     0.258             46.0  \n", "782                     0.333             69.0  \n", "373                     0.506             57.5  \n", "842                     0.083             78.0  \n", "601                     0.066             84.0  \n", "966                     0.329             25.5  \n", "989                     0.599             71.5  \n", "1601                    0.177             60.5  \n", "783                     0.122             37.0  \n", "771                     0.098             78.0  \n", "629                     0.116             41.0  \n", "704                     0.118             38.5  \n", "\n", "[15 rows x 31 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list_sorted = pull_start_list[['ts_code', 'name', 'industry', 'Period_TurnDate', 'Now_SecDate', 'recent_bottom_date','Now_PRA_Rate','PostNowSec_PRA_MaxRate_BreachCount', 'Turn2NowSec_PRA_BandDiff', \n", "                                          'PRA2Cls_Percentile_Diff', \n", "                                          'NowSec_PRA2Close_CoverDays_Diff', 'PostNowSecMaxPRA_PostTurnRank5PRA_Diff',\n", "                                          'PRA2Cls_Percentile_Ratio', 'Recent3Day_MaxPGV_Percentile_PostTurn', 'NowSec_MaxPRA_Percentile_PostTurn', \n", "                                          'PostNowSec_MaxCls_Percentile_PostTurn', \n", "                                          'PostTurn_VGVRank5_NearNowDate',\n", "                                          'PostTurn_VGVRank5_NearNowDate_Days',\n", "                                          'NowSec_PGV_MaxRollAvg_UpCoverDays',\n", "                                          'NowSec_MaxClose_UpCoverDays',\n", "                                          'prepost_recentbottom_daysdiff',\n", "                                          'Recent3Day_PRA_MaxRate_CoverDays', 'Recent3Day_VRA_MaxRate_CoverDays',\n", "                                          'peak2recentbottom_lastdays',\n", "                                          'recentbottom2now_lastdays','PostNowSec_PGV_MaxRollAvg', 'Turn2NowSec_PRA_LowBand', 'Turn2NowSec_PRA_UpBand', \n", "                                          'PostNowSec2PreNowPeak_PRV2Price_RatioDiff', 'PreNowPeak_PRV_Top3Mean', 'PullStart_Score']\n", "                                         ].sort_values(by='PRA2Cls_Percentile_Diff', ascending=True)\n", "pull_start_list_sorted"]}, {"cell_type": "code", "execution_count": 16, "id": "78bfdfd6", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "now_daily_ratio", "rawType": "float64", "type": "float"}, {"name": "now_high_ratio", "rawType": "float64", "type": "float"}, {"name": "Cal_Date", "rawType": "object", "type": "string"}], "ref": "56fdfebc-742b-4fc2-aa57-53daeb8fa7db", "rows": [["1757", "青鸟消防", "10.02", "10.02", "2025-03-04"], ["926", "*ST艾艾", "10.0", "10.0", "2025-03-04"], ["2083", "冠城新材", "9.92", "9.92", "2025-03-04"], ["1903", "信邦智能", "20.02", "20.02", "2025-03-04"], ["723", "*ST立航", "9.99", "9.99", "2025-03-04"], ["1054", "北化股份", "10.05", "10.05", "2025-03-04"], ["2597", "芯原股份", "20.01", "20.01", "2025-03-04"], ["2524", "安路科技", "20.0", "20.0", "2025-03-04"], ["703", "洪都航空", "10.0", "10.0", "2025-03-04"], ["766", "ST炼石", "9.94", "9.94", "2025-03-04"], ["3103", "智微智能", "10.0", "10.0", "2025-03-04"], ["3120", "久其软件", "9.97", "9.97", "2025-03-04"], ["1690", "宁波东力", "9.94", "9.94", "2025-03-04"], ["3136", "达华智能", "9.94", "9.94", "2025-03-04"], ["3161", "朗科科技", "19.98", "19.98", "2025-03-04"], ["791", "新兴装备", "9.99", "9.99", "2025-03-04"], ["2659", "紫光国微", "10.0", "10.0", "2025-03-04"], ["355", "华大智造", "20.0", "20.0", "2025-03-04"], ["200", "福建金森", "9.95", "9.95", "2025-03-04"], ["2487", "格林达", "9.99", "9.99", "2025-03-04"], ["2651", "好上好", "9.99", "9.99", "2025-03-04"], ["1543", "克来机电", "9.99", "9.99", "2025-03-04"], ["1740", "银宝山新", "9.99", "9.99", "2025-03-04"], ["1522", "捷昌驱动", "10.0", "10.0", "2025-03-04"], ["3162", "ST赛为", "20.0", "20.0", "2025-03-04"], ["2308", "兆威机电", "9.93", "10.0", "2025-03-04"], ["2996", "浙大网新", "10.02", "10.02", "2025-03-04"], ["831", "北方长龙", "20.01", "20.01", "2025-03-04"], ["1470", "杭齿前进", "10.01", "10.01", "2025-03-04"], ["2110", "万里股份", "10.02", "10.02", "2025-03-04"]], "shape": {"columns": 4, "rows": 30}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>now_daily_ratio</th>\n", "      <th>now_high_ratio</th>\n", "      <th>Cal_Date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1757</th>\n", "      <td>青鸟消防</td>\n", "      <td>10.02</td>\n", "      <td>10.02</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>926</th>\n", "      <td>*ST艾艾</td>\n", "      <td>10.00</td>\n", "      <td>10.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2083</th>\n", "      <td>冠城新材</td>\n", "      <td>9.92</td>\n", "      <td>9.92</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1903</th>\n", "      <td>信邦智能</td>\n", "      <td>20.02</td>\n", "      <td>20.02</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>723</th>\n", "      <td>*ST立航</td>\n", "      <td>9.99</td>\n", "      <td>9.99</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1054</th>\n", "      <td>北化股份</td>\n", "      <td>10.05</td>\n", "      <td>10.05</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2597</th>\n", "      <td>芯原股份</td>\n", "      <td>20.01</td>\n", "      <td>20.01</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2524</th>\n", "      <td>安路科技</td>\n", "      <td>20.00</td>\n", "      <td>20.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>703</th>\n", "      <td>洪都航空</td>\n", "      <td>10.00</td>\n", "      <td>10.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>766</th>\n", "      <td>ST炼石</td>\n", "      <td>9.94</td>\n", "      <td>9.94</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3103</th>\n", "      <td>智微智能</td>\n", "      <td>10.00</td>\n", "      <td>10.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3120</th>\n", "      <td>久其软件</td>\n", "      <td>9.97</td>\n", "      <td>9.97</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1690</th>\n", "      <td>宁波东力</td>\n", "      <td>9.94</td>\n", "      <td>9.94</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3136</th>\n", "      <td>达华智能</td>\n", "      <td>9.94</td>\n", "      <td>9.94</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3161</th>\n", "      <td>朗科科技</td>\n", "      <td>19.98</td>\n", "      <td>19.98</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>791</th>\n", "      <td>新兴装备</td>\n", "      <td>9.99</td>\n", "      <td>9.99</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2659</th>\n", "      <td>紫光国微</td>\n", "      <td>10.00</td>\n", "      <td>10.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>355</th>\n", "      <td>华大智造</td>\n", "      <td>20.00</td>\n", "      <td>20.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>福建金森</td>\n", "      <td>9.95</td>\n", "      <td>9.95</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2487</th>\n", "      <td>格林达</td>\n", "      <td>9.99</td>\n", "      <td>9.99</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2651</th>\n", "      <td>好上好</td>\n", "      <td>9.99</td>\n", "      <td>9.99</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1543</th>\n", "      <td>克来机电</td>\n", "      <td>9.99</td>\n", "      <td>9.99</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1740</th>\n", "      <td>银宝山新</td>\n", "      <td>9.99</td>\n", "      <td>9.99</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1522</th>\n", "      <td>捷昌驱动</td>\n", "      <td>10.00</td>\n", "      <td>10.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3162</th>\n", "      <td>ST赛为</td>\n", "      <td>20.00</td>\n", "      <td>20.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2308</th>\n", "      <td>兆威机电</td>\n", "      <td>9.93</td>\n", "      <td>10.00</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2996</th>\n", "      <td>浙大网新</td>\n", "      <td>10.02</td>\n", "      <td>10.02</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>831</th>\n", "      <td>北方长龙</td>\n", "      <td>20.01</td>\n", "      <td>20.01</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1470</th>\n", "      <td>杭齿前进</td>\n", "      <td>10.01</td>\n", "      <td>10.01</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2110</th>\n", "      <td>万里股份</td>\n", "      <td>10.02</td>\n", "      <td>10.02</td>\n", "      <td>2025-03-04</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       name  now_daily_ratio  now_high_ratio    Cal_Date\n", "1757   青鸟消防            10.02           10.02  2025-03-04\n", "926   *ST艾艾            10.00           10.00  2025-03-04\n", "2083   冠城新材             9.92            9.92  2025-03-04\n", "1903   信邦智能            20.02           20.02  2025-03-04\n", "723   *ST立航             9.99            9.99  2025-03-04\n", "1054   北化股份            10.05           10.05  2025-03-04\n", "2597   芯原股份            20.01           20.01  2025-03-04\n", "2524   安路科技            20.00           20.00  2025-03-04\n", "703    洪都航空            10.00           10.00  2025-03-04\n", "766    ST炼石             9.94            9.94  2025-03-04\n", "3103   智微智能            10.00           10.00  2025-03-04\n", "3120   久其软件             9.97            9.97  2025-03-04\n", "1690   宁波东力             9.94            9.94  2025-03-04\n", "3136   达华智能             9.94            9.94  2025-03-04\n", "3161   朗科科技            19.98           19.98  2025-03-04\n", "791    新兴装备             9.99            9.99  2025-03-04\n", "2659   紫光国微            10.00           10.00  2025-03-04\n", "355    华大智造            20.00           20.00  2025-03-04\n", "200    福建金森             9.95            9.95  2025-03-04\n", "2487    格林达             9.99            9.99  2025-03-04\n", "2651    好上好             9.99            9.99  2025-03-04\n", "1543   克来机电             9.99            9.99  2025-03-04\n", "1740   银宝山新             9.99            9.99  2025-03-04\n", "1522   捷昌驱动            10.00           10.00  2025-03-04\n", "3162   ST赛为            20.00           20.00  2025-03-04\n", "2308   兆威机电             9.93           10.00  2025-03-04\n", "2996   浙大网新            10.02           10.02  2025-03-04\n", "831    北方长龙            20.01           20.01  2025-03-04\n", "1470   杭齿前进            10.01           10.01  2025-03-04\n", "2110   万里股份            10.02           10.02  2025-03-04"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# recent_list = result_df_recentindus['ts_code'].values.tolist()\n", "pull_start_list[['name', 'now_daily_ratio', 'now_high_ratio', 'Cal_Date']]"]}, {"cell_type": "code", "execution_count": null, "id": "aa947628", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "Now_SecDate", "rawType": "object", "type": "string"}, {"name": "recent2bottom_days", "rawType": "int64", "type": "integer"}, {"name": "nowsec_recent_diff", "rawType": "int64", "type": "integer"}], "ref": "05ac3f71-a7a9-4f30-b6bd-a3999c79899a", "rows": [["739", "2025-05-15", "4", "4"]], "shape": {"columns": 3, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Now_SecDate</th>\n", "      <th>recent2bottom_days</th>\n", "      <th>nowsec_recent_diff</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>739</th>\n", "      <td>2025-05-15</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Now_SecDate  recent2bottom_days  nowsec_recent_diff\n", "739  2025-05-15                   4                   4"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list.query('ts_code==\"688395.SH\"')[['Now_SecDate', 'recent2bottom_days', 'nowsec_recent_diff']]"]}, {"cell_type": "code", "execution_count": 20, "id": "8f7c13e2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:381: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Bottom_List = pd.concat([Bottom_List, bottom_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:565: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Cumret_List = pd.concat([Cumret_List, cumret_temp_df], ignore_index=True)\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/swindex_funcs.py:636: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.\n", "  Rise_List = pd.concat([Rise_List, rise_temp_df], ignore_index=True)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["行业: 农林牧渔, 回撤转折日期: 2025-01-24, 转折日期: 2025-01-27, 转折回升幅度: 0.98, 下行天数: 276, 下行幅度: -25.77\n", "行业: 建筑材料, 回撤转折日期: 2025-01-24, 转折日期: 2024-10-31, 转折回升幅度: 1.36, 下行天数: 396, 下行幅度: -34.11\n", "行业: 轻工制造, 回撤转折日期: 2025-01-23, 转折日期: 2024-12-13, 转折回升幅度: 1.25, 下行天数: 146, 下行幅度: -28.08\n", "行业: 银行, 回撤转折日期: 2025-01-22, 转折日期: 2025-01-10, 转折回升幅度: 4.19, 下行天数: 48, 下行幅度: -27.04\n", "行业: 建筑装饰, 回撤转折日期: 2025-01-22, 转折日期: 2024-12-06, 转折回升幅度: 0.73, 下行天数: 345, 下行幅度: -29.11\n", "行业: 钢铁, 回撤转折日期: 2025-01-21, 转折日期: 2024-12-11, 转折回升幅度: 2.78, 下行天数: 379, 下行幅度: -19.38\n", "行业: 非银金融, 回撤转折日期: 2025-01-10, 转折日期: 2025-01-24, 转折回升幅度: 1.00, 下行天数: 46, 下行幅度: -17.09\n", "行业: 传媒, 回撤转折日期: 2025-01-06, 转折日期: 2024-12-13, 转折回升幅度: 6.60, 下行天数: 266, 下行幅度: -58.47\n", "行业: 计算机, 回撤转折日期: 2025-01-06, 转折日期: 2024-12-10, 转折回升幅度: 7.68, 下行天数: 290, 下行幅度: -51.09\n", "处理股票 000972.SZ 时出错: single positional indexer is out-of-bounds\n", "行业排序结果:\n", "1. 计算机: 34.72, 涨停数/总数:7/333\n", "2. 传媒: 20.25, 涨停数/总数:4/129\n", "3. 轻工制造: 6.96, 涨停数/总数:1/153\n", "4. 非银金融: 5.38, 涨停数/总数:2/83\n", "5. 建筑装饰: 4.68, 涨停数/总数:1/157\n", "6. 农林牧渔: 3.94, 涨停数/总数:1/100\n", "7. 钢铁: 2.44, 涨停数/总数:0/45\n", "8. 建筑材料: 2.27, 涨停数/总数:2/71\n", "9. 银行: 1.46, 涨停数/总数:0/42\n", "未存储筛选股票数据\n", "PullStart股票行业分布：\n", "           count\n", "industry       \n", "计算机           3\n", "非银金融          2\n", "传媒            1\n", "建筑装饰          1\n", "轻工制造          0\n", "农林牧渔          0\n", "钢铁            0\n", "建筑材料          0\n", "银行            0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2119: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  pull_start_list['pullstart_sort_position'] = pull_start_list.groupby('industry').cumcount() + 1\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2340: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  pull_indus_count = pull_start_list[['ts_code', 'industry']].groupby('industry').count()\n", "/Users/<USER>/PycharmProjects/AI_Stock/function_ai/StkPick_ModelFunc.py:2366: FutureWarning: The default of observed=False is deprecated and will be changed to True in a future version of pandas. Pass observed=False to retain current behavior or observed=True to adopt the future default and silence this warning.\n", "  by=['industry', 'stk_checkperiod_sumratio'], ascending=[True, False]).groupby('industry').head(20)\n"]}], "source": ["from function_ai.StkPick_ModelFunc import track_pullstart_stocks\n", "from function_ai.Func_Base import get_trade_date\n", "# trade_date = get_trade_date(start_date='2025-02-05', end_date='2025-03-06')\n", "# trade_date = trade_date[::-1]\n", "trade_date = ['2025-01-27']\n", "for date in trade_date:\n", "    trend_industry_list = ['轻工制造', '银行', '建筑装饰', '钢铁', '非银金融', '计算机', '传媒']\n", "    recent_industry_list = ['农林牧渔', '建筑材料']\n", "    end_date, trend_startdate = date, '2024-12-31'\n", "    recent_turndate = ['2025-01-10', '2025-01-24','2025-01-06']\n", "    result_df_head, result_df_turn, pull_start_list, pull_start_list_core, result_df_recentindus = track_pullstart_stocks(\n", "        end_date=end_date, trend_startdate=trend_startdate,\n", "        recent_turndate=recent_turndate,\n", "        rise_stop_signal=True,\n", "        industry_list=trend_industry_list,\n", "        limit_num=80, store_mode=False,\n", "        recent_indus=recent_industry_list,\n", "        recentindus_calstartdate='2024-12-31')"]}, {"cell_type": "code", "execution_count": 6, "id": "8901bbf2", "metadata": {}, "outputs": [], "source": ["pull_start_list_adj = pull_start_list.sort_values(by=['industry', 'PostNowSec2PreNowPeak_PRV2Price_RatioDiff'], ascending=[True, True])"]}, {"cell_type": "code", "execution_count": null, "id": "bb02a5b7", "metadata": {}, "outputs": [], "source": ["pull_start_list2 = pull_start_list.query('BreakPreNowPeak_Ratio<2.5 & BreakPreNowPeak_Ratio>(-25)')"]}, {"cell_type": "code", "execution_count": null, "id": "948e45b5", "metadata": {}, "outputs": [{"data": {"text/plain": ["['600259.SH',\n", " '000017.SZ',\n", " '002842.SZ',\n", " '300511.SZ',\n", " '300306.SZ',\n", " '000014.SZ',\n", " '603444.SH',\n", " '002631.SZ',\n", " '300805.SZ',\n", " '601801.SH',\n", " '603895.SH',\n", " '002686.SZ',\n", " '603665.SH',\n", " '300233.SZ',\n", " '300031.SZ',\n", " '301200.SZ',\n", " '300467.SZ',\n", " '605080.SH',\n", " '603082.SH',\n", " '300553.SZ']"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list_core['ts_code'].values.tolist()"]}, {"cell_type": "code", "execution_count": null, "id": "1bae9472", "metadata": {}, "outputs": [], "source": ["pull_start_list_repick = pull_start_list.copy()\n", "pull_start_list_repick['PreNowSec_CheckSignal'] = pull_start_list_repick.apply(\n", "    lambda fn: 1 if fn['PreNowSec_SumRatio']==fn['PostSecStart_Sec_MaxDrop_SumRatio'] or \n", "                    # fn['PreNowSec_AvgRatio'] == fn['PostSecStart_Sec_MaxDrop_AvgRatio'] or \n", "                    fn['PreNowSec_LastDays'] == fn['PostSecStart_Sec_MaxDrop_LastDays']\n", "                    else 0, axis=1)\n", "pull_start_list_repick['Pre_PreNowSec_CheckSignal'] = pull_start_list_repick.apply(\n", "    lambda fn: 1 if fn['Pre_PreNow_Sec_AvgRatio'] < fn['PostSecStart_Sec_Max_AvgRatio'] else 0, axis=1)\n", "pull_start_list_repick = pull_start_list_repick.query('PreNowSec_CheckSignal==1')\n"]}, {"cell_type": "code", "execution_count": null, "id": "96666679", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "PreNowSec_CheckSignal", "rawType": "int64", "type": "integer"}, {"name": "Pre_PreNowSec_CheckSignal", "rawType": "int64", "type": "integer"}, {"name": "Pre_PreNow_Sec_AvgRatio", "rawType": "float64", "type": "float"}, {"name": "PostSecStart_Sec_Max_AvgRatio", "rawType": "float64", "type": "float"}], "ref": "616681e1-01cc-4b90-8574-b7266f668b9e", "rows": [["1374", "1", "1", "1.376", "3.382"]], "shape": {"columns": 4, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PreNowSec_CheckSignal</th>\n", "      <th>Pre_PreNowSec_CheckSignal</th>\n", "      <th>Pre_PreNow_Sec_AvgRatio</th>\n", "      <th>PostSecStart_Sec_Max_AvgRatio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1374</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.376</td>\n", "      <td>3.382</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      PreNowSec_CheckSignal  Pre_PreNowSec_CheckSignal  \\\n", "1374                      1                          1   \n", "\n", "      Pre_PreNow_Sec_AvgRatio  PostSecStart_Sec_Max_AvgRatio  \n", "1374                    1.376                          3.382  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list_repick.query('ts_code==\"605099.SH\"')[['PreNowSec_CheckSignal', 'Pre_PreNowSec_CheckSignal', 'Pre_PreNow_Sec_AvgRatio', 'PostSecStart_Sec_Max_AvgRatio']]"]}, {"cell_type": "code", "execution_count": null, "id": "30d8c625", "metadata": {}, "outputs": [{"data": {"text/plain": ["['300236.SZ', '300429.SZ', '605588.SH', '688010.SH', '601665.SH', '605365.SH']"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["pull_start_list_core['ts_code'].values.tolist()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}