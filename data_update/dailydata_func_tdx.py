import os
import pandas as pd
import numpy as np
import struct
import logging
from typing import List, Dict, Optional, Tuple, Generator
from enum import Enum
from datetime import datetime

import os
import sys

# 获取项目根目录路径
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from function_ai.Func_Base import get_stock_info
import config.config_<PERSON> as config_Ali

from sqlalchemy import create_engine, text
import time
from multiprocessing import Pool
from concurrent.futures import ThreadPoolExecutor, as_completed
from sqlalchemy.pool import QueuePool

# 添加akshare导入
try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
    print("akshare导入成功")
except ImportError:
    AKSHARE_AVAILABLE = False
    print("警告: akshare未安装，将跳过股票基本信息获取功能")


def _get_field_value(data_dict: Dict, field_names: List[str]):
    """
    从字典中获取字段值，支持多个可能的字段名
    Args:
        data_dict: 数据字典
        field_names: 可能的字段名列表
    Returns:
        找到的第一个字段值，如果都不存在则返回None
    """
    for field_name in field_names:
        if field_name in data_dict:
            return data_dict[field_name]
    return None


def _convert_chinese_number(value):
    """
    转换中文数值（如'194.06亿'）为数字
    Args:
        value: 原始值
    Returns:
        转换后的数值
    """
    if value is None:
        return None

    try:
        if isinstance(value, str):
            # 处理带单位的数值
            if '万' in value:
                return float(value.replace('万', '').replace(',', '')) * 10000
            elif '亿' in value:
                return float(value.replace('亿', '').replace(',', '')) * 100000000
            elif '千' in value:
                return float(value.replace('千', '').replace(',', '')) * 1000
            else:
                # 移除逗号等分隔符
                return float(value.replace(',', ''))
        else:
            return float(value)
    except (ValueError, TypeError):
        return None


def get_stock_basic_info(ts_code: str) -> Dict:
    """
    通过akshare获取股票基本信息
    Args:
        ts_code: 股票代码，格式如'000001.SZ'
    Returns:
        Dict: 包含股票基本信息的字典
    """
    if not AKSHARE_AVAILABLE:
        return {}

    try:
        # 转换ts_code格式：'000001.SZ' -> '000001'
        symbol = ts_code.split('.')[0]

        # 获取股票基本信息
        stock_info_df = ak.stock_individual_info_em(symbol=symbol)

        # 将DataFrame转换为字典格式
        if isinstance(stock_info_df, pd.DataFrame) and not stock_info_df.empty:
            # akshare返回的是DataFrame，格式为: item | value
            stock_info = dict(zip(stock_info_df['item'], stock_info_df['value']))
        else:
            stock_info = {}

        # 提取需要的字段并重命名
        # 支持多种可能的字段名（akshare可能会更新字段名）
        result = {
            'total_share': _get_field_value(stock_info, ['总股本', '总股本(股)', '总股本(万股)']),
            'float_share': _get_field_value(stock_info, ['流通股', '流通股(股)', '流通股(万股)', '流通A股']),
            'total_mv': _get_field_value(stock_info, ['总市值', '总市值(元)', '总市值(万元)']),
            'circ_mv': _get_field_value(stock_info, ['流通市值', '流通市值(元)', '流通市值(万元)', '流通A股市值']),
        }

        # 数据类型转换和清理
        for key, value in result.items():
            result[key] = _convert_chinese_number(value)

        return result

    except Exception as e:
        print(f"获取股票 {ts_code} 基本信息失败: {str(e)}")
        return {}


def get_stocks_basic_info_batch(ts_codes: List[str], max_workers: int = 4) -> Dict[str, Dict]:
    """
    批量获取股票基本信息
    Args:
        ts_codes: 股票代码列表
        max_workers: 最大并行工作线程数
    Returns:
        Dict[str, Dict]: 股票代码到基本信息的映射
    """
    if not AKSHARE_AVAILABLE:
        print("akshare不可用，跳过股票基本信息获取")
        return {}

    print(f"开始批量获取 {len(ts_codes)} 只股票的基本信息...")

    stock_info_map = {}
    success_count = 0
    error_count = 0

    # 过滤掉指数代码
    index_codes = ['000906.SH', '000001.SH', '399006.SZ']
    stock_codes = [code for code in ts_codes if code not in index_codes]

    print(f"过滤后需要获取信息的股票数量: {len(stock_codes)}")

    # 使用线程池并行获取
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_code = {executor.submit(get_stock_basic_info, code): code
                         for code in stock_codes}

        for i, future in enumerate(as_completed(future_to_code), 1):
            ts_code = future_to_code[future]
            try:
                result = future.result()
                if result:
                    stock_info_map[ts_code] = result
                    success_count += 1
                else:
                    error_count += 1
            except Exception as e:
                print(f"获取 {ts_code} 信息时发生异常: {str(e)}")
                error_count += 1

            # 每处理100只股票显示一次进度
            if i % 100 == 0:
                print(f"已获取 {i}/{len(stock_codes)} 只股票信息 "
                      f"(成功: {success_count}, 失败: {error_count})")

    print(f"股票基本信息获取完成！成功: {success_count}, 失败: {error_count}")
    return stock_info_map


class DayDuplicateStrategy(Enum):
    """日线数据重复处理策略"""
    REPLACE = 'replace'  # 删除已有数据,重新录入
    SKIP = 'skip'       # 跳过重复数据

class DayStockFileProcessor:
    """股票日线数据文件处理器,用于处理股票日线数据文件"""

    def __init__(self, base_path: str):
        """
        初始化处理器
        Args:
            base_path: 基础路径,即日线数据文件目录的路径
        """
        self.base_path = '/Users/<USER>/PycharmProjects/Stock_Data_Files/vipdoc/'
        self.stock_file_map = {}  # 存储股票代码和文件路径的映射
        self.invalid_stocks = []  # 存储没有对应文件的股票代码

        # 交易所目录映射
        self.exchange_dirs = {
            'SH': 'sh/lday',
            'SZ': 'sz/lday',
            'BJ': 'bj/lday'
        }

        # 添加需要处理的指数列表
        self.index_codes = ['000906.SH', '000001.SH', '399006.SZ']

    def get_file_path(self, ts_code: str) -> str:
        """
        根据ts_code生成文件路径
        Args:
            ts_code: 股票代码(格式如:600744.SH)
        Returns:
            str: 完整的文件路径
        """
        code, exchange = ts_code.split('.')
        exchange_dir = self.exchange_dirs.get(exchange)

        if not exchange_dir:
            return ""

        filename = f"{exchange.lower()}{code}.day"
        return os.path.join(self.base_path, exchange_dir, filename)

    def prepare_stock_files(self) -> Tuple[Dict[str, str], List[str]]:
        """
        准备股票文件列表
        Returns:
            Tuple[Dict[str, str], List[str]]:
            - 返回有效的股票代码和文件路径的映射字典
            - 返回无效的股票代码列表(找不到对应文件的股票)
        """
        stock_df = get_stock_info()
        valid_stocks = {}
        invalid_stocks = []

        # 处理普通股票
        for ts_code in stock_df['ts_code']:
            filepath = self.get_file_path(ts_code)
            if filepath and os.path.exists(filepath):
                valid_stocks[ts_code] = filepath
            else:
                invalid_stocks.append(ts_code)

        # 处理指数
        for index_code in self.index_codes:
            filepath = self.get_file_path(index_code)
            if filepath and os.path.exists(filepath):
                valid_stocks[index_code] = filepath
            else:
                invalid_stocks.append(index_code)

        self.stock_file_map = valid_stocks
        self.invalid_stocks = invalid_stocks

        return valid_stocks, invalid_stocks

    def process_stock_list(self) -> None:
        """处理股票列表并打印统计信息"""
        total_stocks = len(self.stock_file_map) + len(self.invalid_stocks)
        self._print_basic_stats(total_stocks)
        self._print_exchange_stats()
        self._print_invalid_stocks()

    def _print_basic_stats(self, total_stocks: int) -> None:
        """打印基本统计信息"""
        print(f"股票处理统计信息:")
        print(f"总股票数量: {total_stocks}")
        print(f"有效股票数量: {len(self.stock_file_map)}")
        print(f"无效股票数量: {len(self.invalid_stocks)}")

    def _print_exchange_stats(self) -> None:
        """打印交易所统计信息"""
        exchange_stats = {}
        for ts_code in self.stock_file_map:
            exchange = ts_code.split('.')[1]
            exchange_stats[exchange] = exchange_stats.get(exchange, 0) + 1

        print("\n各交易所有效股票统计:")
        for exchange, count in exchange_stats.items():
            print(f"{exchange}: {count}只")

    def _print_invalid_stocks(self) -> None:
        """打印无效股票信息"""
        if not self.invalid_stocks:
            return

        print("\n以下股票代码找不到对应的数据文件:")
        for stock in self.invalid_stocks[:10]:
            print(f"- {stock}")
        if len(self.invalid_stocks) > 10:
            print(f"... 还有 {len(self.invalid_stocks) - 10} 只股票")

class DayDataProcessor:
    """日线数据处理器,用于处理和转换日线级别的股票数据"""

    def __init__(self,
                 duplicate_strategy: DayDuplicateStrategy = DayDuplicateStrategy.SKIP,
                 batch_size: int = 1000):
        """
        初始化数据处理器
        Args:
            duplicate_strategy: 数据重复处理策略
            batch_size: 批处理大小
        """
        self.duplicate_strategy = duplicate_strategy
        self.batch_size = batch_size
        self.logger = self._setup_logger()
        # 在初始化时就创建数据库连接池
        self._init_db_pool()

    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('DayDataProcessor')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

    def _init_db_pool(self):
        """初始化数据库连接池"""
        try:
            conf = config_Ali.configModel()
            self.engine = create_engine(
                f'mysql+pymysql://{conf.DC_DB_USER}:{conf.DC_DB_PASS}@{conf.DC_DB_URL}:{conf.DC_DB_PORT}/stocksfit',
                poolclass=QueuePool,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600
            )
            self.logger.info("数据库连接池初始化成功")
        except Exception as e:
            self.logger.error(f"数据库连接池初始化失败: {str(e)}")
            raise

    def process_day_data(self, file_path: str, ts_code: str) -> Optional[pd.DataFrame]:
        """
        处理日线数据文件
        Args:
            file_path: 文件路径
            ts_code: 股票代码(带市场标识,如600744.SH)
        Returns:
            Optional[pd.DataFrame]: 处理后的数据框或None(如果处理失败)
        """
        try:
            df = read_day_data(file_path)
            df = self._transform_data(df, ts_code)

            # 检查数据库中是否已存在该股票当天的数据
            existing_dates = self._check_existing_data(ts_code, df['trade_date'].unique())
            if existing_dates and self.duplicate_strategy == DayDuplicateStrategy.SKIP:
                df = df[~df['trade_date'].isin(existing_dates)]
                if df.empty:
                    self.logger.info(f"{ts_code} 的数据已存在，根据策略跳过")
                    return None

            return df
        except Exception as e:
            self.logger.error(f"处理文件 {file_path} 时发生错误: {str(e)}")
            return None

    def _transform_data(self, df: pd.DataFrame, ts_code: str) -> pd.DataFrame:
        """
        转换数据格式以适应数据库要求
        Args:
            df: 原始数据框
            ts_code: 股票代码
        Returns:
            pd.DataFrame: 处理后的数据框
        """
        try:
            df['ts_code'] = ts_code
            df['trade_date'] = df['date'].dt.strftime('%Y-%m-%d')

            # 计算pre_close (前一天的收盘价)
            df = df.sort_values('date')
            df['pre_close'] = df['close'].shift(1)
            # 对第一条记录,使用当天开盘价作为pre_close
            if not df.empty:
                df.iloc[0, df.columns.get_loc('pre_close')] = df.iloc[0]['open']

            # 确保所需列都存在
            required_columns = ['ts_code', 'trade_date', 'open', 'high', 'low',
                              'close', 'pre_close', 'vol', 'amount']

            # 检查所需列是否存在
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"缺少必要的列: {missing_columns}")

            result_df = df[required_columns].copy()

            self._validate_data(result_df)
            return result_df
        except Exception as e:
            self.logger.error(f"转换数据时发生错误: {str(e)}")
            raise

    def _validate_data(self, df: pd.DataFrame) -> None:
        """
        验证数据有效性
        Args:
            df: 待验证的数据框
        """
        self._check_price_validity(df)
        self._check_high_low_logic(df)
        self._check_volume_amount_match(df)

    def _check_price_validity(self, df: pd.DataFrame) -> None:
        """检查价格数据有效性"""
        price_columns = ['open', 'high', 'low', 'close', 'pre_close']
        for col in price_columns:
            if col in df.columns:
                invalid_prices = df[df[col] <= 0]
                if not invalid_prices.empty:
                    self.logger.warning(f"发现无效价格数据 ({col}): {len(invalid_prices)} 条记录")

    def _check_high_low_logic(self, df: pd.DataFrame) -> None:
        """检查最高价最低价逻辑"""
        invalid_high_low = df[df['high'] < df['low']]
        if not invalid_high_low.empty:
            self.logger.warning(f"发现最高价小于最低价的记录: {len(invalid_high_low)} 条")

    def _check_volume_amount_match(self, df: pd.DataFrame) -> None:
        """检查成交量和成交额匹配性"""
        # 检查成交量为0但成交额不为0的情况
        invalid_vol_amount = df[(df['vol'] == 0) & (df['amount'] > 0)]
        if not invalid_vol_amount.empty:
            self.logger.warning(f"发现成交量为0但成交额不为0的记录: {len(invalid_vol_amount)} 条")

    def _check_existing_data(self, ts_code: str, dates: List[str]) -> List[str]:
        """
        检查数据库中已存在的数据
        Args:
            ts_code: 股票代码
            dates: 要检查的日期列表
        Returns:
            List[str]: 已存在的日期列表
        """
        if not dates:
            return []

        dates_str = "','".join(dates)
        query = f"""
            SELECT DISTINCT trade_date
            FROM stock_data
            WHERE ts_code = '{ts_code}'
            AND trade_date IN ('{dates_str}')
        """

        try:
            with self.engine.connect() as conn:
                result = conn.execute(text(query))
                existing_dates = [row[0] for row in result]
                if existing_dates:
                    self.logger.info(f"{ts_code} 在以下日期已有数据: {existing_dates}")
                return existing_dates
        except Exception as e:
            self.logger.error(f"检查已存在数据时发生错误: {str(e)}")
            return []

    def save_to_database(self, df: pd.DataFrame) -> bool:
        """
        保存数据到数据库
        Args:
            df: 要保存的数据框
        Returns:
            bool: 保存是否成功
        """
        if df.empty:
            return True

        try:
            # 检查数据库表结构
            self._ensure_table_structure()

            # 保存数据
            df.to_sql('stock_data', self.engine, if_exists='append',
                     index=False, method='multi', chunksize=self.batch_size)

            # self.logger.info(f"成功保存 {len(df)} 条记录到数据库")
            return True
        except Exception as e:
            self.logger.error(f"保存数据到数据库时发生错误: {str(e)}")
            return False

    def _ensure_table_structure(self):
        """确保数据库表结构正确"""
        with self.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT COLUMN_NAME, DATA_TYPE
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = 'stocksfit'
                AND TABLE_NAME = 'stock_data'
            """))

            existing_columns = {row[0]: row[1] for row in result}

            required_columns = {
                'ts_code': 'varchar',
                'trade_date': 'varchar',
                'open': 'decimal',
                'high': 'decimal',
                'low': 'decimal',
                'close': 'decimal',
                'pre_close': 'decimal',
                'vol': 'bigint',
                'amount': 'decimal'
            }

            missing_columns = set(required_columns.keys()) - set(existing_columns.keys())
            if missing_columns:
                self.logger.warning(f"数据库表缺少列: {missing_columns}")

    def close(self):
        """关闭数据库连接"""
        if hasattr(self, 'engine'):
            self.engine.dispose()
            self.logger.info("数据库连接已关闭")

def read_day_data(file_path, days=None):
    """
    读取通达信日线行情数据文件(.day)并转换为DataFrame
    Args:
        file_path: 数据文件路径
        days: 读取最近几天的数据，None表示读取所有数据
    Returns:
        pd.DataFrame: 包含日线数据的DataFrame
    """
    dataSet = []
    code = os.path.basename(file_path).replace('.day', '')

    with open(file_path, 'rb') as f:
        buffer = f.read()
        row_size = 32

        for i in range(0, len(buffer), row_size):
            record = buffer[i:i+row_size]

            try:
                # 解析日线数据格式: IIIIIfII (8个字段，每个4字节)
                # 日期(4字节) + 开盘价(4字节) + 最高价(4字节) + 最低价(4字节) +
                # 收盘价(4字节) + 成交额(4字节) + 成交量(4字节) + 保留字段(4字节)
                date_raw, open_price, high_price, low_price, close_price, amount, volume, reserved = struct.unpack('<IIIIIfII', record)

                # 解析日期 (格式: YYYYMMDD)
                year = date_raw // 10000
                month = (date_raw % 10000) // 100
                day = date_raw % 100

                # 价格数据需要除以100转换为实际价格
                open_price = open_price / 100.0
                high_price = high_price / 100.0
                low_price = low_price / 100.0
                close_price = close_price / 100.0

                # 创建日期对象
                date_obj = datetime(year, month, day)

                dataSet.append({
                    'date': date_obj,
                    'open': open_price,
                    'high': high_price,
                    'low': low_price,
                    'close': close_price,
                    'vol': volume,
                    'amount': amount
                })

            except (struct.error, ValueError) as e:
                # 跳过无效记录
                continue

    if not dataSet:
        return pd.DataFrame()

    df = pd.DataFrame(dataSet)
    df = df.sort_values('date')

    # 如果指定了天数，只返回最近几天的数据
    if days is not None and days > 0:
        df = df.tail(days)

    return df

class DayDataBatchProcessor:
    """日线数据批处理器"""

    def __init__(self,
                 duplicate_strategy: DayDuplicateStrategy = DayDuplicateStrategy.SKIP,
                 batch_size: int = 1000,
                 max_workers: int = 4):
        """
        初始化批处理器
        Args:
            duplicate_strategy: 重复数据处理策略
            batch_size: 批处理大小
            max_workers: 最大工作进程数
        """
        self.file_processor = DayStockFileProcessor("")
        self.data_processor = DayDataProcessor(duplicate_strategy, batch_size)
        self.max_workers = max_workers
        self.logger = self._setup_logger()

        # 统计信息
        self.total_stocks = 0
        self.processed_stocks = 0
        self.success_count = 0
        self.error_count = 0
        self.start_time = None

    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger('DayDataBatchProcessor')
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger

    def process_all_stocks(self) -> Dict:
        """
        处理所有股票的日线数据
        Returns:
            Dict: 处理结果统计
        """
        self.start_time = time.time()

        # 准备股票文件列表
        stock_file_map, invalid_stocks = self.file_processor.prepare_stock_files()
        self.total_stocks = len(stock_file_map)

        if not stock_file_map:
            self.logger.warning("没有找到有效的股票数据文件")
            return self._get_summary()

        # self.logger.info(f"开始处理 {self.total_stocks} 只股票的日线数据")
        self.file_processor.process_stock_list()

        # 处理股票数据
        for ts_code, file_path in stock_file_map.items():
            try:
                self._process_single_stock(ts_code, file_path)
                self.success_count += 1
            except Exception as e:
                self.logger.error(f"处理股票 {ts_code} 时发生错误: {str(e)}")
                self.error_count += 1
            finally:
                self.processed_stocks += 1

            # 每处理100只股票打印一次进度
            if self.processed_stocks % 100 == 0:
                self._print_progress()

        return self._get_summary()

    def _process_single_stock(self, ts_code: str, file_path: str) -> None:
        """处理单只股票"""
        df = self.data_processor.process_day_data(file_path, ts_code)
        if df is not None and not df.empty:
            success = self.data_processor.save_to_database(df)
            if not success:
                raise Exception(f"保存股票 {ts_code} 数据失败")

    def _print_progress(self) -> None:
        """打印处理进度"""
        progress = (self.processed_stocks / self.total_stocks) * 100
        elapsed_time = time.time() - self.start_time
        self.logger.info(f"处理进度: {self.processed_stocks}/{self.total_stocks} "
                        f"({progress:.2f}%) - 成功: {self.success_count}, "
                        f"失败: {self.error_count}, 耗时: {elapsed_time:.2f}秒")

    def _get_summary(self) -> Dict:
        """获取处理结果摘要"""
        elapsed_time = time.time() - self.start_time if self.start_time else 0
        return {
            '总数': self.total_stocks,
            '已处理': self.processed_stocks,
            '成功': self.success_count,
            '失败': self.error_count,
            '完成率': f"{(self.processed_stocks/self.total_stocks)*100:.2f}%" if self.total_stocks > 0 else "0%",
            '耗时(秒)': f"{elapsed_time:.2f}"
        }


def process_single_stock_file(args):
    """
    处理单个股票文件的函数，用于并行处理
    Args:
        args: (ts_code, file_path, stock_info_map) 元组
    Returns:
        tuple: (data_type, pd.DataFrame or None)
               data_type: 'stock' 或 'index'
               DataFrame: 处理后的数据或None
    """
    if len(args) == 3:
        ts_code, file_path, stock_info_map = args
    else:
        ts_code, file_path = args
        stock_info_map = {}

    # 判断是否为指数
    index_codes = ['000906.SH', '000001.SH', '399006.SZ']
    data_type = 'index' if ts_code in index_codes else 'stock'

    try:
        # 读取最近2天的数据，用于计算准确的pre_close
        df_full = read_day_data(file_path, days=2)
        if df_full.empty:
            return data_type, None

        # 添加基础字段
        df_full['ts_code'] = ts_code
        df_full['trade_date'] = df_full['date'].dt.strftime('%Y-%m-%d')

        # 确保数据按日期排序
        df_full = df_full.sort_values('date')

        # 计算pre_close：使用前一天的真实收盘价
        df_full['pre_close'] = df_full['close'].shift(1)

        # 如果只有一天数据，pre_close使用开盘价
        if len(df_full) == 1:
            df_full.iloc[0, df_full.columns.get_loc('pre_close')] = df_full.iloc[0]['open']

        # 计算涨跌幅 (pct_chg = (close - pre_close) / pre_close * 100)
        df_full['pct_chg'] = ((df_full['close'] - df_full['pre_close']) / df_full['pre_close'] * 100).round(4)

        # 只保留最新一天的数据用于存储
        df = df_full.tail(1).copy()

        # 添加股票基本信息（仅对股票，不对指数）
        if data_type == 'stock' and ts_code in stock_info_map:
            stock_info = stock_info_map[ts_code]
            df['total_share'] = stock_info.get('total_share', None)
            df['float_share'] = stock_info.get('float_share', None)
            df['total_mv'] = stock_info.get('total_mv', None)
            df['circ_mv'] = stock_info.get('circ_mv', None)

            # 计算换手率 (turnover_rate = vol / total_share * 100)
            if stock_info.get('total_share') is not None and stock_info['total_share'] > 0:
                df['turnover_rate'] = (df['vol'] / stock_info['total_share']) * 100
            else:
                df['turnover_rate'] = None
        else:
            # 对于指数或没有基本信息的股票，设置为None
            df['total_share'] = None
            df['float_share'] = None
            df['total_mv'] = None
            df['circ_mv'] = None
            df['turnover_rate'] = None

        # 选择需要的列
        required_columns = ['ts_code', 'trade_date', 'open', 'high', 'low',
                          'close', 'pre_close', 'pct_chg', 'vol', 'amount', 'total_share',
                          'float_share', 'total_mv', 'circ_mv', 'turnover_rate']
        df = df[required_columns]

        return data_type, df

    except Exception as e:
        print(f"读取 {ts_code} 失败: {str(e)}")
        return data_type, None


def read_all_stocks_to_dataframe_parallel(max_workers=8, fetch_stock_info=True):
    """
    并行读取所有股票和指数数据，分别返回
    Args:
        max_workers: 最大并行工作线程数
        fetch_stock_info: 是否获取股票基本信息
    Returns:
        tuple: (stock_df, index_df) 股票数据和指数数据的DataFrame
    """
    print(f"开始并行读取所有股票和指数数据 (使用 {max_workers} 个线程)...")
    start_time = time.time()

    # 获取股票文件列表
    file_processor = DayStockFileProcessor("")
    stock_file_map, _ = file_processor.prepare_stock_files()

    if not stock_file_map:
        print("没有找到有效的股票数据文件")
        return pd.DataFrame(), pd.DataFrame()

    print(f"找到 {len(stock_file_map)} 只股票和指数，开始处理...")

    # 获取股票基本信息
    stock_info_map = {}
    if fetch_stock_info and AKSHARE_AVAILABLE:
        print("正在获取股票基本信息...")
        ts_codes = list(stock_file_map.keys())
        stock_info_map = get_stocks_basic_info_batch(ts_codes, max_workers=min(4, max_workers))
        print(f"成功获取 {len(stock_info_map)} 只股票的基本信息")
    else:
        print("跳过股票基本信息获取")

    print("开始并行读取数据文件...")

    stock_data = []
    index_data = []
    success_count = 0
    error_count = 0
    stock_count = 0
    index_count = 0

    # 准备任务列表，包含股票基本信息
    tasks = [(ts_code, file_path, stock_info_map) for ts_code, file_path in stock_file_map.items()]

    # 使用线程池并行处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_stock = {executor.submit(process_single_stock_file, task): task[0]
                          for task in tasks}

        # 收集结果
        for i, future in enumerate(as_completed(future_to_stock), 1):
            ts_code = future_to_stock[future]
            try:
                data_type, result = future.result()
                if result is not None:
                    if data_type == 'stock':
                        stock_data.append(result)
                        stock_count += 1
                    else:  # index
                        index_data.append(result)
                        index_count += 1
                    success_count += 1
                else:
                    error_count += 1
            except Exception as e:
                print(f"处理 {ts_code} 时发生异常: {str(e)}")
                error_count += 1

            # 每处理500只股票显示一次进度
            if i % 500 == 0:
                elapsed = time.time() - start_time
                print(f"已处理 {i}/{len(tasks)} 只股票和指数 "
                      f"(成功: {success_count}, 失败: {error_count}) "
                      f"耗时: {elapsed:.1f}秒")

    # 合并股票数据
    stock_df = pd.DataFrame()
    if stock_data:
        print("正在合并股票数据...")
        stock_df = pd.concat(stock_data, ignore_index=True)

    # 合并指数数据
    index_df = pd.DataFrame()
    if index_data:
        print("正在合并指数数据...")
        index_df = pd.concat(index_data, ignore_index=True)

    elapsed_time = time.time() - start_time
    print(f"并行数据读取完成！")
    print(f"股票记录数: {len(stock_df):,} ({stock_count}只股票)")
    if not stock_df.empty and 'turnover_rate' in stock_df.columns:
        valid_turnover = stock_df['turnover_rate'].notna().sum()
        print(f"  其中有换手率数据: {valid_turnover} 条")
    print(f"指数记录数: {len(index_df):,} ({index_count}只指数)")
    print(f"总记录数: {len(stock_df) + len(index_df):,}")
    print(f"成功: {success_count}, 失败: {error_count}")
    print(f"总耗时: {elapsed_time:.2f}秒")
    print(f"平均速度: {len(tasks)/elapsed_time:.1f}个/秒")

    return stock_df, index_df


def read_all_stocks_to_dataframe(fetch_stock_info=True):
    """
    一次性读取所有股票和指数数据 (串行版本)
    Args:
        fetch_stock_info: 是否获取股票基本信息
    Returns:
        tuple: (stock_df, index_df) 股票数据和指数数据的DataFrame
    """
    print("开始一次性读取所有股票和指数数据...")
    start_time = time.time()

    # 获取股票文件列表
    file_processor = DayStockFileProcessor("")
    stock_file_map, _ = file_processor.prepare_stock_files()

    if not stock_file_map:
        print("没有找到有效的股票数据文件")
        return pd.DataFrame(), pd.DataFrame()

    print(f"找到 {len(stock_file_map)} 只股票和指数，开始处理...")

    # 获取股票基本信息
    stock_info_map = {}
    if fetch_stock_info and AKSHARE_AVAILABLE:
        print("正在获取股票基本信息...")
        ts_codes = list(stock_file_map.keys())
        stock_info_map = get_stocks_basic_info_batch(ts_codes, max_workers=4)
        print(f"成功获取 {len(stock_info_map)} 只股票的基本信息")
    else:
        print("跳过股票基本信息获取")

    print("开始读取数据文件...")

    stock_data = []
    index_data = []
    success_count = 0
    error_count = 0
    stock_count = 0
    index_count = 0

    for i, (ts_code, file_path) in enumerate(stock_file_map.items(), 1):
        data_type, result = process_single_stock_file((ts_code, file_path, stock_info_map))
        if result is not None:
            if data_type == 'stock':
                stock_data.append(result)
                stock_count += 1
            else:  # index
                index_data.append(result)
                index_count += 1
            success_count += 1
        else:
            error_count += 1

        # 每处理500只股票显示一次进度
        if i % 500 == 0:
            elapsed = time.time() - start_time
            print(f"已处理 {i}/{len(stock_file_map)} 只股票和指数 "
                  f"(成功: {success_count}, 失败: {error_count}) "
                  f"耗时: {elapsed:.1f}秒")

    # 合并股票数据
    stock_df = pd.DataFrame()
    if stock_data:
        print("正在合并股票数据...")
        stock_df = pd.concat(stock_data, ignore_index=True)

    # 合并指数数据
    index_df = pd.DataFrame()
    if index_data:
        print("正在合并指数数据...")
        index_df = pd.concat(index_data, ignore_index=True)

    elapsed_time = time.time() - start_time
    print(f"数据读取完成！")
    print(f"股票记录数: {len(stock_df):,} ({stock_count}只股票)")
    if not stock_df.empty and 'turnover_rate' in stock_df.columns:
        valid_turnover = stock_df['turnover_rate'].notna().sum()
        print(f"  其中有换手率数据: {valid_turnover} 条")
    print(f"指数记录数: {len(index_df):,} ({index_count}只指数)")
    print(f"总记录数: {len(stock_df) + len(index_df):,}")
    print(f"成功: {success_count}, 失败: {error_count}")
    print(f"总耗时: {elapsed_time:.2f}秒")
    print(f"平均速度: {len(stock_file_map)/elapsed_time:.1f}个/秒")

    return stock_df, index_df


def save_dataframe_to_database(stock_df: pd.DataFrame, index_df: pd.DataFrame, batch_size: int = 50000):
    """
    将股票和指数DataFrame分别保存到对应的数据库表
    Args:
        stock_df: 股票数据DataFrame
        index_df: 指数数据DataFrame
        batch_size: 批处理大小
    """
    if stock_df.empty and index_df.empty:
        print("没有数据需要保存")
        return

    print(f"开始保存数据到数据库...")
    print(f"股票数据: {len(stock_df):,} 条记录")
    print(f"指数数据: {len(index_df):,} 条记录")

    start_time = time.time()

    # 初始化数据库连接
    try:
        conf = config_Ali.configModel()
        engine = create_engine(
            f'mysql+pymysql://{conf.DC_DB_USER}:{conf.DC_DB_PASS}@{conf.DC_DB_URL}:{conf.DC_DB_PORT}/stocksfit',
            poolclass=QueuePool,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            pool_recycle=3600
        )
        print("数据库连接建立成功")
    except Exception as e:
        print(f"数据库连接失败: {str(e)}")
        return

    try:
        # 保存股票数据到stock_data表
        if not stock_df.empty:
            print(f"\n开始保存股票数据到 stock_data 表...")
            _save_to_table(stock_df, 'stock_data', engine, batch_size)

        # 保存指数数据到index_data表
        if not index_df.empty:
            print(f"\n开始保存指数数据到 index_data 表...")
            _save_to_table(index_df, 'index_data', engine, batch_size)

        elapsed_time = time.time() - start_time
        total_records = len(stock_df) + len(index_df)
        print(f"\n所有数据保存完成！")
        print(f"总记录数: {total_records:,}")
        print(f"总耗时: {elapsed_time:.2f}秒")
        print(f"平均速度: {total_records/elapsed_time:,.0f}条记录/秒")

    finally:
        engine.dispose()
        print("数据库连接已关闭")


def _save_to_table(df: pd.DataFrame, table_name: str, engine, batch_size: int):
    """
    将DataFrame保存到指定表
    Args:
        df: 要保存的DataFrame
        table_name: 目标表名
        engine: 数据库引擎
        batch_size: 批处理大小
    """
    if df.empty:
        print(f"  {table_name} 没有数据需要保存")
        return

    total_batches = (len(df) + batch_size - 1) // batch_size

    # 显示数据结构信息
    print(f"  {table_name} 数据结构:")
    print(f"    列数: {len(df.columns)}")
    print(f"    列名: {list(df.columns)}")

    # 检查新增字段的数据情况
    if table_name == 'stock_data':
        if 'pct_chg' in df.columns:
            valid_pct_chg = df['pct_chg'].notna().sum()
            print(f"    有效涨跌幅记录: {valid_pct_chg}/{len(df)}")
        if 'turnover_rate' in df.columns:
            valid_turnover = df['turnover_rate'].notna().sum()
            print(f"    有效换手率记录: {valid_turnover}/{len(df)}")
        if 'total_share' in df.columns:
            valid_share = df['total_share'].notna().sum()
            print(f"    有效总股本记录: {valid_share}/{len(df)}")

    for i in range(0, len(df), batch_size):
        batch_df = df.iloc[i:i + batch_size]
        batch_num = i // batch_size + 1

        try:
            batch_df.to_sql(table_name, engine, if_exists='append',
                           index=False, method='multi')

            print(f"  {table_name} 批次 {batch_num}/{total_batches} 保存成功 "
                  f"({len(batch_df):,} 条记录)")

        except Exception as e:
            print(f"  {table_name} 批次 {batch_num} 保存失败: {str(e)}")
            # 显示详细错误信息
            if "doesn't exist" in str(e).lower() or "unknown column" in str(e).lower():
                print(f"    可能是数据库表结构问题，请检查 {table_name} 表是否包含所有必要字段")
            continue


def process_tdx_daydata_mega_fast(use_parallel=True, max_workers=8, fetch_stock_info=True):
    """
    超高速处理模式 - 一次性读取所有数据，分别保存股票和指数
    Args:
        use_parallel: 是否使用并行处理
        max_workers: 并行工作线程数
        fetch_stock_info: 是否获取股票基本信息并计算换手率
    """
    print("=" * 60)
    print(f"开始超高速处理通达信日线数据 ({'并行' if use_parallel else '串行'}模式)")
    print("股票数据将保存到 stock_data 表")
    print("指数数据将保存到 index_data 表")
    if fetch_stock_info:
        print("将通过akshare获取股票基本信息并计算换手率")
    else:
        print("跳过股票基本信息获取")
    print("=" * 60)

    total_start_time = time.time()

    # 步骤1: 一次性读取所有股票和指数数据
    if use_parallel:
        stock_df, index_df = read_all_stocks_to_dataframe_parallel(max_workers, fetch_stock_info)
    else:
        stock_df, index_df = read_all_stocks_to_dataframe(fetch_stock_info)

    if stock_df.empty and index_df.empty:
        print("没有数据可处理")
        return

    # 步骤2: 分别保存股票和指数数据到对应表
    save_dataframe_to_database(stock_df, index_df, batch_size=50000)

    total_elapsed = time.time() - total_start_time
    total_records = len(stock_df) + len(index_df)
    print("=" * 60)
    print(f"超高速处理完成！总耗时: {total_elapsed:.2f}秒")
    print(f"处理速度: {total_records/total_elapsed:,.0f}条记录/秒")
    print(f"股票数据: {len(stock_df):,} 条 → stock_data 表")
    print(f"指数数据: {len(index_df):,} 条 → index_data 表")
    if fetch_stock_info and not stock_df.empty and 'turnover_rate' in stock_df.columns:
        valid_turnover = stock_df['turnover_rate'].notna().sum()
        print(f"成功计算换手率: {valid_turnover} 条记录")
    print("=" * 60)


def process_tdx_daydata_parallel():
    """并行处理模式 - 推荐，包含股票基本信息"""
    process_tdx_daydata_mega_fast(use_parallel=True, max_workers=8, fetch_stock_info=True)


def process_tdx_daydata_parallel_fast():
    """并行处理模式 - 快速，不获取股票基本信息"""
    process_tdx_daydata_mega_fast(use_parallel=True, max_workers=8, fetch_stock_info=False)


def process_tdx_daydata_serial():
    """串行处理模式 - 包含股票基本信息"""
    process_tdx_daydata_mega_fast(use_parallel=False, fetch_stock_info=True)


def process_tdx_daydata():
    """主函数 - 默认使用并行超高速模式，包含股票基本信息"""
    process_tdx_daydata_parallel()


if __name__ == '__main__':
    process_tdx_daydata()

