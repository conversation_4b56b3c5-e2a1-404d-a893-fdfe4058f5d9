from function_ai.StkPick_Func_V7 import get_result_3
from function_ai.StkQuota_Func_V7 import set_resultindexs
from function_ai.DailyGap_Func import cal_gapindex
import pandas as pd
end_date = '2024-05-09'
stk_code = '603208.SH'
result = get_result_3(end_date=end_date, mode='First_Half')
from function_ai.Func_Base import get_trade_date
trade_df = get_trade_date()
stk_temp = result.query('ts_code==@stk_code')

common_column_names, gapvalue_column_names = set_resultindexs()
# column_names = common_column_names + gapvalue_column_names
Result_GapValue = pd.concat([stk_temp[['ts_code']], pd.DataFrame(columns=gapvalue_column_names)], sort=False, axis=1)

result_gapvalue = pd.DataFrame()
result_break = cal_gapindex(Result_GapValue.iloc[-1].copy(), stk_temp.iloc[-1].copy(), end_date=end_date, trade_df=trade_df, data_source='local')