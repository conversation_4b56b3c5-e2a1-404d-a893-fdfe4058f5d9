{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["已连接到 base (Python 3.11.5)"]}, {"cell_type": "code", "metadata": {"ExecuteTime": {"end_time": "2025-05-04T01:39:31.229712Z", "start_time": "2025-05-04T01:39:26.952834Z"}}, "source": ["from machine_learn.Func_MacinLn import *"], "outputs": [], "execution_count": 1}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集天数： 68\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/var/folders/pj/qfcvtnss6rj7b0t5zc2l3_sm0000gn/T/ipykernel_12918/4007923994.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m result_train = stkpick_model_train(start_date='2024-05-20', \n\u001b[0m\u001b[1;32m      2\u001b[0m                                    \u001b[0mend_date\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'2025-01-31'\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m                                    \u001b[0mlabel_style\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'Label'\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      4\u001b[0m                                    \u001b[0mmodel_select\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'XGB'\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m                                    \u001b[0mpretreat\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'none'\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/PycharmProjects/AI_Stock/machine_learn/Func_MacinLn.py\u001b[0m in \u001b[0;36mstkpick_model_train\u001b[0;34m(start_date, end_date, label_style, model_select, pretreat, lag_num)\u001b[0m\n\u001b[1;32m    749\u001b[0m         \u001b[0mprint\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'无符合PreTreat条件的待选品种'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    750\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 751\u001b[0;31m     result = clf_rf(origin_data, model_select=model_select,\n\u001b[0m\u001b[1;32m    752\u001b[0m                     \u001b[0mmodel_style\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'train'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mlabel_style\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mlabel_style\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    753\u001b[0m                     treat_style=pretreat, lag_num=lag_num)\n", "\u001b[0;32m~/PycharmProjects/AI_Stock/machine_learn/Func_MacinLn.py\u001b[0m in \u001b[0;36mclf_rf\u001b[0;34m(result, model_select, model_style, label_style, model_date, treat_style, lag_num)\u001b[0m\n\u001b[1;32m    482\u001b[0m         \u001b[0;31m#     data = get_train_label(data, label_style=label_style, lag_num=lag_num)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    483\u001b[0m         \u001b[0;31m# else:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 484\u001b[0;31m         \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mget_train_label\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdata\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mlabel_style\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mlabel_style\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mlag_num\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mlag_num\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    485\u001b[0m         \u001b[0;32mfrom\u001b[0m \u001b[0msklearn\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpreprocessing\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mLabelEncoder\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    486\u001b[0m         \u001b[0mlec\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mLabelEncoder\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/PycharmProjects/AI_Stock/machine_learn/Func_MacinLn.py\u001b[0m in \u001b[0;36mget_train_label\u001b[0;34m(result, label_style, lag_num)\u001b[0m\n\u001b[1;32m    187\u001b[0m                                                  axis=1, result_type='expand')\n\u001b[1;32m    188\u001b[0m     \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 189\u001b[0;31m         result_data['Ratio'] = result_data.apply(func=cal_osci_ratio, args=(stock_data, trading_date,),\n\u001b[0m\u001b[1;32m    190\u001b[0m                                                  axis=1, result_type='expand')\n\u001b[1;32m    191\u001b[0m     \u001b[0mresult_data\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mresult_data\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdropna\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msubset\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m'Ratio'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mhow\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m'any'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msort_values\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'Ratio'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mascending\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mFalse\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/frame.py\u001b[0m in \u001b[0;36mapply\u001b[0;34m(self, func, axis, raw, result_type, args, **kwargs)\u001b[0m\n\u001b[1;32m   8738\u001b[0m             \u001b[0mkwargs\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   8739\u001b[0m         )\n\u001b[0;32m-> 8740\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mop\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mapply\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   8741\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   8742\u001b[0m     def applymap(\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/apply.py\u001b[0m in \u001b[0;36mapply\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    686\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mapply_raw\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    687\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 688\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mapply_standard\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    689\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    690\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0magg\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/apply.py\u001b[0m in \u001b[0;36mapply_standard\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    810\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    811\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mapply_standard\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 812\u001b[0;31m         \u001b[0mresults\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mres_index\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mapply_series_generator\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    813\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    814\u001b[0m         \u001b[0;31m# wrap results\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/apply.py\u001b[0m in \u001b[0;36mapply_series_generator\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    826\u001b[0m             \u001b[0;32mfor\u001b[0m \u001b[0mi\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mv\u001b[0m \u001b[0;32min\u001b[0m \u001b[0menumerate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mseries_gen\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    827\u001b[0m                 \u001b[0;31m# ignore SettingWithCopy here in case the user mutates\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 828\u001b[0;31m                 \u001b[0mresults\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mf\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mv\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    829\u001b[0m                 \u001b[0;32mif\u001b[0m \u001b[0misinstance\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mresults\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mABCSeries\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    830\u001b[0m                     \u001b[0;31m# If we have a view on v, we need to make a copy because\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/apply.py\u001b[0m in \u001b[0;36mf\u001b[0;34m(x)\u001b[0m\n\u001b[1;32m    129\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    130\u001b[0m             \u001b[0;32mdef\u001b[0m \u001b[0mf\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 131\u001b[0;31m                 \u001b[0;32mreturn\u001b[0m \u001b[0mfunc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    132\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    133\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/PycharmProjects/AI_Stock/machine_learn/Func_MacinLn.py\u001b[0m in \u001b[0;36mcal_osci_ratio\u001b[0;34m(result, stock_data, trade_dates, cal_startdate, lag_num)\u001b[0m\n\u001b[1;32m     32\u001b[0m     \u001b[0mlennum\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mmin\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlag_num\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mtrade_dates\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mtrade_dates\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0mdate\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m-\u001b[0m \u001b[0;36m1\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     33\u001b[0m     \u001b[0mcal_enddate\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mtrade_dates\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mtrade_dates\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0mdate\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mlennum\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 34\u001b[0;31m     \u001b[0mstk_data\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mstock_data\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mquery\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'ts_code==@stk_code'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mset_index\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'trade_date'\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdrop\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mFalse\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     35\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mstk_data\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mloc\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mdate\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0mcal_enddate\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0;36m3\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     36\u001b[0m         \u001b[0mcal_startdate\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mstk_data\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mloc\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mdate\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0mcal_enddate\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m'close'\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mindex\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/frame.py\u001b[0m in \u001b[0;36mquery\u001b[0;34m(self, expr, inplace, **kwargs)\u001b[0m\n\u001b[1;32m   4058\u001b[0m         \u001b[0mkwargs\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"level\"\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mkwargs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpop\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"level\"\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m0\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0;36m1\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   4059\u001b[0m         \u001b[0mkwargs\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"target\"\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mN<PERSON>\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 4060\u001b[0;31m         \u001b[0mres\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0meval\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mexpr\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   4061\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   4062\u001b[0m         \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/frame.py\u001b[0m in \u001b[0;36meval\u001b[0;34m(self, expr, inplace, **kwargs)\u001b[0m\n\u001b[1;32m   4189\u001b[0m         \u001b[0mkwargs\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m\"resolvers\"\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mkwargs\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mget\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"resolvers\"\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0mtuple\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mresolvers\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   4190\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 4191\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0m_eval\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mexpr\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0minplace\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0minplace\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   4192\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   4193\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mselect_dtypes\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0minclude\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mNone\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mexclude\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mNone\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m->\u001b[0m \u001b[0mDataFrame\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/computation/eval.py\u001b[0m in \u001b[0;36meval\u001b[0;34m(expr, parser, engine, truediv, local_dict, global_dict, resolvers, level, target, inplace)\u001b[0m\n\u001b[1;32m    346\u001b[0m         )\n\u001b[1;32m    347\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 348\u001b[0;31m         \u001b[0mparsed_expr\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mExpr\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mexpr\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mengine\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mengine\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mparser\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mparser\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0menv\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0menv\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    349\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    350\u001b[0m         \u001b[0;31m# construct the engine and evaluate the parsed expression\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/computation/expr.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, expr, engine, parser, env, level)\u001b[0m\n\u001b[1;32m    804\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mparser\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mparser\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    805\u001b[0m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_visitor\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mPARSERS\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mparser\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0menv\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mengine\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mparser\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 806\u001b[0;31m         \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mterms\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mparse\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    807\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    808\u001b[0m     \u001b[0;34m@\u001b[0m\u001b[0mproperty\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/computation/expr.py\u001b[0m in \u001b[0;36mparse\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    823\u001b[0m         \u001b[0mParse\u001b[0m \u001b[0man\u001b[0m \u001b[0mexpression\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    824\u001b[0m         \"\"\"\n\u001b[0;32m--> 825\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_visitor\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvisit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexpr\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    826\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    827\u001b[0m     \u001b[0;34m@\u001b[0m\u001b[0mproperty\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/computation/expr.py\u001b[0m in \u001b[0;36mvisit\u001b[0;34m(self, node, **kwargs)\u001b[0m\n\u001b[1;32m    409\u001b[0m         \u001b[0mmethod\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m\"visit_\"\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0mtype\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    410\u001b[0m         \u001b[0mvisitor\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mgetattr\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmethod\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 411\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mvisitor\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    412\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    413\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mvisit_Module\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/computation/expr.py\u001b[0m in \u001b[0;36mvisit_Module\u001b[0;34m(self, node, **kwargs)\u001b[0m\n\u001b[1;32m    415\u001b[0m             \u001b[0;32mraise\u001b[0m \u001b[0mSyntaxError\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m\"only a single expression is allowed\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    416\u001b[0m         \u001b[0mexpr\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnode\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mbody\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 417\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvisit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mexpr\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    418\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    419\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mvisit_Expr\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/computation/expr.py\u001b[0m in \u001b[0;36mvisit\u001b[0;34m(self, node, **kwargs)\u001b[0m\n\u001b[1;32m    409\u001b[0m         \u001b[0mmethod\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m\"visit_\"\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0mtype\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    410\u001b[0m         \u001b[0mvisitor\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mgetattr\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmethod\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 411\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mvisitor\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    412\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    413\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mvisit_Module\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/computation/expr.py\u001b[0m in \u001b[0;36mvisit_Expr\u001b[0;34m(self, node, **kwargs)\u001b[0m\n\u001b[1;32m    418\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    419\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mvisit_Expr\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 420\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvisit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvalue\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    421\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    422\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0m_rewrite_membership_op\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mleft\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mright\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/computation/expr.py\u001b[0m in \u001b[0;36mvisit\u001b[0;34m(self, node, **kwargs)\u001b[0m\n\u001b[1;32m    409\u001b[0m         \u001b[0mmethod\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m\"visit_\"\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0mtype\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    410\u001b[0m         \u001b[0mvisitor\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mgetattr\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmethod\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 411\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mvisitor\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    412\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    413\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mvisit_Module\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/computation/expr.py\u001b[0m in \u001b[0;36mvisit_Compare\u001b[0;34m(self, node, **kwargs)\u001b[0m\n\u001b[1;32m    716\u001b[0m             \u001b[0mop\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mtranslate_In\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mops\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    717\u001b[0m             \u001b[0mbinop\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mast\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mBinOp\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mop\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mop\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mleft\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mnode\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mleft\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mright\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mcomps\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 718\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvisit\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mbinop\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    719\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    720\u001b[0m         \u001b[0;31m# recursive case: we have a chained comparison, a CMP b CMP c, etc.\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/computation/expr.py\u001b[0m in \u001b[0;36mvisit\u001b[0;34m(self, node, **kwargs)\u001b[0m\n\u001b[1;32m    409\u001b[0m         \u001b[0mmethod\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m\"visit_\"\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0mtype\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__name__\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    410\u001b[0m         \u001b[0mvisitor\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mgetattr\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmethod\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 411\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mvisitor\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    412\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    413\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mvisit_Module\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/computation/expr.py\u001b[0m in \u001b[0;36mvisit_BinOp\u001b[0;34m(self, node, **kwargs)\u001b[0m\n\u001b[1;32m    532\u001b[0m         \u001b[0mop\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mop_class\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mleft\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mright\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_maybe_transform_eq_ne\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mnode\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    533\u001b[0m         \u001b[0mleft\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mright\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_maybe_downcast_constants\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mleft\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mright\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 534\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_maybe_evaluate_binop\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mop\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mop_class\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mleft\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mright\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    535\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    536\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mvisit_Div\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mnode\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/computation/expr.py\u001b[0m in \u001b[0;36m_maybe_evaluate_binop\u001b[0;34m(self, op, op_class, lhs, rhs, eval_in_python, maybe_eval_in_python)\u001b[0m\n\u001b[1;32m    518\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mres\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mop\u001b[0m \u001b[0;32min\u001b[0m \u001b[0meval_in_python\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    519\u001b[0m             \u001b[0;31m# \"in\"/\"not in\" ops are always evaluated in python\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 520\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_maybe_eval\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mres\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0meval_in_python\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    521\u001b[0m         \u001b[0;32melif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mengine\u001b[0m \u001b[0;34m!=\u001b[0m \u001b[0;34m\"pytables\"\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    522\u001b[0m             if (\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/computation/expr.py\u001b[0m in \u001b[0;36m_maybe_eval\u001b[0;34m(self, binop, eval_in_python)\u001b[0m\n\u001b[1;32m    486\u001b[0m         \u001b[0;31m# in that case a + 2 * b will be evaluated using numexpr, and the \"in\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    487\u001b[0m         \u001b[0;31m# call will be evaluated using isin (in python space)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 488\u001b[0;31m         return binop.evaluate(\n\u001b[0m\u001b[1;32m    489\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0menv\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mengine\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mparser\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mterm_type\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0meval_in_python\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    490\u001b[0m         )\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/computation/ops.py\u001b[0m in \u001b[0;36mevaluate\u001b[0;34m(self, env, engine, parser, term_type, eval_in_python)\u001b[0m\n\u001b[1;32m    453\u001b[0m             \u001b[0;31m# base cases\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    454\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mop\u001b[0m \u001b[0;32min\u001b[0m \u001b[0meval_in_python\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 455\u001b[0;31m                 \u001b[0mres\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfunc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mleft\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvalue\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mright\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mvalue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    456\u001b[0m             \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    457\u001b[0m                 \u001b[0;32mfrom\u001b[0m \u001b[0mpandas\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcore\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcomputation\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0meval\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0meval\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/computation/ops.py\u001b[0m in \u001b[0;36m_in\u001b[0;34m(x, y)\u001b[0m\n\u001b[1;32m    269\u001b[0m     \"\"\"\n\u001b[1;32m    270\u001b[0m     \u001b[0;32mtry\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 271\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mx\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0misin\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0my\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    272\u001b[0m     \u001b[0;32mexcept\u001b[0m \u001b[0mAttributeError\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    273\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mis_list_like\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/series.py\u001b[0m in \u001b[0;36misin\u001b[0;34m(self, values)\u001b[0m\n\u001b[1;32m   5023\u001b[0m         \u001b[0mdtype\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mbool\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   5024\u001b[0m         \"\"\"\n\u001b[0;32m-> 5025\u001b[0;31m         \u001b[0mresult\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0malgorithms\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0misin\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_values\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mvalues\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   5026\u001b[0m         return self._constructor(result, index=self.index).__finalize__(\n\u001b[1;32m   5027\u001b[0m             \u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmethod\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m\"isin\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/opt/anaconda3/lib/python3.9/site-packages/pandas/core/algorithms.py\u001b[0m in \u001b[0;36misin\u001b[0;34m(comps, values)\u001b[0m\n\u001b[1;32m    522\u001b[0m         \u001b[0mf\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mhtable\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mismember\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    523\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 524\u001b[0;31m     \u001b[0;32mreturn\u001b[0m \u001b[0mf\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcomps\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mvalues\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    525\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    526\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["result_train = stkpick_model_train(start_date='2024-06-07',\n", "                                   end_date='2025-01-31',\n", "                                   label_style='Label',\n", "                                   model_select='XGB',\n", "                                   pretreat='turn',\n", "                                   lag_num=20)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集天数： 53\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[I 2024-12-22 12:33:07,443] A new study created in memory with name: no-name-c5058163-3f6d-49bb-b659-e835976fa2bb\n", "[I 2024-12-22 12:34:57,673] Trial 0 finished with value: 0.6964343423462243 and parameters: {'n_estimators': 360, 'max_depth': 7, 'learning_rate': 0.2226204144935528, 'subsample': 0.9382541971368488, 'colsample_bytree': 0.6515696778502623, 'gamma': 0.07487756254266621, 'min_child_weight': 1, 'reg_alpha': 0.46373236942518803, 'reg_lambda': 1.6243756734840225}. Best is trial 0 with value: 0.6964343423462243.\n", "[I 2024-12-22 12:36:11,347] Trial 1 finished with value: 0.6858953642859708 and parameters: {'n_estimators': 283, 'max_depth': 6, 'learning_rate': 0.22783873085321069, 'subsample': 0.93145894124939, 'colsample_bytree': 0.8421996474857121, 'gamma': 0.27169478396506375, 'min_child_weight': 1, 'reg_alpha': 0.6172788678035572, 'reg_lambda': 1.6497919929622893}. Best is trial 0 with value: 0.6964343423462243.\n", "[I 2024-12-22 12:39:02,049] Trial 2 finished with value: 0.699935365722288 and parameters: {'n_estimators': 365, 'max_depth': 9, 'learning_rate': 0.1562413062417932, 'subsample': 0.8631808177737761, 'colsample_bytree': 0.6225174922449304, 'gamma': 0.0340785617305095, 'min_child_weight': 4, 'reg_alpha': 0.7300937919378403, 'reg_lambda': 1.2156852135455232}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:43:07,376] Trial 3 finished with value: 0.695087794893892 and parameters: {'n_estimators': 287, 'max_depth': 8, 'learning_rate': 0.24470712173786063, 'subsample': 0.9022906150575677, 'colsample_bytree': 0.805890811694537, 'gamma': 0.07086229431558713, 'min_child_weight': 5, 'reg_alpha': 0.2934916384772206, 'reg_lambda': 1.7968786382512834}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:44:45,719] Trial 4 finished with value: 0.6904556716578693 and parameters: {'n_estimators': 277, 'max_depth': 9, 'learning_rate': 0.182820284046528, 'subsample': 0.9583990307348706, 'colsample_bytree': 0.9193894368455002, 'gamma': 0.27630615746050513, 'min_child_weight': 1, 'reg_alpha': 0.31881066386570145, 'reg_lambda': 1.336927112329907}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:46:46,103] Trial 5 finished with value: 0.6926640094796941 and parameters: {'n_estimators': 381, 'max_depth': 7, 'learning_rate': 0.1811851529224329, 'subsample': 0.8553258624388507, 'colsample_bytree': 0.7990239807150693, 'gamma': 0.24667131175698156, 'min_child_weight': 1, 'reg_alpha': 0.6716624823735596, 'reg_lambda': 1.421448995735942}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:48:44,767] Trial 6 finished with value: 0.6944055441847103 and parameters: {'n_estimators': 390, 'max_depth': 7, 'learning_rate': 0.17331304796435648, 'subsample': 0.8052653356382778, 'colsample_bytree': 0.7027620489674686, 'gamma': 0.00877310856570368, 'min_child_weight': 5, 'reg_alpha': 0.4213587196768528, 'reg_lambda': 1.7388903045094302}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:51:24,389] Trial 7 finished with value: 0.6949980250637365 and parameters: {'n_estimators': 288, 'max_depth': 9, 'learning_rate': 0.15614609429090567, 'subsample': 0.8603921337003924, 'colsample_bytree': 0.9262561438723925, 'gamma': 0.11947674824493158, 'min_child_weight': 2, 'reg_alpha': 0.5247060915708369, 'reg_lambda': 1.5936079458333676}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:53:09,193] Trial 8 finished with value: 0.6911917842651442 and parameters: {'n_estimators': 368, 'max_depth': 9, 'learning_rate': 0.23240173983801068, 'subsample': 0.9517783455565424, 'colsample_bytree': 0.9802469633109074, 'gamma': 0.21470509783627384, 'min_child_weight': 1, 'reg_alpha': 0.24945190578421594, 'reg_lambda': 1.7189275480845843}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:54:20,842] Trial 9 finished with value: 0.6764874860856763 and parameters: {'n_estimators': 207, 'max_depth': 7, 'learning_rate': 0.10943265574458583, 'subsample': 0.8629165964974708, 'colsample_bytree': 0.8681056033510215, 'gamma': 0.2255649779137107, 'min_child_weight': 3, 'reg_alpha': 0.3290748699995717, 'reg_lambda': 1.3276849772801715}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:56:29,525] Trial 10 finished with value: 0.6944953140148659 and parameters: {'n_estimators': 334, 'max_depth': 8, 'learning_rate': 0.1225308374638773, 'subsample': 0.9932014239412693, 'colsample_bytree': 0.6129189917057052, 'gamma': 0.1418186848116905, 'min_child_weight': 4, 'reg_alpha': 0.8841657206329618, 'reg_lambda': 1.0872339919448462}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 12:57:45,150] Trial 11 finished with value: 0.6856081008294733 and parameters: {'n_estimators': 331, 'max_depth': 6, 'learning_rate': 0.20612300508186304, 'subsample': 0.9015479476512174, 'colsample_bytree': 0.6043267387609413, 'gamma': 0.014221615873663246, 'min_child_weight': 3, 'reg_alpha': 0.05102779130594015, 'reg_lambda': 1.069733811223704}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:00:09,789] Trial 12 finished with value: 0.6945312219469281 and parameters: {'n_estimators': 344, 'max_depth': 8, 'learning_rate': 0.13839025974612715, 'subsample': 0.8101299666194082, 'colsample_bytree': 0.6939131733223599, 'gamma': 0.07837765029714244, 'min_child_weight': 4, 'reg_alpha': 0.7890408789200856, 'reg_lambda': 1.9717374491796091}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:02:08,582] Trial 13 finished with value: 0.6945132679808971 and parameters: {'n_estimators': 357, 'max_depth': 7, 'learning_rate': 0.20476024926775216, 'subsample': 0.9273691865282778, 'colsample_bytree': 0.7164512112344051, 'gamma': 0.06186106709150792, 'min_child_weight': 4, 'reg_alpha': 0.9776389509203265, 'reg_lambda': 1.2108066285141552}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:04:55,836] Trial 14 finished with value: 0.6966318359725663 and parameters: {'n_estimators': 400, 'max_depth': 8, 'learning_rate': 0.1497348276889373, 'subsample': 0.8380956804821335, 'colsample_bytree': 0.648738512101859, 'gamma': 0.10481005703732871, 'min_child_weight': 2, 'reg_alpha': 0.7125768622971533, 'reg_lambda': 1.5009389253275067}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:07:39,918] Trial 15 finished with value: 0.6945312219469281 and parameters: {'n_estimators': 390, 'max_depth': 9, 'learning_rate': 0.1452737614692306, 'subsample': 0.838088793428815, 'colsample_bytree': 0.7540587785031824, 'gamma': 0.18242967376671657, 'min_child_weight': 2, 'reg_alpha': 0.7560135653681959, 'reg_lambda': 1.4897456307335364}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:09:57,364] Trial 16 finished with value: 0.694154188660275 and parameters: {'n_estimators': 314, 'max_depth': 8, 'learning_rate': 0.1607528202314196, 'subsample': 0.8269547267144034, 'colsample_bytree': 0.6511400489820123, 'gamma': 0.11259506583048798, 'min_child_weight': 2, 'reg_alpha': 0.6520756987982171, 'reg_lambda': 1.2383163834894297}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:12:18,295] Trial 17 finished with value: 0.6948005314373945 and parameters: {'n_estimators': 255, 'max_depth': 9, 'learning_rate': 0.13137379997900414, 'subsample': 0.8843785786394728, 'colsample_bytree': 0.6560205670666653, 'gamma': 0.04552260698190478, 'min_child_weight': 3, 'reg_alpha': 0.8293586231998331, 'reg_lambda': 1.1614116074696532}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:15:06,943] Trial 18 finished with value: 0.6945850838450213 and parameters: {'n_estimators': 400, 'max_depth': 8, 'learning_rate': 0.10318302921719963, 'subsample': 0.8776123416498145, 'colsample_bytree': 0.759847864218374, 'gamma': 0.10575310833121747, 'min_child_weight': 4, 'reg_alpha': 0.9728716231179708, 'reg_lambda': 1.4110121684174233}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:17:25,115] Trial 19 finished with value: 0.6946209917770836 and parameters: {'n_estimators': 311, 'max_depth': 9, 'learning_rate': 0.15666841545934945, 'subsample': 0.8341266384892538, 'colsample_bytree': 0.7393566429644737, 'gamma': 0.16989118004702167, 'min_child_weight': 2, 'reg_alpha': 0.550087446127974, 'reg_lambda': 1.5216478292577769}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:19:07,100] Trial 20 finished with value: 0.6940644188301195 and parameters: {'n_estimators': 246, 'max_depth': 8, 'learning_rate': 0.19904615801450729, 'subsample': 0.846519348192308, 'colsample_bytree': 0.670909851388734, 'gamma': 0.03650603508346971, 'min_child_weight': 3, 'reg_alpha': 0.6925585828820764, 'reg_lambda': 1.0217079198404622}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:21:02,808] Trial 21 finished with value: 0.6953391504183274 and parameters: {'n_estimators': 367, 'max_depth': 7, 'learning_rate': 0.16949123025866544, 'subsample': 0.9165639177857169, 'colsample_bytree': 0.630518532785455, 'gamma': 0.09451610034388296, 'min_child_weight': 2, 'reg_alpha': 0.45538569818188857, 'reg_lambda': 1.5661176261230767}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:22:33,110] Trial 22 finished with value: 0.6876907608890805 and parameters: {'n_estimators': 354, 'max_depth': 6, 'learning_rate': 0.19114600118311756, 'subsample': 0.8801256544291542, 'colsample_bytree': 0.6697200866492512, 'gamma': 0.037832096614600935, 'min_child_weight': 1, 'reg_alpha': 0.5775037614833183, 'reg_lambda': 1.9049965242285372}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:24:20,188] Trial 23 finished with value: 0.6958598154332293 and parameters: {'n_estimators': 376, 'max_depth': 7, 'learning_rate': 0.2162108396623835, 'subsample': 0.965515210778701, 'colsample_bytree': 0.6380126355092246, 'gamma': 0.1398260873998254, 'min_child_weight': 2, 'reg_alpha': 0.7480741576697898, 'reg_lambda': 1.4591765403237602}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:26:19,938] Trial 24 finished with value: 0.6942080505583683 and parameters: {'n_estimators': 327, 'max_depth': 8, 'learning_rate': 0.14346790823733216, 'subsample': 0.8169655423960038, 'colsample_bytree': 0.600231631041479, 'gamma': 0.09183282122460949, 'min_child_weight': 3, 'reg_alpha': 0.15205849347998474, 'reg_lambda': 1.6528365040548982}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:28:13,841] Trial 25 finished with value: 0.6898272828467809 and parameters: {'n_estimators': 398, 'max_depth': 7, 'learning_rate': 0.12248440890712858, 'subsample': 0.987001756140719, 'colsample_bytree': 0.6757063753897109, 'gamma': 0.05229948897380451, 'min_child_weight': 5, 'reg_alpha': 0.4507731994394024, 'reg_lambda': 1.310738209238572}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:30:59,159] Trial 26 finished with value: 0.6977270279004633 and parameters: {'n_estimators': 353, 'max_depth': 8, 'learning_rate': 0.16422572620102305, 'subsample': 0.9421339111205961, 'colsample_bytree': 0.7291779280173059, 'gamma': 0.002126002391467069, 'min_child_weight': 1, 'reg_alpha': 0.8589968953702525, 'reg_lambda': 1.8306810360814487}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:33:23,921] Trial 27 finished with value: 0.6983374627455206 and parameters: {'n_estimators': 347, 'max_depth': 8, 'learning_rate': 0.167553412234359, 'subsample': 0.8679079074499524, 'colsample_bytree': 0.7273055026998037, 'gamma': 0.0037904198278414097, 'min_child_weight': 4, 'reg_alpha': 0.9009186030855807, 'reg_lambda': 1.8777086797465774}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:36:17,219] Trial 28 finished with value: 0.6988401737943911 and parameters: {'n_estimators': 343, 'max_depth': 9, 'learning_rate': 0.16708452318473158, 'subsample': 0.9158970386756309, 'colsample_bytree': 0.7284496386652011, 'gamma': 0.002824690940660681, 'min_child_weight': 4, 'reg_alpha': 0.8990978247530178, 'reg_lambda': 1.8734403025047466}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:38:55,580] Trial 29 finished with value: 0.6973320406477791 and parameters: {'n_estimators': 313, 'max_depth': 9, 'learning_rate': 0.18450145185898195, 'subsample': 0.9138601780781155, 'colsample_bytree': 0.7766428195643705, 'gamma': 0.024630118209961128, 'min_child_weight': 4, 'reg_alpha': 0.9217489881127978, 'reg_lambda': 1.9052671652919142}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:41:52,819] Trial 30 finished with value: 0.6991094832848578 and parameters: {'n_estimators': 342, 'max_depth': 9, 'learning_rate': 0.17250948297140534, 'subsample': 0.8857892412330469, 'colsample_bytree': 0.8082047452222483, 'gamma': 0.0003096754484130451, 'min_child_weight': 4, 'reg_alpha': 0.9229578634379162, 'reg_lambda': 1.9973283239466362}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:44:50,436] Trial 31 finished with value: 0.6985529103378937 and parameters: {'n_estimators': 344, 'max_depth': 9, 'learning_rate': 0.1733253000575388, 'subsample': 0.8911978242706663, 'colsample_bytree': 0.8397386232319756, 'gamma': 0.0015639685637776901, 'min_child_weight': 4, 'reg_alpha': 0.8192248091356431, 'reg_lambda': 1.980736976516331}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:47:34,270] Trial 32 finished with value: 0.697709073934432 and parameters: {'n_estimators': 338, 'max_depth': 9, 'learning_rate': 0.176538192382235, 'subsample': 0.8912096589155852, 'colsample_bytree': 0.8495372422637604, 'gamma': 0.025082018143083917, 'min_child_weight': 5, 'reg_alpha': 0.8036884849493154, 'reg_lambda': 1.9970923921779704}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:50:15,270] Trial 33 finished with value: 0.6979065675607741 and parameters: {'n_estimators': 318, 'max_depth': 9, 'learning_rate': 0.19298095327736733, 'subsample': 0.897353131371015, 'colsample_bytree': 0.8268500906496331, 'gamma': 0.026123308719463903, 'min_child_weight': 4, 'reg_alpha': 0.9965614029673182, 'reg_lambda': 1.9513452044009059}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:52:48,722] Trial 34 finished with value: 0.6970088692592192 and parameters: {'n_estimators': 297, 'max_depth': 9, 'learning_rate': 0.1530032390837206, 'subsample': 0.9169427209955965, 'colsample_bytree': 0.8817176014098931, 'gamma': 0.0005328667823237725, 'min_child_weight': 5, 'reg_alpha': 0.9322943438630589, 'reg_lambda': 1.8065479478457165}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:55:27,805] Trial 35 finished with value: 0.697116593055406 and parameters: {'n_estimators': 367, 'max_depth': 9, 'learning_rate': 0.18699301694235476, 'subsample': 0.8737710101925196, 'colsample_bytree': 0.8008085269164813, 'gamma': 0.06261373662405166, 'min_child_weight': 4, 'reg_alpha': 0.8497613106334851, 'reg_lambda': 1.7530898264334749}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 13:58:13,454] Trial 36 finished with value: 0.69767316600237 and parameters: {'n_estimators': 323, 'max_depth': 9, 'learning_rate': 0.17070008352990987, 'subsample': 0.9073741410276617, 'colsample_bytree': 0.8983081015253288, 'gamma': 0.018696598974817742, 'min_child_weight': 4, 'reg_alpha': 0.6332606237555911, 'reg_lambda': 1.8468116273477233}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:01:03,540] Trial 37 finished with value: 0.69767316600237 and parameters: {'n_estimators': 375, 'max_depth': 9, 'learning_rate': 0.17933233862703582, 'subsample': 0.9295493073722909, 'colsample_bytree': 0.8284102823743893, 'gamma': 0.042487719168289154, 'min_child_weight': 5, 'reg_alpha': 0.7630383972308306, 'reg_lambda': 1.9328920665406069}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:04:17,530] Trial 38 finished with value: 0.6975295342741211 and parameters: {'n_estimators': 341, 'max_depth': 9, 'learning_rate': 0.13549703979335576, 'subsample': 0.8515650473541099, 'colsample_bytree': 0.779278115286496, 'gamma': 0.076945735378592, 'min_child_weight': 3, 'reg_alpha': 0.9470939882544727, 'reg_lambda': 1.7001268724446834}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:06:56,442] Trial 39 finished with value: 0.6973499946138103 and parameters: {'n_estimators': 296, 'max_depth': 9, 'learning_rate': 0.16419227753642712, 'subsample': 0.8931582845300036, 'colsample_bytree': 0.9559198535587811, 'gamma': 0.02919372069026845, 'min_child_weight': 5, 'reg_alpha': 0.8305936742787001, 'reg_lambda': 1.7847953833321226}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:09:39,284] Trial 40 finished with value: 0.6941900965923372 and parameters: {'n_estimators': 364, 'max_depth': 9, 'learning_rate': 0.2425297250191757, 'subsample': 0.8872733070406776, 'colsample_bytree': 0.8178713020020999, 'gamma': 0.05171871547072761, 'min_child_weight': 4, 'reg_alpha': 0.8843013450215457, 'reg_lambda': 1.8683677115966724}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:16:17,527] Trial 41 finished with value: 0.6979424754928364 and parameters: {'n_estimators': 348, 'max_depth': 9, 'learning_rate': 0.16912641534746745, 'subsample': 0.8672055181789114, 'colsample_bytree': 0.8541882388801835, 'gamma': 0.001537970106450164, 'min_child_weight': 4, 'reg_alpha': 0.8879219279711739, 'reg_lambda': 1.996675793219151}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:19:35,980] Trial 42 finished with value: 0.6981220151531473 and parameters: {'n_estimators': 385, 'max_depth': 9, 'learning_rate': 0.17539898956560368, 'subsample': 0.8597227968411232, 'colsample_bytree': 0.7874190819033543, 'gamma': 0.012284558433638074, 'min_child_weight': 4, 'reg_alpha': 0.9137423676285752, 'reg_lambda': 1.878709076477902}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:22:01,712] Trial 43 finished with value: 0.6937951093396532 and parameters: {'n_estimators': 348, 'max_depth': 8, 'learning_rate': 0.1591951396978074, 'subsample': 0.8705103693890852, 'colsample_bytree': 0.6993189825643849, 'gamma': 0.01313885899049818, 'min_child_weight': 4, 'reg_alpha': 0.7230217236557506, 'reg_lambda': 1.935560964356229}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:25:06,751] Trial 44 finished with value: 0.6988940356924843 and parameters: {'n_estimators': 338, 'max_depth': 9, 'learning_rate': 0.15008503119031924, 'subsample': 0.9045831100173811, 'colsample_bytree': 0.7561575010440758, 'gamma': 0.013611938733463854, 'min_child_weight': 3, 'reg_alpha': 0.814083424008452, 'reg_lambda': 1.6559031412788126}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:27:25,784] Trial 45 finished with value: 0.6921612984308233 and parameters: {'n_estimators': 335, 'max_depth': 9, 'learning_rate': 0.14721569824570893, 'subsample': 0.9055571348875833, 'colsample_bytree': 0.7614536733990732, 'gamma': 0.2999693181362393, 'min_child_weight': 3, 'reg_alpha': 0.8062448391085492, 'reg_lambda': 1.6424957381476286}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:29:52,467] Trial 46 finished with value: 0.6957700456030738 and parameters: {'n_estimators': 273, 'max_depth': 9, 'learning_rate': 0.1510735563897887, 'subsample': 0.9399257617923131, 'colsample_bytree': 0.7979037448731183, 'gamma': 0.06213522636519753, 'min_child_weight': 3, 'reg_alpha': 0.674934179641244, 'reg_lambda': 1.6910317783587843}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:32:30,895] Trial 47 finished with value: 0.6958598154332293 and parameters: {'n_estimators': 305, 'max_depth': 9, 'learning_rate': 0.1811465529115801, 'subsample': 0.9115563751109556, 'colsample_bytree': 0.8714665678776917, 'gamma': 0.0348802705868958, 'min_child_weight': 3, 'reg_alpha': 0.35606290861879586, 'reg_lambda': 1.3811582968451397}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:35:46,137] Trial 48 finished with value: 0.6986606341340802 and parameters: {'n_estimators': 360, 'max_depth': 9, 'learning_rate': 0.12809465975062173, 'subsample': 0.9202393637465351, 'colsample_bytree': 0.9128040541447489, 'gamma': 0.017153936010032005, 'min_child_weight': 4, 'reg_alpha': 0.6033682758259424, 'reg_lambda': 1.7860880911403014}. Best is trial 2 with value: 0.699935365722288.\n", "[I 2024-12-22 14:38:56,217] Trial 49 finished with value: 0.6978167977306186 and parameters: {'n_estimators': 360, 'max_depth': 9, 'learning_rate': 0.12005949930946709, 'subsample': 0.9245658237308364, 'colsample_bytree': 0.9805916472612614, 'gamma': 0.05188540180961789, 'min_child_weight': 5, 'reg_alpha': 0.5992254928575689, 'reg_lambda': 1.7801895678599078}. Best is trial 2 with value: 0.699935365722288.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Best Parameters: {'n_estimators': 365, 'max_depth': 9, 'learning_rate': 0.1562413062417932, 'subsample': 0.8631808177737761, 'colsample_bytree': 0.6225174922449304, 'gamma': 0.0340785617305095, 'min_child_weight': 4, 'reg_alpha': 0.7300937919378403, 'reg_lambda': 1.2156852135455232}\n", "Best Score: 0.699935365722288\n", "Evaluation Accuracy: 0.7229443447037702\n", "交叉验证报错： Found input variables with inconsistent numbers of samples: [69623, 55698]\n", "XGB训练模型已存储至： XGB_model(Label_recov_1030).pkl\n"]}], "source": ["result_train = stkpick_model_train(start_date='2024-02-05', end_date='2024-10-30',  model_select='XGB', label_style='Label', pretreat='recov', lag_num=30)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/PycharmProjects/AI_Stock/machine_learn/Func_MacinLn.py:428: FutureWarning: DataFrame.applymap has been deprecated. Use DataFrame.map instead.\n", "  is_string = df.applymap(lambda x: isinstance(x, str))\n"]}, {"ename": "ValueError", "evalue": "Feature shape mismatch, expected: 656, got 658", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 4\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01mfunction_ai\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mFunc_Base\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m get_stock_info\n\u001b[1;32m      3\u001b[0m stk_list \u001b[38;5;241m=\u001b[39m get_stock_info()[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mts_code\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mtolist()\n\u001b[0;32m----> 4\u001b[0m result_check, result_predict \u001b[38;5;241m=\u001b[39m stkpick_model_predict(predict_date\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m2024-08-20\u001b[39m\u001b[38;5;124m'\u001b[39m, stk_list\u001b[38;5;241m=\u001b[39mstk_list, model_select\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mXGB\u001b[39m\u001b[38;5;124m'\u001b[39m, label_style\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mLabel\u001b[39m\u001b[38;5;124m'\u001b[39m, pretreat\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mnone\u001b[39m\u001b[38;5;124m'\u001b[39m, model_date\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m2024-10-30\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[0;32m~/PycharmProjects/AI_Stock/machine_learn/Func_MacinLn.py:782\u001b[0m, in \u001b[0;36mstkpick_model_predict\u001b[0;34m(predict_date, stk_list, label_style, model_select, model_date, store_mode, pretreat, lag_num, industry)\u001b[0m\n\u001b[1;32m    779\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m无符合PreTreat条件的待选品种\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m    780\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m--> 782\u001b[0m result \u001b[38;5;241m=\u001b[39m clf_rf(origin_data, model_select\u001b[38;5;241m=\u001b[39mmodel_select,\n\u001b[1;32m    783\u001b[0m                 model_style\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpredict\u001b[39m\u001b[38;5;124m'\u001b[39m, label_style\u001b[38;5;241m=\u001b[39mlabel_style, model_date\u001b[38;5;241m=\u001b[39mmodel_date,\n\u001b[1;32m    784\u001b[0m                 treat_style\u001b[38;5;241m=\u001b[39mpretreat, lag_num\u001b[38;5;241m=\u001b[39mlag_num)\n\u001b[1;32m    786\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m label_style \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mLabel\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;129;01mor\u001b[39;00m label_style \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMean_Label\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mor\u001b[39;00m label_style \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mStrength_Label\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m    787\u001b[0m     sort_label \u001b[38;5;241m=\u001b[39m rank_label[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[0;32m~/PycharmProjects/AI_Stock/machine_learn/Func_MacinLn.py:711\u001b[0m, in \u001b[0;36mclf_rf\u001b[0;34m(result, model_select, model_style, label_style, model_date, treat_style, lag_num)\u001b[0m\n\u001b[1;32m    708\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(pkl_filename, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mrb\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m file:\n\u001b[1;32m    709\u001b[0m     clf_pred \u001b[38;5;241m=\u001b[39m pickle\u001b[38;5;241m.\u001b[39mload(file)\n\u001b[0;32m--> 711\u001b[0m y_label \u001b[38;5;241m=\u001b[39m clf_pred\u001b[38;5;241m.\u001b[39mpredict(X_train_origin)\n\u001b[1;32m    712\u001b[0m y_pro \u001b[38;5;241m=\u001b[39m clf_pred\u001b[38;5;241m.\u001b[39mpredict_proba(X_train_origin)\n\u001b[1;32m    713\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m label_style \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mLabel\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;129;01mor\u001b[39;00m label_style \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMean_Label\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mor\u001b[39;00m label_style \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mStrength_Label\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.11/site-packages/xgboost/sklearn.py:1565\u001b[0m, in \u001b[0;36mXGBClassifier.predict\u001b[0;34m(self, X, output_margin, validate_features, base_margin, iteration_range)\u001b[0m\n\u001b[1;32m   1556\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpredict\u001b[39m(\n\u001b[1;32m   1557\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   1558\u001b[0m     X: ArrayLike,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1562\u001b[0m     iteration_range: Optional[IterationRange] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m   1563\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m ArrayLike:\n\u001b[1;32m   1564\u001b[0m     \u001b[38;5;28;01mwith\u001b[39;00m config_context(verbosity\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mverbosity):\n\u001b[0;32m-> 1565\u001b[0m         class_probs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28msuper\u001b[39m()\u001b[38;5;241m.\u001b[39mpredict(\n\u001b[1;32m   1566\u001b[0m             X\u001b[38;5;241m=\u001b[39mX,\n\u001b[1;32m   1567\u001b[0m             output_margin\u001b[38;5;241m=\u001b[39moutput_margin,\n\u001b[1;32m   1568\u001b[0m             validate_features\u001b[38;5;241m=\u001b[39mvalidate_features,\n\u001b[1;32m   1569\u001b[0m             base_margin\u001b[38;5;241m=\u001b[39mbase_margin,\n\u001b[1;32m   1570\u001b[0m             iteration_range\u001b[38;5;241m=\u001b[39miteration_range,\n\u001b[1;32m   1571\u001b[0m         )\n\u001b[1;32m   1572\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m output_margin:\n\u001b[1;32m   1573\u001b[0m             \u001b[38;5;66;03m# If output_margin is active, simply return the scores\u001b[39;00m\n\u001b[1;32m   1574\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m class_probs\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.11/site-packages/xgboost/sklearn.py:1186\u001b[0m, in \u001b[0;36mXGBModel.predict\u001b[0;34m(self, X, output_margin, validate_features, base_margin, iteration_range)\u001b[0m\n\u001b[1;32m   1184\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_can_use_inplace_predict():\n\u001b[1;32m   1185\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1186\u001b[0m         predts \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_booster()\u001b[38;5;241m.\u001b[39minplace_predict(\n\u001b[1;32m   1187\u001b[0m             data\u001b[38;5;241m=\u001b[39mX,\n\u001b[1;32m   1188\u001b[0m             iteration_range\u001b[38;5;241m=\u001b[39miteration_range,\n\u001b[1;32m   1189\u001b[0m             predict_type\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmargin\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m output_margin \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mvalue\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   1190\u001b[0m             missing\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmissing,\n\u001b[1;32m   1191\u001b[0m             base_margin\u001b[38;5;241m=\u001b[39mbase_margin,\n\u001b[1;32m   1192\u001b[0m             validate_features\u001b[38;5;241m=\u001b[39mvalidate_features,\n\u001b[1;32m   1193\u001b[0m         )\n\u001b[1;32m   1194\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m _is_cupy_alike(predts):\n\u001b[1;32m   1195\u001b[0m             \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mcupy\u001b[39;00m  \u001b[38;5;66;03m# pylint: disable=import-error\u001b[39;00m\n", "File \u001b[0;32m/opt/anaconda3/lib/python3.11/site-packages/xgboost/core.py:2520\u001b[0m, in \u001b[0;36mBooster.inplace_predict\u001b[0;34m(self, data, iteration_range, predict_type, missing, validate_features, base_margin, strict_shape)\u001b[0m\n\u001b[1;32m   2516\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\n\u001b[1;32m   2517\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m`shape` attribute is required when `validate_features` is True.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   2518\u001b[0m         )\n\u001b[1;32m   2519\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(data\u001b[38;5;241m.\u001b[39mshape) \u001b[38;5;241m!=\u001b[39m \u001b[38;5;241m1\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnum_features() \u001b[38;5;241m!=\u001b[39m data\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m1\u001b[39m]:\n\u001b[0;32m-> 2520\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m   2521\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFeature shape mismatch, expected: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnum_features()\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   2522\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mgot \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdata\u001b[38;5;241m.\u001b[39mshape[\u001b[38;5;241m1\u001b[39m]\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   2523\u001b[0m         )\n\u001b[1;32m   2525\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m _is_np_array_like(data):\n\u001b[1;32m   2526\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdata\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m _ensure_np_dtype\n", "\u001b[0;31mValueError\u001b[0m: Feature shape mismatch, expected: 656, got 658"]}], "source": ["from machine_learn.Func_MacinLn import *\n", "from function_ai.Func_Base import get_stock_info\n", "stk_list = get_stock_info()['ts_code'].tolist()\n", "result_check, result_predict = stkpick_model_predict(predict_date='2024-08-20', stk_list=stk_list, model_select='XGB', label_style='Label', pretreat='none', model_date='2024-10-30')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 2}