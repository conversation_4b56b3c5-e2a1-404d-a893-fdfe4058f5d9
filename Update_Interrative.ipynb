# 更新当日数据
from function_ai.swindex_crap import crap_swindex_from_web, level1_update
crap = crap_swindex_from_web(end_date='2025-06-12', mode='today')
level1_update(mode='replace')

# 更新昨日数据
from function_ai.swindex_crap import crap_swindex_from_web, level1_update
crap = crap_swindex_from_web(end_date='2025-06-12', mode='history')
level1_update(mode='replace')

from function_ai.StkQuota_Func_V7 import stksfit_result_3
stk_temp, _ = stksfit_result_3(end_date='2024-12-24', mode='pick')

from function_ai.StkQuota_Func_V7 import stksfit_result_3
from function_ai.Func_Base import get_trade_date

dates = ['2024-05-09', '2024-05-13', '2024-05-17']
for date in dates:
    # if date == '2025-06-23':
        # stk_temp = stksfit_result_3(end_date=date, cal_mode='First_Half', freqdata_source='local', storemode=True) 
    # else:
    stk_temp = stksfit_result_3(end_date=date, cal_mode='All', freqdata_source='local', storemode=True)      
    print('finish date:', date)

from function_ai.StkQuota_Func_V7 import update_result_column, stksfit_result_3
from function_ai.Func_Base import get_trade_date

trade_dates = get_trade_date()
dates = trade_dates[(trade_dates>='2024-12-26') & (trade_dates<='2024-12-31')]
# 
# dates = ['2024-07-10']
for date in dates:
    
    # if date == '2024-09-06':
        # stk_temp = update_result_column(end_date=date, mode='Second_Half', freqdata_source='local')
    # else:
    stk_temp = stksfit_result_3(end_date=date, mode='pick', freqdata_source='local')      
    print('finish date:', date)


result_break = stk_temp[0]
result_break['Cal_Date'].iloc[-1]

from data_update.freqdata_func_tdx import process_tdx_freqdata
from function_ai.StkQuota_Func_V7 import update_result_column, stksfit_result_3
from function_ai.Func_Base import get_trade_date
# process_tdx_freqdata()

dates = ['2025-08-15']
for date in dates:
    stk_temp = stksfit_result_3(end_date=date, cal_mode='Second_Half', freqdata_source='local', storemode=True)
    print('finish date:', date)
    

result_break = stk_temp[0]
result_break['Period_TurnDate'].iloc[:10]

from data_update.dailydata_func_tdx import read_day_data, process_tdx_daydata
# file_path = '/Users/<USER>/PycharmProjects/Stock_Data_Files/vipdoc/sh/lday/sh600104.day'
# df = read_day_data(file_path=file_path)
process_tdx_daydata()

from sqlalchemy import create_engine, text
import config.config_Ali as config
conf = config.configModel()
engine = create_engine(
    'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
        conf.DC_DB_PORT) + '/stocksfit')
import pandas as pd
pd.io.sql.to_sql(result_break, 'stk_results_3', engine, index=False, schema='stocksfit',
                             if_exists='append')
engine.dispose()