{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["已连接到 base (Python 3.11.5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkQuota_Func_V7 import stksfit_result_3\n", "result_filtered, _ = stksfit_result_3(end_date='2024-11-27', mode='ADJ', stk_code='000910.SZ')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cond1 = (result_filtered.eval(\"PostNowSec_Recover_TopOpen_Days<=5 & \"\n", "                                     \"PostNowSec_Recover_TopOpen_Date==Cal_Date\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cond2 = result_filtered.eval(\"PostNowSec_LastDays<=5 & \"\n", "                                     \"PreNowSec_LastDays>3\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cond3 = ((result_filtered.eval(\"Section_StartDate<Now_SecDate & \"\n", "                                     \"PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays>0\")) |\n", "                 (result_filtered.eval(\"Section_StartDate==Now_SecDate & \"\n", "                                     \"Peak2Sec_PGV_MinRollAvg2Sec_LastDays>0 & \"\n", "                                     \"Peak2Sec_PGV_MinRollAvg2Sec_LastDays<10\")))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cond4 = result_filtered.eval(\"PreNowPeak2NowSec_RatioMovAvg_NowSecRank<8\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cond5 = result_filtered.eval(\"(PostSecStart_Over5MovAvg_Prop>0.5 | Section_StartDate==Now_SecDate) & \"\n", "                                   \"Peak2Sec_Und5MovAvg_Prop>0.6\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_filtered[['PostPeak_Recent_Neg4_RecovDate', 'PostNowSec_Recover_TopOpen_Date', 'PostNowSec_Recover_TopOpen_Days', 'PreNowPeak2NowSec_RatioMovAvg_NowSecRank', 'Now_SecDate', 'PreNowPeak2NowSec_MinRatioMovAvg_Date', 'Section_StartDate']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_filtered[['PreNowSec_LastDays', 'PostNowSec_LastDays', 'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays', 'Peak2Sec_PGV_MinRollAvg2Sec_LastDays']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_filtered[['PostSecStart_PGV_RollAvg_HighQuntl', 'Recent3Day_PGV_MaxRollAvg']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.Func_Base import get_stock_data\n", "from function_ai.StkQuota_Func_V7 import cal_trend_wls\n", "stk_data = get_stock_data(stk_code='003041.SZ', start_date='2024-09-13', end_date='2024-11-15')\n", "stk_data = stk_data.set_index('trade_date')\n", "stk_data['trend_wls_slope'] = 0\n", "stk_data['trend_wls_r2'] = 0\n", "stk_data['trend_wls_dev_ratio'] = 0\n", "for date in stk_data.index:\n", "    trend_wls_slope, trend_wls_r2, trend_wls_dev_ratio = cal_trend_wls(stk_data.loc[date:])\n", "    stk_data.loc[date, 'trend_wls_slope'] = trend_wls_slope\n", "    stk_data.loc[date, 'trend_wls_r2'] = trend_wls_r2\n", "    stk_data.loc[date, 'trend_wls_dev_ratio'] = trend_wls_dev_ratio\n", "r2_length = len(stk_data.loc[stk_data['trend_wls_r2'].idxmax():])\n", "r2_length, stk_data['trend_wls_r2'].idxmax(), stk_data['trend_wls_r2'].max(), stk_data.loc[stk_data['trend_wls_r2'].idxmax(),'trend_wls_dev_ratio']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 计算section状态\n", "from function_ai.Func_Base import section_stat\n", "section_rise, section_drop, day_list = section_stat(stk_code='002691.SZ', start_date='2024-03-18', end_date='2025-01-10')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_ModelFunc import cal_stock_industry_strength\n", "relative_return, cum_relative_returns = cal_stock_industry_strength(stk_code='603667.SH', start_date='2024-09-18', end_date='2025-01-14')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cum_relative_returns.loc['2024-12-12':].idxmin()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cum_relative_returns.plot()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.Func_Base import get_stock_data\n", "stk_data = get_stock_data(stk_code='603928.SH', start_date='2024-12-10', end_date='2025-01-06')\n", "stk_data = stk_data.set_index('trade_date')\n", "stk_data['daily_ratio'] = round((stk_data['close']/stk_data['close'].shift(1) - 1)*100, 2)\n", "ratio_mean = stk_data['daily_ratio'].mean()\n", "ratio_std = stk_data.query('daily_ratio>0')['daily_ratio'].std()\n", "ratio_mean,ratio_std"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_Func_V7 import induspick_turn_state\n", "indus_count, plat_indus_count = induspick_turn_state(end_date='2025-01-06', section_startdate='2025-01-06')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_Func_V7 import get_data_from_pullstart\n", "result = get_data_from_pullstart(end_date='2025-02-28', \n", "                                 class_type='TurnBreak', industry_list=['机械设备'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_ModelFunc import track_pullstart_stocks\n", "from function_ai.Func_Base import get_trade_date\n", "# trade_date = get_trade_date(start_date='2025-02-05', end_date='2025-03-04')\n", "# trade_date = trade_date[::-1]\n", "trade_date = ['2024-10-21']\n", "for date in trade_date:\n", "    # industry_list = ['房地产', '纺织服饰', '建筑装饰', '建筑材料', '轻工制造', '家用电器', '交通运输', '公用事业', \n", "                    #  '煤炭', '农林牧渔']\n", "    industry_list=['建筑材料']\n", "    end_date, index_turndate, index_peakdate = date, ['2024-10-08', '2024-09-18'], '2024-08-28'\n", "    result_df, result_df_turn, pull_start_list = track_pullstart_stocks(end_date=end_date, index_turndate=index_turndate, \n", "                                                                        index_peakdate=index_peakdate, industry_list=industry_list, \n", "                                                                        limit_num=80, store_mode=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_ModelFunc import cal_target_pgv\n", "in_target_pgv, out_target_pgv = cal_target_pgv(\n", "    stk_list=['002674.SZ',\n", "                '002178.SZ',\n", "                '603856.SH', '002121.SZ', '600604.SH','600826.SH', '002645.SZ','600173.SH', '002527.SZ',\n", "                '003037.SZ','300195.SZ','300950.SZ','603486.SH','600606.SH',\n", "                '605305.SH', '603680.SH', '002901.SZ',\n", "                '000705.SZ', '603090.SH', '603950.SH','603617.SH', \n", "                '601869.SH', '600774.SH', '600829.SH', '300878.SZ',\n", "                '001360.SZ', '001324.SZ', '002072.SZ', '002757.SZ', '300021.SZ',\n", "                '002639.SZ', '603193.SH',\n", "                '601969.SH', '000735.SZ', '300189.SZ',\n", "                '603052.SH', '600671.SH', '600222.SH',\n", "                '002248.SZ',\n", "                '600610.SH', '603179.SH', '600698.SH', '301076.SZ',\n", "                '601177.SH', '001380.SZ', '002190.SZ',\n", "                '002213.SZ', '603900.SH', '002377.SZ', '002353.SZ', '002354.SZ',\n", "                '002164.SZ', '603319.SH', '000702.SZ', '600503.SH', '603500.SH',\n", "                '000880.SZ', '600666.SH', '600172.SH', '002240.SZ', '002565.SZ',\n", "                '603090.SH', '000014.SZ', '603023.SH', '000006.SZ',\n", "                '601020.SH', '000962.SZ', '603273.SH', '002249.SZ', '603158.SH'], check_date='2025-08-13')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["in_target_pgv = in_target_pgv.sort_values(by='ts_code', ascending=True)\n", "out_target_pgv = out_target_pgv.sort_values(by='ts_code', ascending=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_ModelFunc import cal_target_pgv\n", "in_target_pgv_sigle, out_target_pgv_sigle = cal_target_pgv(stk_list=['002393.SZ'\n", "                                                                     ], check_date='2025-07-15')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.Func_Base import cal_period_ratio\n", "period_return = cal_period_ratio(start_date='2025-01-06', end_date='2025-03-10', mode='Max')\n", "industry_list = ['非银金融']\n", "period_return_indus = period_return.query('industry in @industry_list')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_Func_V7 import get_result_3\n", "result_store = get_result_3(end_date='2025-03-25')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["indus_list = ['机械设备', '基础化工', '煤炭', '电力设备', '有色金属']\n", "result_store_now_firstbreach = result_store.query('industry in @indus_list & NowSec_PRA_FirstBreach_Date==\"2025-03-24\"'\n", "                                                  )[['industry', 'ts_code', 'name', 'PreNow_PeakDate', 'NowSec_PRA_FirstBreach_Date', 'PreNowPeak_PRV_Top3Mean', 'PostNowSec_PGV_MaxRollAvg']]\n", "result_store_sec_firstbreach = result_store.query('industry in @indus_list & SecStart_PRA_FirstBreach_Date==\"2025-03-24\"'\n", "                                                  )[['industry', 'ts_code', 'name', 'Section_PeakDate', 'SecStart_PRA_FirstBreach_Date', 'SectionPeak_PRV_Top3Mean', 'PostSecStart_PGV_MaxRollAvg']]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"ExecuteTime": {"end_time": "2025-06-13T01:34:19.358970Z", "start_time": "2025-06-13T01:32:44.784208Z"}}, "outputs": [], "source": ["# 调用ruptures检测变动点\n", "\n", "import ruptures as rpt\n", "import numpy as np\n", "from function_ai.Func_Base import get_stock_data\n", "from function_ai.DailyGap_Func import get_min_indicators\n", "\n", "code = '600640.SH'\n", "start_date, end_date = '2025-04-08', '2025-06-13'\n", "# stk_indicator = 'turnover'\n", "stk_indicator = 'peak_gap'\n", "\n", "min_period_for_avg = 4 # 用于计算平均值的最小天数\n", "\n", "if  stk_indicator == 'turnover':\n", "    stock_data = get_stock_data(stk_code=code, start_date=start_date, end_date=end_date)\n", "    stock_data = stock_data.set_index('trade_date')\n", "    data = stock_data[stk_indicator].dropna().values.reshape(-1, 1)\n", "    dates_index = stock_data.index\n", "else:\n", "    gap_data = get_min_indicators(stk_code=code, start_date=start_date, end_date=end_date, draw_turnover=False, draw_gap=False)\n", "    gap_data = gap_data.set_index('trade_date')\n", "    data = gap_data[stk_indicator].dropna().values.reshape(-1, 1)\n", "    dates_index = gap_data.index\n", "\n", "\n", "detector = rpt.Dynp(model=\"l2\", min_size=min_period_for_avg).fit(data)\n", "    \n", "result_indices = detector.predict(n_bkps=1)\n", "\n", "# data_variance = np.var(data)\n", "# if np.isclose(data_variance, 0):  # 如果方差接近零\n", "#     data_variance = 1e-10  # 设置一个很小的默认值\n", "\n", "# penalty_value = np.log(len(data)) * data_variance * 0.1  # 这是一个尝试性的启发式值，可能需要调整\n", "# if show_info:\n", "#     print(f\"\\n使用的 penalty 参数: {penalty_value:.2f}\")\n", "\n", "# result_indices = detector.predict(pen=penalty_value)\n", "\n", "if result_indices and result_indices[0] < len(data):\n", "    change_point_array_index = result_indices[0]\n", "\n", "    # 将 numpy 数组索引映射回 DataFrame 的日期索引\n", "    # change_point_array_index 是新段开始的索引，所以对应的日期就是 T 日\n", "    t_date = dates_index[change_point_array_index]\n", "    print(f\"检测到变动起始日期: {t_date}\")\n", "    \n", "    if change_point_array_index >= min_period_for_avg and \\\n", "            (len(data) - change_point_array_index) >= min_period_for_avg:\n", "    \n", "        # 使用检测到的分界点来划分前期和后期数据\n", "        pre_change_data = data[:change_point_array_index].flatten()  # .flatten() 转换为1D\n", "        post_change_data = data[change_point_array_index:].flatten()\n", "\n", "        post_change_days = len(post_change_data)\n", "\n", "        mean_before = np.mean(pre_change_data)\n", "        mean_after = np.mean(post_change_data)\n", "\n", "        absolute_change = mean_after - mean_before\n", "        relative_change_percent = (absolute_change / mean_before) * 100 if mean_before != 0 else np.nan\n", "        \n", "        print(\n", "            f\"\\n前期 ({dates_index[0]} 至 {dates_index[change_point_array_index - 1]}) \"\n", "            f\"{stk_indicator}平均数值: {mean_before:.2f}\")\n", "        print(\n", "            f\"后期 ({dates_index[change_point_array_index]} 至 {dates_index[-1]}) \"\n", "            f\"{stk_indicator}平均数值: {mean_after:.2f}\")\n", "        print(f\"{stk_indicator}绝对变动幅度: {absolute_change:.2f}\")\n", "        if not np.isnan(relative_change_percent):\n", "            print(f\"{stk_indicator}相对变动幅度: {relative_change_percent:.2f}\")\n", "        else:\n", "            print(\"前期平均数值为0，无法计算相对变动幅度。\")\n", "\n", "else:\n", "    print('未检测到变动起始日期')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gapvalue_column_names = ['PostSecStart_MaxPeakGap', 'PostSecStart_MaxPGV',\n", "                             'PostSecStart_MaxValleyGap', 'PostSecStart_MaxValleyGapValue',\n", "                             'PostSecStart_MedianPeakGap', 'PostSecStart_MedianPeakGapValue',\n", "                             'PostSecStart_MedianValleyGap', 'PostSecStart_MedianValleyGapValue',\n", "                             'PostSecStart_MinPeakGap', 'PostSecStart_MinValleyGap',\n", "                             'PostSecPeak_MaxValleyGapValue', 'PostSecPeak_MaxPeakGapValue',\n", "                             'PostSecPeak_MinValleyGapValue', 'PostSecPeak_MinPeakGapValue',\n", "                             'PostSecPeak_MedianValleyGapValue', 'PostSecPeak_MedianPeakGapValue',\n", "                             'PostNowSec_MaxPeakGap', 'PostNowSec_MaxPeakGapValue',\n", "                             'PostNowSec_MaxValleyGap', 'PostNowSec_MaxValleyGapValue',\n", "                             'PostNowSec_MedianPeakGap',\n", "                             'PostNowSec_MedianPeakGapValue', 'PostNowSec_MedianValleyGapValue',\n", "                             'PostNowSec_MinPeakGap',\n", "                             'PostSecBottom_MaxPeakGap', 'PostSecBottom_MaxValleyGap',\n", "                             'Peak2Sec_MaxPeakGap', 'Peak2Sec_MaxValleyGap',\n", "                             'Peak2Sec_MaxPeakGapValue', 'Peak2Sec_MaxValleyGapValue',\n", "                             'Now_PeakGap', 'Now_PeakGapValue',\n", "                             'Now_ValleyGap', 'Now_ValleyGapValue',\n", "                             'Recent3Day_MaxPeakGapValue', 'Recent3Day_MaxValleyGapValue',\n", "                             'Recent3Day_MinPeakGapValue', 'Recent3Day_MinValleyGapValue',\n", "                             'Peak3Day_MaxPeakGapValue',\n", "                             'PostSecStart_PeakGap_HighQuntl', 'PostSecStart_PeakGapValue_HighQuntl',\n", "                             'PostSecStart_PeakGap_LowQuntl', 'PostSecStart_PeakGapValue_LowQuntl',\n", "                             'PostSecStart_ValleyGap_HighQuntl', 'PostSecStart_ValleyGapValue_HighQuntl',\n", "                             'PostSecStart_ValleyGap_LowQuntl', 'PostSecStart_ValleyGapValue_LowQuntl',\n", "                             'PostTurn_MedianPeakGapValue', 'PostTurn_PeakGapValue_HighQuntl',\n", "                             'PostTurn_MaxPeakGapValue', 'PostTurn_MedianValleyGapValue',\n", "                             'PostTurn_ValleyGapValue_HighQuntl', 'PostTurn_MaxValleyGapValue',\n", "                             'Peak2Sec_PeakGap_HighQuntl', 'Peak2Sec_PeakGap_LowQuntl',\n", "                             'Peak2Sec_ValleyGap_HighQuntl', 'Peak2Sec_MedianValleyGapValue',\n", "                             'Peak2Sec_PeakGapValue_HighQuntl', 'Peak2Sec_ValleyGapValue_HighQuntl',\n", "                             'PreNowSec_MinPeakGap', 'PreNowSec_MinPeakGap_Date',\n", "                             'PreNowSec_MinValleyGap', 'PreNowSec_MaxValleyGap', 'PreNowSec_MaxValleyGapValue',\n", "                             'PreNowSec_MedianPeakGap', 'PreNowSec_MedianValleyGap',\n", "                             'NowSec_Recent3DValley_Over_HighQuntl_Num',\n", "                             'NowSec_Recent3D_MaxValleyGapValue',\n", "                             'PreTurnPeak_Sec_MaxPeakGapValue', 'PreSecPeak_Sec_MaxPeakGapValue',\n", "                             #    'PostSecStart_MaxPeakGap2Yesd_Ratio',\n", "                             #    'PostSecStart_PeakGapNow2Med_Ratio',\n", "                             #    'PostSecStart_UndMed_SecondDate',\n", "                             #    'PostSecStart_PeakGapValue_TrackSignal', 'PostSecPeak_PeakGapUndHighQuntl_Signal',\n", "                             #    'PostSecPeak_Over_Btw_Num', 'PostPreNowSec_Over_Btw_Num',\n", "                             #    'PostSecPeak_Over_HighQuntl_Num', 'PostPreNowSec_Over_HighQuntl_Num',\n", "                             #    'PostNowSec_Over_HighQuntl_Num', 'PostNowSec_Over_Btw_Num',\n", "                             #    'Recent3DValley_Over_HighQuntl_Num', 'Recent3DPeak_Over_HighQuntl_Num',\n", "                             #    'Recent5DValley_Over_Median_Num', 'Recent5DPeak_Over_Median_Num',\n", "                             #    'PostSecPeak_Rank3_ValleyGapValue',\n", "                             #    'PostSecPeak_DownConsecutive_AvgLastDays', 'PostSecMaxRollAvg_DownConsecutive_AvgLastDays',\n", "                             #    'PostNowSec_UpConsecutive_AvgLastDays', 'PostSecStart_UpConsecutive_AvgLastDays',\n", "                             #    'PostPreSecPeak_DownConsecutive_AvgLastDays',\n", "                             'Now_PostSecPeak_VGV_Desc_Rank', 'Now_PostSecPeak_VGV_MaxCoverDays',\n", "                             'Now_PostSecStart_VGV_MaxCoverDays',\n", "                             'Now_PostNowSec_PGV_Desc_Rank', 'Now_PostNowSec_PGV_MaxCoverDays',\n", "                             'PostSecPeak_MaxVGV2Now_LastDays',\n", "                             'PostSecPeak_VGV_MinRollAvg2MaxRatio', 'PostSecPeak_VGV_NowRollAvg2MaxRatio',\n", "                             'PostSecPeak_VGV_MinRollAvg2Now_LastDays', 'Now_PostSecPeak_VGV_RollAvg_Asc_Rank',\n", "                             'Now_PostSecPeak_VGV_RollAvg_MinCoverDays',\n", "                             'PostSecStart_PGV_MaxRollAvg2MinRatio', 'PostSecStart_PGV_MaxRollAvg2MeanRatio',\n", "                             'PostSecStart_PGV_MaxRollAvg2Now_LastDays',\n", "                             'PostSecStart_PGV_NowRollAvg2MinRatio', 'Now_PostSecStart_PGV_RollAvg_Desc_Rank',\n", "                             'Now_PostSecStart_PGV_RollAvg_Asc_Rank',\n", "                             'Now_PGV_RollAvg_CoverDays', 'PostSecStart_PGV_MinRollAvg2Now_LastDays',\n", "                             'PostSecPeak_PGV_MinRollAvg2MaxRatio', 'PostSecPeak_PGV_NowRollAvg2MaxRatio',\n", "                             'PostSecPeak_PGV_MinRollAvg2Now_LastDays', 'Now_PostSecPeak_PGV_RollAvg_Asc_Rank',\n", "                             'Now_PostSecPeak_PGV_RollAvg_MinCoverDays',\n", "                             'PostSecStart_VGV_MaxRollAvg2MinRatio',\n", "                             'PostSecStart_VGV_NowRollAvg2MinRatio', 'Now_PostSecStart_VGV_RollAvg_Desc_Rank',\n", "                             'Now_VGV_RollAvg_CoverDays',\n", "                             'PostSecPeak_PGV_MinRollAvg', 'PostSecPeak_PGV_MaxRollAvg',\n", "                             'Recent2Day_MeanPeakGapValue', 'Recent2Day_MeanValleyGapValue',\n", "                             'Recent9Day_MaxValleyGapValue', 'Recent9Day_MaxPeakGapValue',\n", "                             'PostSecStart_PGV_MaxRollAvg', 'PostSecStart_PGV_MinRollAvg',\n", "                             'PostSecStart_VGV_MaxRollAvg',\n", "                             'PostSec_Peak_PGV_RollAvg', 'PostSec_Peak_PGV_RollAvg_Desc_Rank',\n", "                             'PostPreNowBottom_PGV_MinRollAvg', 'Now_PostPreNowBottom_PGV_RollAvg_Asc_Rank',\n", "                             'Now_PGV_RollAvg', 'Now_VGV_RollAvg', 'PostSecPeak_PGV_MinRollAvg_Date',\n", "                             'PGV_Post_MinRollAvg_MaxVGV_CoverDays', 'PostNowSec_PGV_MinRollAvg',\n", "                             'PostNowSec_PGV_MaxRollAvg', 'PostNowSec_PGV_MaxRollAvg_Date',\n", "                             'Recent3Day_PGV_MinRollAvg', 'Recent3Day_PGV_MaxRollAvg',\n", "                             'Recent3Day_PGV_MeanRollAvg',\n", "                             'PostSecMaxRollAvg_PGV_MinRollAvg', 'PostSecMaxRollAvg_PGV_MinRollAvg_Date',\n", "                             'PostSecMaxRollAvg_PGV_MinRollAvg2MaxRatio',\n", "                             'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays',\n", "                             'PostSecMaxRollAvg_PGV_PostMin_MaxRollAvg2Max_Ratio',\n", "                             'PreSecMaxRollAvg_PGV_MinRollAvg', 'Post2Pre_PGV_MinRollAvg_Ratio',\n", "                             'PostSecMaxRollAvg_PGV_RollAvg_Now2Min_Ratio', 'PreSecMaxRollAvg_PGV_MaxRollAvg2MinRatio',\n", "                             'PreSecMaxRollAvg_PGV_MeanRollAvg', 'PostSecPeak_PGV_MaxRollAvg_Quarter',\n", "                             'PostNowSec_PGV_MeanRollAvg', 'PostSecStart_PGV_MeanRollAvg',\n", "                             'PostSecStart_PGV_MaxRollAvg_Date', 'PostSecStart_PeakDate_Diff',\n", "                             'AftPreTurnPeak_PGV_MinRollAvg', 'AftPreTurnPeak_PGV_MinRollAvg2MaxRatio',\n", "                             'AftPreTurnPeak_PGV_MinRollAvg2Now_LastDays', 'PreNowPeak_PGV_MeanRollAvg',\n", "                             'PostSecMaxRollAvg_MinPGV', 'PostSecMaxRollAvg_MinPGV2Now_LastDays',\n", "                             'PostSecMaxRollAvg_MinPGV2MaxRatio', 'Pre3Date_PeakGapValue', 'PostSecStart_MaxPGV_Date',\n", "                             'PostMinRollAvg_RiseTrend_Prop', 'PostSecStart_RiseTrend_Prop', 'MinRollAvg_NowSec_Diff',\n", "                             'PostSecPeak_DropTrend_Prop', 'PostPreNowPeak_DropTrend_Prop', 'MinRollAvg_Truncated_Diff',\n", "                             'Now_PGVRollAvg_DownCount', 'Now_PGVRollAvg_UpCount',\n", "                             'BfMinRollAvg_PGVRollAvg_DownCount', 'PostSec_MaxRollAvg_PeakDate_Diff',\n", "                             'PostSecPeak_PGV_MeanRollAvg_TruncatedValue',\n", "                             'SecConcave_PGV_MinRollAvg', 'SecConcave_PGV_MinRollAvg2MeanRatio',\n", "                             'SecConcave_PGV_MinRollAvg_Date', 'SecConcave_PGV_MinRollAvg2Now_LastDays',\n", "                             'Peak2Sec_PGV_MinRollAvg', 'Peak2Sec_PGV_MinRollAvg_Date',\n", "                             'Peak2Sec_PGV_MinRollAvg2MeanRatio',\n", "                             'PostSecPeak_DownConsecutive_Num', 'PostSecMaxRollAvg_DownConsecutive_Num',\n", "                             'PostNowSec_UpConsecutive_Num', 'PostSecStart_UpConsecutive_Num',\n", "                             'PreNowSec_PGV_MeanRollAvg', 'PostNowSec_MaxPeakGapValue_Date',\n", "                             'DownConsecutive2Now_LastDays', 'UpConsecutive2Now_LastDays',\n", "                             'PostNow2PostSec_PGV_MeanRollAvg_Ratio', 'PreNow2PostSec_PGV_MeanRollAvg_Ratio',\n", "                             'DownConsecutive_PGVRollAvg_DiffRatio', 'UpConsecutive_PGVRollAvg_DiffRatio',\n", "                             'DownConsecutive_SumRatio', 'UpConsecutive_SumRatio',\n", "                             'DownConsecutive_Start_PGVRollAvg', 'UpConsecutive_Start_PGVRollAvg',\n", "                             'Break_DownSecutiveStart_First2Now_LastDays', 'Break_UpSecutiveStart_First2Now_LastDays',\n", "                             'DownConsecutive_Last2Break_LastDays', 'UpConsecutive_Last2Break_LastDays',\n", "                             'PostNow2PreNow_PGV_MeanRollAvg_Ratio',\n", "                             'PostSecMaxRollAvg_PGV_MaxRollAvg2Min_SumRatio',\n", "                             'PostSecPeak_DownConsecutive_MaxLastDays', 'PostSecMaxRollAvg_DownConsecutive_MaxLastDays',\n", "                             'PostNowSec_UpConsecutive_MaxLastDays', 'PostSecStart_UpConsecutive_MaxLastDays',\n", "                             'PostPreSecPeak_DownConsecutive_Num',\n", "                             'PostPreSecPeak_DownConsecutive_MaxLastDays',\n", "                             'PostPreNowBottom_UpConsecutive_Num', 'PostPreNowBottom_UpConsecutive_MaxLastDays',\n", "                             'PostPreNowBottom_DownConsecutive_Num', 'PostPreNowBottom_DownConsecutive_MaxLastDays',\n", "                             'PreNowPeak_PGV_MinRollAvg2Now_LastDays', 'PreNowPeak_PGV_MinRollAvg2MaxRatio',\n", "                             'PostSecMaxRollAvg_PGV_MinRollAvg2Now_SumRatio', 'PostSecPeak_PGV_MinRollAvg2Now_SumRatio',\n", "                             'Peak2Sec_PGV_MinRollAvg2Sec_LastDays', 'Peak2Sec_PGV_MinRollAvg2Sec_SumRatio',\n", "                             'PreNowPeak_PGV_MaxRollAvg', 'PreNowSec_PGV_MaxRollAvg',\n", "                             'Peak2Sec_PGV_MaxRollAvg', 'Peak2Sec_PGV_MeanRollAvg',\n", "                             'Peak2Sec_PGV_RollAvg_LowQuntl', 'PGVRollAvg_Now2PreSecLowQuntl_Ratio',\n", "                             'PostSecStart_PGV_RollAvg_LowQuntl',\n", "                             'PostSecStart_PGV_RollAvg_HighQuntl', 'PostSec2PreSec_PGV_MaxRollAvg_Ratio',\n", "                             'PGVRollAvg_Now2PostSecLowQuntl_Ratio', 'PGVRollAvg_Now2PostSecHighQuntl_Ratio',\n", "                             'PostSecMaxRollAvg_PGV_MeanRollAvg',\n", "                             'Peak2Sec_PGV_RollAvg_VolRange', 'PostSecStart_PGV_RollAvg_VolRange',\n", "                             'PostSecMaxRollAvg_PGV_RollAvg_VolRange',\n", "                             'PostSec_RiseSec10Days_PGV_MaxRollAvg', 'PreNowSec_DropSec10Days_PGV_MinRollAvg',\n", "                             'Is_Bottom_Reversal', 'Is_Expanding', 'Is_More_Volatile',\n", "                             'PostSecPeak_MaxTO_Eff', 'PostSecPeak_MaxTO_Eff_Date',\n", "                             'PostSecPeak_MaxTO_Eff2Now_LastDays',\n", "                             'PostSecPeak_TO_Eff_Max2Min_Ratio',\n", "                             'Now_TO_Eff',\n", "                             'PostPreNowPeak_MaxTO_Eff', 'PostPreNowPeak_MaxTO_Eff_Date',\n", "                             'PostPreNowPeak_MaxTO_Eff2Now_LastDays',\n", "                             'PostPreNowPeak_MaxTO_Eff_CoverDays', 'PostPreNowPeak_TO_Eff_Max2Min_Ratio',\n", "                             'PostSec_MinTO_Eff', 'PostSec_MinTO_Eff_Date', 'PostSec_MinTO_Eff2Now_LastDays',\n", "                             'PostSec_MinTO_Eff_CoverDays', 'PostSec_TO_Eff_Min2Max_Ratio',\n", "                             'PostPreNowPeak_U2D_MaxTO_Eff', 'PostPreNowPeak_U2D_MaxTO_Eff_Date',\n", "                             'PostPreNowPeak_U2D_MaxTO_Eff2Now_LastDays', 'PostPreNowPeak_U2D_MaxTO_Eff_CoverDays',\n", "                             'Eff_Recent2Previous_MinChange',\n", "                             'Is_LowBound_Oscillation',\n", "                             'Recent_EffPeak_ChangeRate', 'Recent_EffPeak_ChangeRate_Percentile',\n", "                             'PostPreNowPeak_MaxTO_Eff_Band', 'PostSec_MinTO_Eff_Band',\n", "                             'PostPreNowPeak_U2D_MaxTO_Eff_Band',\n", "                             'Latest_TO_Eff_Band',\n", "                             'Eff_Recent2Previous_Change',\n", "                             'Eff_Avg_Peak_Period', 'Eff_Avg_Valley_Period',\n", "                             'Eff_Peak_Intensity', 'Eff_Valley_Intensity',\n", "                             'Days_From_Last_Peak', 'Days_From_Last_Valley',\n", "                             'PostSecMaxRollAvg_PGV_MinRollAvg_Band', 'PostSecMaxRollAvg_PGV_MinRollAvg_CoverDays',\n", "                             'PostSecStart_PGV_MaxRollAvg_Band', 'PostSecStart_PGV_MaxRollAvg_CoverDays',\n", "                             'PostNowSec_PGV_MaxRollAvg_Band', 'PostNowSec_PGV_MaxRollAvg_CoverDays',\n", "                             'Now_PGV_RollAvg_Rank',\n", "                             'PGV_RollAvg_Recent2Previous_Change',\n", "                             'PGV_RollAvg_NowSec2Previous_Change',\n", "                             'PGV_RollAvg_VolatilityRatio',\n", "                             'Efficiency_VolatilityRatio', 'U2D_Efficiency_VolatilityRatio',\n", "                             'PostPreNowPeak_U2D_MinTO_Eff', 'PostPreNowPeak_U2D_MinTO_Eff_Date',\n", "                             'PostPreNowPeak_U2D_MinTO_Eff2Now_LastDays',\n", "                             'PostPreNowPeak_U2D_MinTO_Eff_CoverDays',\n", "                             'Eff_Latest2Pre3Mean', 'UpEff_Latest2Pre3Mean',\n", "                             'PostPreNow_MaxUpEff_Date', 'PostPreNow_MaxUpEff2Now_LastDays',\n", "                             'Latest_Eff_Peak_Date', 'Latest_Eff_Peak2Now_Days',\n", "                             'Latest_UpEff_Peak_Date', 'Latest_UpEff_Peak2Now_Days',\n", "                             'PostPreNow_UpEff_MaxTO_Eff_Band', 'PostNowSec_PGVRollAvg_CoverDrop_Diff',\n", "                             'PostNowSec_PGVRollAvg_CoverDrop_DiffRatio',\n", "                             'PreTurnPeak_PRV_Top3Mean', 'SectionPeak_PRV_Top3Mean',\n", "                             'SectionStart_PRV_Top3Mean', 'NowSec_PRV_Top3Mean',\n", "                             'PreNowSec_PRV_Top3Mean', 'PreNowPeak_PRV_Top3Mean',\n", "                             'Turn_PRV_Top3Mean', 'PostTurnPeak_PRV_Top3Mean', 'PostSecPeak_PRV_Top3Mean',\n", "                             'PostSecPeak_PRV_Top3Mean_CoverDays',\n", "                             'PostSecPeak_PRA2Close_CoverDays_Diff',\n", "                             'Recent3Day_PRA_SectionStart_Rank', 'Recent3Day_PRA_NowSec_Rank',\n", "                             'NowSec_PGV_MaxRollAvg_UpCoverDays', 'NowSec_PGV_MinRollAvg_DownCoverDays',\n", "                             'NowSec_PGV_UpCoverPeriod_Max2Min_DiffRatio', \n", "                             'NowSec_PGV_DownCoverPeriod_Min2Max_DiffRatio',\n", "                             'NowSec_PRA2Close_CoverDays_Diff',\n", "                             'SecStart_PGV_MaxRollAvg_UpCoverDays', 'SecStart_PGV_MinRollAvg_DownCoverDays',\n", "                             'SecStart_PGV_UpCoverPeriod_Max2Min_DiffRatio',\n", "                             'SecStart_PGV_DownCoverPeriod_Min2Max_DiffRatio',\n", "                             'SecStart_PRA2Close_CoverDays_Diff',\n", "                             'NowSec_MaxPRA_Percentile_PostTurn',\n", "                             'NowSec_MinPRA_Percentile_PostTurn',\n", "                             'NowSec_MaxPRA_Percentile_PostSectionPeak',\n", "                             'NowSec_MinPRA_Percentile_PostSectionPeak',\n", "                             'PostSecStart_MaxPRA_Percentile_PostSectionPeak',\n", "                             'PostSecStart_MaxPRA_Percentile_PostTurn',\n", "                             'SecPeak2NowSec_PRA_UpBand',\n", "                             'SecPeak2NowSec_PRA_LowBand',\n", "                             'Turn2NowSec_PRA_UpBand',\n", "                             'Turn2NowSec_PRA_LowBand',\n", "                             'Latest_EffPeak2NowSec_Diff',\n", "                            #  'PostPreNowPeak_PRA_MaxRate',\n", "                            #  'PostPreNowPeak_PRA_MaxRate_Date',\n", "                            #  'PostPreNowPeak_PRA_MaxRate_Date2NowSec_Diff',\n", "                            #  'PostPreNowPeak_MaxRate_PRA_Percentile',\n", "                             'Now_PRA_Percentile_PostSectionPeak',\n", "                             'PostNowSec_PRA_MaxRate',\n", "                             'PostNowSec_PRA_MaxRate_Date',\n", "                             'PostNowSec_MaxRate2Now_LastDays',\n", "                             'PostNowSec_NowSec2MaxRate_LastDays',\n", "                             'PostSecStart_PRA_MaxRate',\n", "                             'PostSecStart_PRA_MaxRate_Date',\n", "                             'Now_PRA_Rate',\n", "                             'PostNowSec_PRA_MaxRate_BreachCount',\n", "                             'PostSecStart_PRA_MaxRate_BreachCount',\n", "                             'PostNowSec_MaxRate_PRA_Percentile',\n", "                             'PostNowSec_MaxRate_Post2Pre_DiffRatio',\n", "                             'SecStart_PRA_FirstBreach_Date',\n", "                             'SecStart_FirstBreach2Now_LastDays',\n", "                             'SecStart_PRA_LastBreach_Date',\n", "                             'SecStart_LastBreach2Now_LastDays',\n", "                             'SecStart_PRA_Breach_Count',\n", "                             'SecStart_PRA_LastBreach_ContinuousDays',\n", "                             'NowSec_PRA_FirstBreach_Date',\n", "                             'NowSec_FirstBreach2Now_LastDays',\n", "                             'NowSec_PRA_LastBreach_Date',\n", "                             'NowSec_LastBreach2Now_LastDays',\n", "                             'NowSec_PRA_Breach_Count',\n", "                             'NowSec_PRA_LastBreach_ContinuousDays',\n", "                             'PostSecStart_MaxPGVDate2Now_LastDays',\n", "                             'PostSecStart_MaxVGV',\n", "                             'PostSecStart_MaxVGV_Date',\n", "                             'PostSecStart_MaxVGVDate2Now_LastDays',\n", "                             'PostSecStart_PGV_Max2Mean_Ratio',\n", "                             'PostSecStart_VGV_Max2Mean_Ratio',\n", "                             'PostSecStart_PGV_Now2Mean_Ratio',\n", "                             'PostNowSec_PGV_Now2Mean_Ratio',\n", "                             'PostNowSec_PGV_Max2Mean_Ratio',\n", "                             'PGV_Now2Pre3Days_Ratio',\n", "                             'PostSec_PGV_TurnP_Date',\n", "                             'PostSec_PGV_TurnP_AbsChange',\n", "                             'PostSec_PGV_TurnP_RelaChange',\n", "                             'PostSec_PGV_PostTurnP_LastDays',\n", "                             'Peak2Sec_PGV_TurnP_Date',\n", "                             'Peak2Sec_PGV_TurnP_AbsChange',\n", "                             'Peak2Sec_PGV_TurnP_RelaChange',\n", "                             'Peak2Sec_PGV_PostTurnP_LastDays',\n", "                             'Cal_Date']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gapvalue_column_names_adj = ['ts_code'] + gapvalue_column_names"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.StkPick_Func_V7 import get_result_3\n", "result_store = get_result_3(start_date='2024-05-20', end_date='2025-06-20')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_store['Cal_Date'].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.Func_Base import get_stock_info\n", "from function_ai.StkQuota_Func_V7 import set_resultindexs\n", "import pandas as pd\n", "Result_Common = get_stock_info()\n", "Result_Common = Result_Common[['ts_code', 'name', 'industry', 'area']]\n", "common_column_names, gapvalue_column_names = set_resultindexs()\n", "# column_names = common_column_names + gapvalue_column_names\n", "Result_Common = pd.concat([Result_Common, pd.DataFrame(columns=common_column_names)], sort=False, axis=1)\n", "Result_GapValue = pd.concat([Result_Common[['ts_code']], pd.DataFrame(columns=gapvalue_column_names)], sort=False, axis=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from function_ai.Func_Base import get_stock_data\n", "stk_data = get_stock_data(stk_code='002691.SZ', start_date='2024-11-19', end_date='2025-04-28')\n", "stk_data = stk_data.set_index('trade_date')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stk_data['mean_price'] = round((stk_data['close'] + stk_data['open'])/2, 3)\n", "stk_data['mean_close_rollavg'] = round(stk_data['close'].rolling(window=3, closed='left').mean(), 3)\n", "stk_data['over_movavg'] = stk_data.apply(lambda x: 1 if x['mean_price']>x['mean_close_rollavg'] else 0, axis=1)\n", "stk_data['mean2movavg_rate'] = round((stk_data['mean_price'] / stk_data['mean_close_rollavg'] - 1) * 100, 3)\n", "[stk_data.loc['2025-01-06':'2025-02-13', 'mean2movavg_rate'].max(), stk_data.loc['2025-02-13':'2025-04-09', 'mean2movavg_rate'].min(), \n", "stk_data.loc['2025-01-06':'2025-02-13', 'mean2movavg_rate'].idxmax(), stk_data.loc['2025-02-13':'2025-04-09', 'mean2movavg_rate'].idxmin()]\n", "\n", " "]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 2}