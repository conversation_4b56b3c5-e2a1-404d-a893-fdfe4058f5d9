
import logging
# from turtle import st
from unittest import result
from arrow import now
import numpy as np
import pandas as pd
import pdb
from sqlalchemy import create_engine, inspect, text
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
from multiprocessing import Pool, cpu_count
from functools import partial
from tqdm import tqdm
import os
import sys

# 获取项目根目录路径
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from function_ai.DailyGap_Func import get_min_indicators
from machine_learn.Func_MacinLn import stkpick_model_predict
from function_ai.swindex_funcs import get_swindex_data
from function_ai.StkQuota_Func_V7 import calculate_deviation, cal_trend_wls, cal_trend_consecutive
from function_ai.StkPick_Func_V7 import get_result_3, conf_nowdiff, cal_swindex_state
from function_ai.Func_Base import get_trade_date, get_stock_data, cal_period_ratio, get_stock_info, get_index_data

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# @contextmanager
# def get_db_connection():
#     """创建数据库连接的上下文管理器"""
#     import config.config_Ali as config
#     conf = config.configModel()
#     engine = create_engine(
#         'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
#             conf.DC_DB_PORT) + '/stocksfit')
#     try:
#         conn = engine.connect()
#         yield conn
#     finally:
#         conn.close()
#         engine.dispose()


def get_stock_pick_result(pick_date=None, pull_startdate=None,
                          bottom_industry=None, store_mode=False):
    """从stk_results_modelrf数据表获取指定日期范围内的股票，并根据入场时机进行筛选。

    主要功能:
    - 从数据表获取指定日期范围内的股票数据
    - 根据市场指数转折点日期作为基准进行筛选
    - 筛选出两类股票:强势拉升型和缓行型
    - 对筛选结果进行Now_SecDate确认
    - 输出符合条件的跟踪股票名单

    参数:
        pickdate (str): 指定筛选日期
        pull_startdate (str): 区间起始日期,用于计算区间涨跌幅
        bottom_industry (list): 底部行业列表,默认为None
        store_mode (bool): 是否存储结果到数据库,默认False

    返回:
        tuple: 包含三个DataFrame:
            - result_sec_pick: 主要筛选结果,包含符合条件的股票及其指标
            - result_bottom_pull: 底部拉升型股票筛选结果
            - result_potential_strong: 潜在强势股票筛选结果

    更新说明:
        20241214:
        - 更改为筛选所有品种
        - 按照最新日期指标确认Now_SecDate后纳入输出结果
        - 优化了筛选逻辑和输出结果的组织方式
    """
    trade_dates = get_trade_date()

    ts_list = get_stock_info()['ts_code'].unique().tolist()
    if pull_startdate is None:
        print('缺失指数区间起始日期pull_startdate!')
        return
    if bottom_industry is None:
        bottom_industry = []

    result_origin = get_result_3(end_date=pick_date, stk_list=ts_list)
    if 'id' in result_origin.columns:
        result_origin = result_origin.drop('id', axis=1)
    min_secdate = pd.to_datetime(
        result_origin['PreNow_PeakDate']).min().strftime('%Y-%m-%d')
    stock_data = get_stock_data(
        start_date=min_secdate, end_date=pick_date, stk_code=ts_list)

    result_origin['Now_SecDiff'] = result_origin.apply(
        lambda fn: conf_nowdiff(
            fn['Now_SecDate'], pick_date, trade_dates, fn['ts_code'], stock_data),
        axis=1)

    period_ratio = cal_period_ratio(
        start_date=pull_startdate, end_date=pick_date, mode='Max')
    result_origin = pd.merge(result_origin, period_ratio[['ts_code', 'Max_SumRatio']],
                             on='ts_code', how='left')

    # 筛选强势品种品种，处于拉升Section区段
    # 分为两种情况: slow_decline(缓步下行) 和 narrow_range(窄幅横盘)
    drop_avg = -1.5

    def get_potential_type(row):
        try:
            # slow_decline: 缓步下行
            if (row['Peak2Sec_LastDays'] is not None and row['Peak2Sec_LastDays'] > 20 and
                row['Peak2Sec_AvgRatio'] is not None and row['Peak2Sec_AvgRatio'] > drop_avg and
                row['Peak2Sec_Sec_Max_LastDays'] is not None and row['Peak2Sec_LastDays'] is not None and
                row['Peak2Sec_Sec_Max_LastDays'] >= min(20, row['Peak2Sec_LastDays']) and
                row['Peak2Sec_PGV_MinRollAvg2MeanRatio'] is not None and row['Peak2Sec_PGV_MinRollAvg2MeanRatio'] <= 0.5 and
                row['Peak2Sec_PGV_MinRollAvg2Sec_LastDays'] is not None and row['Peak2Sec_PGV_MinRollAvg2Sec_LastDays'] < 15 and
                    row['PostSecStart_RiseRatio'] is not None and row['PostSecStart_RiseRatio'] < 25):
                return 'slow_decline'
            # narrow_range: 窄幅横盘
            elif (row['SecConcave_LastDays'] is not None and row['SecConcave_LastDays'] > 40 and
                  row['SecConcave_RatioBand'] is not None and row['SecConcave_RatioBand'] < 40 and
                  row['SecConcave_TO_Sum'] is not None and row['SecConcave_TO_Sum'] > 80 and
                  row['SecConcave_PGV_MinRollAvg2Now_LastDays'] is not None and row['SecConcave_PGV_MinRollAvg2Now_LastDays'] < 15 and
                  row['PostSecStart_RiseRatio'] is not None and row['PostSecStart_RiseRatio'] < 25):
                return 'narrow_range'
            return 'none'
        except:
            return 'none'

    result_origin['Potential_Strong'] = result_origin.apply(
        get_potential_type, axis=1)

    if 'id' in result_origin.columns:
        result_origin = result_origin.drop(columns=['id'], axis=1)

    # 将指定列移到最前面
    cols_to_front = ['Potential_Strong',
                     'PostSecStart_Over5MovAvg_Prop', 'Peak2Sec_Und5MovAvg_Prop']
    other_cols = [
        col for col in result_origin.columns if col not in cols_to_front]
    result_origin = result_origin[cols_to_front + other_cols]

    # 筛选弱势行业的强势品种
    result_bottom_pull = result_origin.query('Now_SecDiff<=1 & '
                                             'industry in @bottom_industry & '
                                             '((PostSecStart_PGV_MaxRollAvg2Now_LastDays>=3 & '
                                             '(PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays>3 | '
                                             'PostSecStart_PGV_MaxRollAvg2Now_LastDays>15) & '
                                             '(PostSecStart_PeakDate_Diff>3 | '
                                             'PostSecStart_PGV_MaxRollAvg2MeanRatio<5) & '
                                             'PostSecPeak2Now_LastDays>=1) | '
                                             'Section_StartDate==Now_SecDate) & '
                                             'Now_PGVRollAvg_DownCount>=3'
                                             ).sort_values(
        by=['Cal_Date', 'Max_SumRatio'], ascending=[False, False]).groupby(['Cal_Date', 'industry']).head(15).copy()

    indus_maxsum = result_bottom_pull.groupby(
        'industry')['Max_SumRatio'].mean()
    print('行业MaxSum均值排名：\n', indus_maxsum.sort_values(ascending=False))

    result_bottom_pull['Track_Categ'] = 'Track_Bottom_Pull'

    # RollAvgDown转折点
    result_sec_turn = result_origin.query(
        'DownConsecutive2Now_LastDays<=2 & '
        'Now_SecDiff<=1 & '
        'PostSecPeak_DownConsecutive_Num>=2 & '
        'PreNow2PostSec_PGV_MeanRollAvg_Ratio>1'
    ).sort_values(by=['Cal_Date', 'PreNow2PostSec_PGV_MeanRollAvg_Ratio'], ascending=[False, False]).copy()

    result_sec_turn['Track_Categ'] = 'Track_DownCount_Turn'

    # 上行区段RollAvgDown入场点
    result_sec_pull = result_origin.query('DownConsecutive2Now_LastDays==0 & '
                                          'PostNowSec_LastDays>=Now_PGVRollAvg_DownCount & '
                                          'PostNow2PostSec_PGV_MeanRollAvg_Ratio>1 & '
                                          'PostNowSec_AvgRatio<2 & '
                                          'Section_StartDate<Now_SecDate & '
                                          'PostNowSec_PeakDate==Cal_Date & '
                                          'PostNowSec_UpConsecutive_Num == 1'
                                          ).sort_values(by=['Cal_Date', 'PostNow2PreNow_PGV_MeanRollAvg_Ratio'],
                                                        ascending=[False, False]).copy()
    result_sec_pull['Track_Categ'] = 'Track_PGVAvg_RisePull'

    result_sec_pick = pd.concat(
        [result_sec_pull, result_sec_turn], ignore_index=True, join='outer')
    if 'id' in result_sec_pick.columns:
        result_sec_pick.drop(columns=['id'], axis=1, inplace=True)

    if store_mode:
        store_quota = pd.concat(
            [result_sec_pick, result_bottom_pull], ignore_index=True, join='outer')
        check_date = store_quota['Cal_Date'].max()
        import config.config_Ali as config
        conf = config.configModel()
        engine = create_engine(
            'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                conf.DC_DB_PORT) + '/stocksfit')
        store_sql = f"""select * from stocksfit.stkpick_turntrack where Cal_Date='{check_date}'"""
        turntrack_storedata = pd.read_sql(store_sql, engine)
        Sesstion = sessionmaker(bind=engine)
        session = Sesstion()
        if len(turntrack_storedata) > 0:
            try:
                delete_sql = text(
                    "delete from stocksfit.stkpick_turntrack where Cal_Date= :end_date")
                with session.begin():
                    session.execute(delete_sql, {"end_date": check_date})
                session.commit()
            except Exception as e:
                session.rollback()
                print('An error occured:', e)
        try:
            pd.io.sql.to_sql(store_quota, 'stkpick_turntrack', engine, index=False, schema='stocksfit',
                             if_exists='append')
            print(check_date, '筛选数据已存储', '\n已存储股票条目：', len(store_quota))
        except:
            pdb.set_trace()
        session.close()
        engine.dispose()
    result_totalmv = result_origin.query('industry in @bottom_industry'
                                         ).sort_values(by='Total_MV', ascending=False
                                                       ).groupby('industry').head(20).copy()
    result_sec_pick = filter_stocks_by_bottom_industries(result_sec_pick, end_date=pick_date,
                                                         trade_dates=trade_dates, indus_num=5)
    result_bottom_pull = filter_stocks_by_bottom_industries(result_bottom_pull, end_date=pick_date,
                                                            trade_dates=trade_dates, indus_num=5, print_indus=False)
    # 为result_totalmv增加急跌恢复日期字段
    result_totalmv['DropRecov_Date'] = None
    for idx, row in result_totalmv.iterrows():
        stk_data = stock_data[stock_data['ts_code'] == row['ts_code']].copy()
        stk_data = stk_data.set_index('trade_date', drop=False)
        stk_ratio = stk_data['close'].pct_change() * 100
        if row['PreNow_PeakDate'] is not None and row['Now_SecDate'] is not None:
            stk_ratio_peak = stk_ratio.loc[row['PreNow_PeakDate']:row['Now_SecDate']]
            if sum(stk_ratio_peak < -1.5) > 0:
                DropDate = stk_ratio_peak[stk_ratio_peak < -1.5].index[-1]
            else:
                DropDate = stk_ratio_peak.idxmin()
            DropDate_Lag = stk_data.loc[DropDate:].index[1] if len(
                stk_data.loc[DropDate:]) > 1 else None
            if DropDate_Lag is not None:
                Restore_Date = stk_data.loc[DropDate_Lag:][
                    stk_data.loc[DropDate_Lag:, 'high'] >= max(stk_data.loc[DropDate, 'pre_close'],
                                                               stk_data.loc[DropDate, 'open'])].index[0] \
                    if np.any(stk_data.loc[DropDate_Lag:, 'high'] >=
                              max(stk_data.loc[DropDate, 'pre_close'], stk_data.loc[DropDate, 'open'])) \
                    else None
                if Restore_Date is not None and Restore_Date > DropDate and Restore_Date > row['Now_SecDate']:
                    result_totalmv.loc[idx, 'DropRecov_Date'] = Restore_Date
    cols = result_totalmv.columns.tolist()
    cols.remove('DropRecov_Date')
    result_totalmv = result_totalmv[['DropRecov_Date'] + cols]

    return result_sec_pick, result_bottom_pull, result_totalmv


# def get_sectrack_result(track_date=None, pick_date=None, pull_startdate=None,
#                         bottom_industry=None):
#     """使用get_turntrack_result筛选指数转折点品种，在两个转折点区间跟踪筛选入场时机。

#     主要功能:
#     - 调用get_turntrack_result获取指数转折点筛选结果
#     - 在两个转折点区间内跟踪股票走势
#     - 根据走势特征筛选合适的入场时机

#     参数:
#         track_date (str): 跟踪日期
#         pick_date (str): 筛选日期
#         pull_startdate (str): 区间起始日期
#         bottom_industry (list): 底部行业列表,默认为None

#     返回:
#         tuple: 包含两个DataFrame:
#             - result_pick: 符合入场条件的股票筛选结果
#             - track_result: 原始转折点筛选结果

#     更新说明:
#         20241214:
#         - 优化了筛选逻辑
#         - 增加了对底部行业的支持
#     """
#     trade_dates = get_trade_date()
#     lead2_date = trade_dates[trade_dates<track_date][-2]
#     pull_quota, turn_plate_quota, _ = get_turntrack_result(pick_date=pick_date,
#                                                            pull_startdate=pull_startdate,
#                                                            bottom_industry=bottom_industry)
#     track_result = pd.concat([pull_quota, turn_plate_quota.query('Track_Categ=="Track_Peak_Plate"')],
#                              ignore_index=True, join='outer')
#     pick_stklist = track_result['ts_code'].unique().tolist()
#     result = get_result_3(end_date=track_date)
#     result_pick = result.query('ts_code in @pick_stklist & '
#                                'PostSecMaxRollAvg_MinPGV2Now_LastDays==0 & '
#                                'PostSecStart_PGV_MaxRollAvg2Now_LastDays>0 & '
#                                '(Now_SecDate==@track_date | PostNowSec_PeakDate<=@lead2_date)'
#                                ).copy().sort_values(by='PostSecMaxRollAvg_PGV_MinRollAvg2MaxRatio', ascending=True)
#     result_pick['Now2Median_PGV'] = round(
#         result_pick['PostSecMaxRollAvg_MinPGV'] / result_pick['PostSecStart_PGV_MeanRollAvg'], 3)
#     result_pick = result_pick[['ts_code', 'name', 'industry', 'PostTurn_COR_Und2Poxn',
#                                'PostSecMaxRollAvg_MinPGV',
#                                'PostSecMaxRollAvg_PGV_MinRollAvg2MaxRatio',
#                                'PostSecStart_PGV_MaxRollAvg2Now_LastDays',
#                                'PostSecStart_PGV_MaxRollAvg2MinRatio',
#                                'Now2Median_PGV', 'PostSecMaxRollAvg_MinPGV2Now_LastDays',
#                                'Now_SecDate', 'PostNowSec_PeakDate']]
#     return result_pick, track_result


def process_stock_data_optimized(row, stock_data, industry_data, stock_info):
    """处理单个股票数据的辅助函数

    Args:
        row: 包含股票信息的Series
        stock_data: 股票历史数据
        industry_data: 预计算的行业数据字典
        stock_info: 股票基本信息DataFrame

    Returns:
        dict: 包含处理结果的字典
    """
    ts_code = row['ts_code']
    industry = stock_info[stock_info['ts_code'] == ts_code]['industry'].iloc[0]

    if industry not in industry_data:
        return None

    ind_data = industry_data[industry]
    stk_data = stock_data[
        (stock_data['ts_code'] == ts_code) &
        (stock_data['trade_date'] >= row['PreNow_PeakDate'])
    ].copy()
    stk_data = stk_data.set_index('trade_date')

    # 计算股票相对行业的收益率
    stk_common_dates = sorted(list(set(stk_data.index) &
                                   set(ind_data['industry_index'].index)))

    if not stk_common_dates:
        return None

    stock_returns = stk_data.loc[stk_common_dates, 'close'].pct_change()
    industry_returns_for_stock = ind_data['industry_index'].loc[stk_common_dates].pct_change(
    )
    relative_returns = (stock_returns - industry_returns_for_stock).fillna(0)
    cumulative_relative_returns = (1 + relative_returns).cumprod() - 1

    stk_min_cumret_date = cumulative_relative_returns.idxmin()
    stk_min_cumret_drop = (cumulative_relative_returns.min() -
                           cumulative_relative_returns.loc[:stk_min_cumret_date].max())

    # 计算行业内排名
    industry_min_cumret_date = ind_data['industry_min_cumret_date']
    if industry_min_cumret_date not in stk_data.index:
        return None

    current_ret = (stk_data['close'].iloc[-1] /
                   stk_data.loc[industry_min_cumret_date, 'close']) - 1

    # 直接使用预计算的收益率数据
    stocks_returns = ind_data['stocks_returns']
    if stocks_returns:
        rank_percentile = (sum(r >= current_ret for r in stocks_returns.values()) /
                           len(stocks_returns))
    else:
        rank_percentile = None

    return {
        'ts_code': ts_code,
        'stk_min_cumret_date': stk_min_cumret_date,
        'industry_min_cumret_date': industry_min_cumret_date,
        'industry_rank_percentile': round(rank_percentile, 4) if rank_percentile is not None else None,
        'stk_min_cumret_drop': round(stk_min_cumret_drop*100, 4),
        'industry_min_cumret_drop': round(ind_data['industry_min_cumret_drop']*100, 4),
        'industry_drop_enddate': ind_data['industry_enddate'],
        'industry_rise_maxdate': ind_data['industry_maxdate'],
        'industry_end2max_ratio': round(ind_data['industry_end2max_ratio'], 4)
    }


def process_totalmv_stock(row, stock_data):
    """处理单个市值股票数据的辅助函数"""
    stk_data = stock_data[stock_data['ts_code'] == row['ts_code']].copy()
    stk_data = stk_data.set_index('trade_date', drop=False)
    stk_ratio = stk_data['close'].pct_change() * 100

    if row['PreNow_PeakDate'] is not None and row['Now_SecDate'] is not None \
            and row['Now_SecDate'] > row['PreNow_PeakDate']:
        stk_ratio_peak = stk_ratio.loc[row['PreNow_PeakDate']:row['Now_SecDate']]
        if len(stk_ratio_peak) > 0:
            if sum(stk_ratio_peak < -1.5) > 0:
                DropDate = stk_ratio_peak[stk_ratio_peak < -1.5].index[-1]
            else:
                DropDate = stk_ratio_peak.idxmin()
            DropDate_Lag = stk_data.loc[DropDate:].index[1] if len(
                stk_data.loc[DropDate:]) > 1 else None
            if DropDate_Lag is not None:
                if np.any(stk_data.loc[DropDate_Lag:, 'high'] >=
                          max(stk_data.loc[DropDate, 'pre_close'], stk_data.loc[DropDate, 'open'])):
                    Restore_Date = stk_data.loc[DropDate_Lag:][
                        stk_data.loc[DropDate_Lag:, 'high'] >= max(
                            stk_data.loc[DropDate, 'pre_close'],
                            stk_data.loc[DropDate, 'open'])].index[0]
                    if Restore_Date > DropDate and Restore_Date > row['Now_SecDate']:
                        return {'ts_code': row['ts_code'], 'DropRecov_Date': Restore_Date}
    return {'ts_code': row['ts_code'], 'DropRecov_Date': None}


def get_turnbreak_stocks(turntrack_startdate=None, recentdrop_startdate=None, end_date=None,
                         scope='sec', model_adj=True, store_mode=True,
                         index_turndate=None, bottom_industry=None):
    """从数据表获取指定日期后的转折点筛选结果，并根据走势特征进行二次筛选。

    参数更新:
        end_date: 现在支持单个日期(str)或日期列表(list)
    """
    if turntrack_startdate is None:
        print('必须录入turntrack_startdate日期')
        return

    # 将end_date转换为列表格式
    end_dates = [end_date] if isinstance(end_date, str) else end_date

    # 优化数据库连接和查询
    import config.config_Ali as config
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit',
        pool_size=10,
        max_overflow=20
    )

    # 一次性获取所有数据
    if scope.lower() == 'sec':
        turntrack_sql = f"""
            SELECT * FROM stocksfit.stkpick_turntrack 
            WHERE Cal_Date >= '{turntrack_startdate}' 
            AND Cal_Date <= '{max(end_dates)}'
        """
        prepared_data = pd.read_sql(turntrack_sql, engine)
        prepared_data = prepared_data.drop_duplicates(
            subset=['ts_code'], keep='last')
    else:
        prepared_data = get_stock_info()

    if len(prepared_data) == 0:
        print('备选品种为0，数据库中未预存计算结果')
        engine.dispose()
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

    # 获取股票数据
    ts_list = prepared_data['ts_code'].unique().tolist()
    result_check = get_result_3(end_date=end_dates, stk_list=ts_list)

    # 检查缺失日期
    missing_dates = set(end_dates) - set(result_check['Cal_Date'].unique())
    if missing_dates:
        print('警告: 以下日期在result_check中缺失数据:\n' + '\n'.join(missing_dates))

    # 一次性获取所有股票数据
    min_section_peakdate = pd.to_datetime(
        result_check['Section_PeakDate']).min().strftime('%Y-%m-%d')
    needed_columns = ['ts_code', 'trade_date', 'open',
                      'high', 'low', 'close', 'pre_close', 'turnover']
    stock_data = get_stock_data(
        start_date=min_section_peakdate, end_date=max(end_dates))[needed_columns]
    stock_data = stock_data.sort_values(['ts_code', 'trade_date'])

    # 数据预处理
    if scope.lower() == 'sec':
        prepared_data = prepared_data.rename(columns={'Cal_Date': 'Pick_Date'})
        result_check = pd.merge(result_check,
                                prepared_data[['ts_code',
                                               'Pick_Date', 'Track_Categ']],
                                on='ts_code', how='inner')

    # 计算Now_SecDiff
    trade_dates = get_trade_date()
    result_check['Now_SecDiff'] = 999 if index_turndate is None else \
        result_check.apply(lambda fn: conf_nowdiff(
            fn['Now_SecDate'], index_turndate, trade_dates), axis=1)

    # 使用向量化操作替代eval
    result_filtered = result_check.copy()

    if len(result_filtered) > 0:
        # 条件1: 近期回补特征
        # 选择最近10天内出现回补且回补日期为计算日期的股票
        cond1 = (result_filtered.eval("(PostNowSec_Recover_TopOpen_Date==Cal_Date & "
                                      "(PostPeak_Recent_Neg4_RecovDate==Cal_Date & "
                                      "PostPeak_Recent_Neg4_DropDate<=Now_SecDate & "
                                      "PostPeak_Recent_Neg4_RecovDays<=8)) & "
                                      "PreNowSec_Sec_AvgTurnover>1"))

        # 条件2: 区间持续时间特征
        # 选择当前区间持续时间大于等于3天且前高到现在的最大换手距今不超过10天的股票
        cond2 = result_filtered.eval("PreNowSec_LastDays>=2 & "
                                     "(PostPreNowPeak_MaxTO_Eff2Now_LastDays<10 | "
                                     "PostPreNowPeak_U2D_MaxTO_Eff2Now_LastDays<5 | "
                                     "Days_From_Last_Peak<5 | "
                                     "PostPreNowPeak_MaxTO_Eff_Band<0)")

        # 条件3: 有效高点特征
        # 选择近期有效高点变化率在50%分位数以下、有效高点强度大于0.5且前高到现在的最大换手有效带宽大于1的股票
        cond3 = result_filtered.eval("PostPreNowPeak_MaxTO_Eff_CoverDays>10 & "
                                     "Eff_Peak_Intensity>0.3 & PostPreNowPeak_U2D_MaxTO_Eff_Band>=1")

        # 条件4: 有效高点变化趋势
        # 选择近期有效高点较前期下降或前高到现在的最大换手有效带宽大于1的股票
        cond4 = result_filtered.eval(
            "(Eff_Recent2Previous_Change<0 | PostPreNowPeak_MaxTO_Eff_Band>1)")

        # # 条件5: 前高到现在区间排名
        # # 选择前高到现在区间移动平均比值排名小于8的股票
        # cond5 = result_filtered.eval("PreNowPeak2NowSec_RatioMovAvg_NowSecRank<8")
        cond5 = result_filtered.eval("Now_SecDiff<=2 | Now_SecDiff==999")

        # 条件6: 均线位置
        # 选择区间开始后大部分时间在5日均线上方或刚开始新区间,且前高到区间大部分时间在5日均线下方的股票
        cond6 = result_filtered.eval("(PostSecStart_Over5MovAvg_Prop>0.5 | PostSecStart_RiseRatio<20) | "
                                     "Peak2Sec_Und5MovAvg_Prop>0.5")

        # 合并所有条件
        result_filtered = result_filtered[cond1 &
                                          cond2 & cond3 & cond4 & cond5 & cond6]

    if len(result_filtered) > 0:
        output_DropRecov = result_filtered.sort_values(
            by=['Eff_Avg_Peak_Period', 'PostPreNowPeak_MaxTO_Eff_CoverDays'],
            ascending=[True, False]
        )
        output_DropRecov['DropRecov_Style'] = output_DropRecov.apply(
            lambda x: 'Recov3Days' if x['PostNowSec_Recover_TopOpen_Date'] == x['Cal_Date']
            else 'RecovNeg4Day', axis=1)

        # 将指定列移到最前面
        cols_to_front = ['DropRecov_Style', 'PostSecStart_Over5MovAvg_Prop',
                         'Peak2Sec_Und5MovAvg_Prop', 'Now_SecDiff']
        other_cols = [
            col for col in output_DropRecov.columns if col not in cols_to_front]
        output_DropRecov = output_DropRecov[cols_to_front + other_cols]

    else:
        print('备选品种为0，数据库中未预存计算结果')
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

    recentdrop_startdate = trade_dates[trade_dates <= max(end_dates)][-5]
    Bottom_List, _ = cal_swindex_state(end_date=max(
        end_dates), index_preturn_date=recentdrop_startdate)

    if len(output_DropRecov) > 0:
        output_DropRecov['stk_min_cumret_date'] = None
        output_DropRecov['industry_min_cumret_date'] = None
        output_DropRecov['industry_rank_percentile'] = None
        output_DropRecov['stk_min_cumret_drop'] = None
        output_DropRecov['industry_min_cumret_drop'] = None
        output_DropRecov['industry_drop_enddate'] = None
        output_DropRecov['industry_rise_maxdate'] = None
        output_DropRecov['industry_end2max_ratio'] = None

        # 预先计算行业相关数据
        industry_data = {}
        stock_info = get_stock_info()

        # 获取所有需要处理的行业
        unique_industries = stock_info[stock_info['ts_code'].isin(
            output_DropRecov['ts_code'])]['industry'].unique()

        # 获取行业指数数据
        all_industry_data = get_swindex_data(
            start_date=min(
                output_DropRecov['PreNow_PeakDate'].min(), recentdrop_startdate),
            end_date=max(end_dates)
        )

        # 获取中证800指数数据
        zz800_data = get_index_data(
            stk_code='000906.SH',
            start_date=min(
                output_DropRecov['PreNow_PeakDate'].min(), recentdrop_startdate),
            end_date=max(end_dates)
        )
        zz800_data = zz800_data.set_index('trade_date')

        # 预先计算所有行业的收益率排名数据
        industry_returns_data = {}

        # 预计算每个行业的指标
        for industry in unique_industries:
            if industry in all_industry_data.columns and industry in Bottom_List['indus'].values:
                industry_maxdate = Bottom_List[Bottom_List['indus']
                                               == industry]['End2Max_MaxDate'].iloc[0]
                industry_startdate = Bottom_List[Bottom_List['indus']
                                                 == industry]['start_date'].iloc[0]
                industry_enddate = Bottom_List[Bottom_List['indus']
                                               == industry]['end_date'].iloc[0]
                industry_end2max_ratio = Bottom_List[Bottom_List['indus']
                                                     == industry]['End2Max_Ratio'].iloc[0]

                # 计算行业相对指数的收益率
                industry_index = all_industry_data[industry]
                zz800_index = zz800_data['close']

                industry_common_dates = sorted(list(set(industry_index.loc[industry_maxdate:].index) &
                                                    set(zz800_index.loc[industry_maxdate:].index)))

                if industry_common_dates:
                    industry_returns = industry_index[industry_common_dates].pct_change(
                    )
                    zz800_returns = zz800_index[industry_common_dates].pct_change(
                    )
                    industry_relative_returns = (
                        industry_returns - zz800_returns).fillna(0)
                    industry_cumulative_relative_returns = (
                        1 + industry_relative_returns).cumprod() - 1

                    # 计算行业指标
                    industry_min_cumret_date = industry_cumulative_relative_returns.idxmin()
                    industry_min_cumret_drop = (industry_cumulative_relative_returns.min() -
                                                industry_cumulative_relative_returns.loc[:industry_min_cumret_date].max())

                    # 计算行业从最低点至今的涨幅
                    industry_rise_ratio = (
                        industry_index.iloc[-1] / industry_index.loc[industry_min_cumret_date] - 1) * 100

                    # 预先计算该行业内所有股票的收益率
                    industry_stocks = stock_info[stock_info['industry'] == industry]['ts_code'].tolist(
                    )
                    stocks_returns = {}
                    for ind_stk in industry_stocks:
                        try:
                            ind_stk_data = stock_data[(stock_data['ts_code'] == ind_stk) &
                                                      (stock_data['trade_date'] >= industry_min_cumret_date)].copy()
                            ind_stk_data = ind_stk_data.set_index('trade_date')
                            if len(ind_stk_data) >= 2:
                                ret = (
                                    ind_stk_data['close'].iloc[-1] / ind_stk_data['close'].iloc[0]) - 1
                                stocks_returns[ind_stk] = ret
                        except:
                            continue

                    # 存储行业数据
                    industry_data[industry] = {
                        'industry_min_cumret_date': industry_min_cumret_date,
                        'industry_min_cumret_drop': industry_min_cumret_drop,
                        'industry_index': industry_index,
                        'industry_maxdate': industry_maxdate,
                        'industry_startdate': industry_startdate,
                        'industry_enddate': industry_enddate,
                        'industry_end2max_ratio': industry_end2max_ratio,
                        'industry_ret2now_ratio': industry_rise_ratio,
                        'stocks_returns': stocks_returns
                    }

        # 创建一个新的字典，只包含需要的数据
        industry_summary = {}
        for industry, data in industry_data.items():
            industry_summary[industry] = {
                'industry_min_cumret_date': data['industry_min_cumret_date'],
                'industry_min_cumret_drop': round(data['industry_min_cumret_drop'], 3),
                'industry_ret2now_ratio': data['industry_ret2now_ratio'],
                'industry_drop_enddate': data['industry_enddate'],
                'industry_rise_maxdate': data['industry_maxdate'],
                'industry_end2max_ratio': round(data['industry_end2max_ratio'], 3),
                'industry_drop_startdate': data['industry_startdate'],
            }

        # 使用多进程处理股票数据
        with Pool(processes=cpu_count()) as pool:
            process_func = partial(process_stock_data_optimized,
                                   stock_data=stock_data,
                                   industry_data=industry_data,
                                   stock_info=stock_info)
            results = pool.map(
                process_func, [row for _, row in output_DropRecov.iterrows()])

        # 更新DataFrame
        for result in results:
            if result:
                idx = output_DropRecov[output_DropRecov['ts_code']
                                       == result['ts_code']].index[0]
                for key, value in result.items():
                    if key != 'ts_code':
                        output_DropRecov.loc[idx, key] = value

    if len(output_DropRecov) > 0 and bottom_industry is not None:
        output_DropRecov_Indus = output_DropRecov.query(
            'industry in @bottom_industry').copy()
    else:
        output_DropRecov_Indus = pd.DataFrame()

    # #增加筛选bottom_industry中市值居前品种的droprecov筛选
    # if bottom_industry is not None:
    #     result_check_enddate = result_check[result_check['Cal_Date']==max(end_dates)].copy()
    #     result_totalmv = result_check_enddate.query('industry in @bottom_industry'
    #                                      ).sort_values(by='Total_MV', ascending=False
    #                                                    ).groupby('industry').head(20).copy()

    #     # 使用多进程处理市值股票数据
    #     with Pool(processes=cpu_count()) as pool:
    #         process_func = partial(process_totalmv_stock, stock_data=stock_data)
    #         results = pool.map(process_func, [row for _, row in result_totalmv.iterrows()])

    #     # 更新DataFrame
    #     for result in results:
    #         idx = result_totalmv[result_totalmv['ts_code'] == result['ts_code']].index[0]
    #         result_totalmv.loc[idx, 'DropRecov_Date'] = result['DropRecov_Date']

    #     cols_to_front = ['DropRecov_Date', 'PostSecStart_Over5MovAvg_Prop', 'Peak2Sec_Und5MovAvg_Prop']
    #     other_cols = [col for col in result_totalmv.columns if col not in cols_to_front]
    #     result_totalmv = result_totalmv[cols_to_front + other_cols].sort_values(
    #         by=['industry', 'Total_MV'], ascending=[True, False])
    # else:
    #     result_totalmv = pd.DataFrame()

    # 调用模型再筛选
    DropRecov_Predict = pd.DataFrame()
    if model_adj and len(output_DropRecov) > 1:
        print('启动预测模型!')
        _, DropRecov_Predict = stkpick_model_predict(
            predict_date=max(end_dates),
            stk_list=output_DropRecov['ts_code'].unique().tolist(),
            model_select='XGB',
            model_date='2024-12-31',
            label_style='Label',
            pretreat='none')

    if store_mode and len(output_DropRecov) > 0:
        store_droprecov = output_DropRecov.copy()
        # 对每个end_date分别存储结果
        for curr_end_date in end_dates:
            curr_store_data = store_droprecov[store_droprecov['Cal_Date'] == curr_end_date].copy(
            )
            if len(curr_store_data) == 0:
                continue

            curr_store_data['Pick_Date'] = curr_end_date
            curr_store_data['Recent_DropDate'] = recentdrop_startdate
            curr_store_data['Scope'] = scope

            store_sql = f"""select * from stocksfit.stkpick_droprecov 
                            where Pick_Date='{curr_end_date}' and Recent_DropDate='{recentdrop_startdate}' 
                            """
            turntrack_storedata = pd.read_sql(store_sql, engine)

            Sesstion = sessionmaker(bind=engine)
            session = Sesstion()
            if len(turntrack_storedata) > 0:
                try:
                    delete_sql = text("delete from stocksfit.stkpick_droprecov "
                                      "where Pick_Date= :end_date and Recent_DropDate= :recentdrop_startdate")
                    with session.begin():
                        session.execute(delete_sql, {"end_date": curr_end_date,
                                                     "recentdrop_startdate": recentdrop_startdate})
                    session.commit()
                except Exception as e:
                    session.rollback()
                    print('An error occured:', e)

            try:
                pd.io.sql.to_sql(curr_store_data, 'stkpick_droprecov', engine, index=False, schema='stocksfit',
                                 if_exists='append')
                print(f'{curr_end_date} DropRecov筛选数据已存储',
                      '\n已存储股票条目：', len(curr_store_data))
            except:
                pdb.set_trace()
            session.close()

    else:
        print('未存储DropRecov筛选数据')

    engine.dispose()
    return output_DropRecov, output_DropRecov_Indus, DropRecov_Predict, industry_summary


def filter_stocks_by_bottom_industries(output_DropRecov, end_date, trade_dates,
                                       indus_num=5, recentdrop_startdate=None,
                                       print_indus=True):
    """根据行业上涨日期筛选股票

    Args:
        output_DropRecov (pd.DataFrame): 待筛选的股票DataFrame
        end_date (str): 目标日期
        recentdrop_startdate (str): 近期下跌起始日期
        trade_dates (list): 交易日期列表

    Returns:
        pd.DataFrame: 筛选后的股票列表
    """
    # 获取check_date前15个交易日日期
    if recentdrop_startdate is None:
        recentdrop_startdate = trade_dates[trade_dates <= end_date][-indus_num]

    Bottom_List, _ = cal_swindex_state(
        end_date=end_date, index_preturn_date=recentdrop_startdate)
    Bottom_List = Bottom_List.sort_values(
        ['end_date', 'MaxDrop', 'Drop_Lastdays'], ascending=[False, True, False])
    Bottom_List = Bottom_List.reset_index(drop=True)

    # 获取end_date前2个月的1号日期
    end_dt = pd.to_datetime(end_date)
    first_day = (end_dt - pd.DateOffset(months=1)
                 ).replace(day=1).strftime('%Y-%m-%d')
    # 获取1号后的首个交易日期作为pre20_date
    pre2month_date = trade_dates[trade_dates > first_day][0]

    indus_list = Bottom_List.query(
        'end_date>=@pre2month_date')['indus'].values.tolist()

    if print_indus:
        print(f'{pre2month_date}日期后的Bottom转折行业列表:')
        indus_dates = Bottom_List.query(
            'end_date>=@pre2month_date')[['indus', 'end_date', 'Drop_Lastdays']].values.tolist()
        for indus, end_dt, last_days in indus_dates:
            print(f'- {indus} (结束日期: {end_dt}, 下行持续天数: {last_days})')

    # 筛选属于符合条件行业的股票
    if len(output_DropRecov) > 0 and len(indus_list) > 0:
        output_DropRecov_Indus = output_DropRecov[
            output_DropRecov['industry'].isin(indus_list)
        ].copy()

        # 新增字段标识
        def check_bottom_date(row):
            indus_bottom = Bottom_List[Bottom_List['indus'] == row['industry']]
            if len(indus_bottom) == 0:
                return None

            bottom_end_date = pd.to_datetime(indus_bottom['end_date'].iloc[0])
            now_sec_date = pd.to_datetime(row['Now_SecDate'])

            # 获取两个日期之间的交易日数量
            date1_idx = min(bottom_end_date, now_sec_date).strftime('%Y-%m-%d')
            date2_idx = max(bottom_end_date, now_sec_date).strftime('%Y-%m-%d')
            days_diff = len(
                trade_dates[(trade_dates > date1_idx) & (trade_dates <= date2_idx)])

            if days_diff <= 2:
                return indus_bottom['end_date'].iloc[0]
            return None

        output_DropRecov_Indus.insert(0, 'Bottom_Indus_Turn_Flag',
                                      output_DropRecov_Indus.apply(check_bottom_date, axis=1))

    else:
        output_DropRecov_Indus = pd.DataFrame()

    return output_DropRecov_Indus


def get_trend_wls_length(stock_data, ts_code, cal_startdate, cal_enddate):
    """计算趋势WLS长度

    根据给定的股票数据、代码和日期,计算趋势WLS(加权最小二乘法)的长度特征。

    参数:
        stock_data (DataFrame): 包含股票数据的DataFrame
        ts_code (str): 股票代码
        cal_date (str): 计算日期

    返回:
        tuple: 包含以下三个值:
            - max_r2 (float): 最大R方值
            - max_r2_length (int): 最大R方值对应的趋势长度(天数)
            - max_r2_width (float): 最大R方值对应的趋势宽度(偏差比例)
    """
    if cal_startdate < cal_enddate:
        stk_data = stock_data.query('ts_code==@ts_code').set_index(
            'trade_date', drop=False).loc[cal_startdate:cal_enddate].copy()
        stk_data['trend_wls_slope'] = 0.0  # 显式指定float类型
        stk_data['trend_wls_r2'] = 0.0  # 显式指定float类型
        stk_data['trend_wls_dev_ratio'] = 0.0  # 显式指定float类型
        for date in stk_data.index:
            trend_wls_slope, trend_wls_r2, trend_wls_dev_ratio = cal_trend_wls(
                stk_data.loc[date:])
            stk_data.loc[date, 'trend_wls_slope'] = float(
                trend_wls_slope)  # 确保转换为float
            stk_data.loc[date, 'trend_wls_r2'] = float(
                trend_wls_r2)  # 确保转换为float
            stk_data.loc[date, 'trend_wls_dev_ratio'] = float(
                trend_wls_dev_ratio)  # 确保转换为float
        max_r2 = float(stk_data['trend_wls_r2'].max())
        max_r2_length = int(
            len(stk_data.loc[stk_data['trend_wls_r2'].idxmax():]))  # 确保转换为int
        max_r2_width = float(
            stk_data.loc[stk_data['trend_wls_r2'].idxmax(), 'trend_wls_dev_ratio'])
        return max_r2, max_r2_length, max_r2_width
    else:
        return 0.0, 0, 0.0


def track_turnbreak_stocks(index_turndates=None, lag_num=None, check_date=None, industry=None):
    """对step1存储结果的二次确认筛选。

    主要功能:
    - 获取index_turndates中每个日期及其后lag_num天的存储品种
    - 确认Pick_Date至check_date期间股价走势特征:
        1. 股价未跌破PostPeak_Recent_Neg4_DropDate_Close
        2. 出现日跌幅超2.5%的交易日
        3. 跌幅后出现反弹回复
    - 输出符合条件的跟踪股票名单

    参数:
        index_turndates (str或list): 日期列表,可为单个日期或日期列表
        lag_num (int): 每个日期后续天数,用于确定筛选区间
        check_date (str): 确认日期,用于获取最新数据进行确认

    返回:
        DataFrame: result_output,包含以下主要字段:
            - ts_code (str): 股票代码
            - name (str): 股票名称
            - industry (str): 所属行业
            - Check_RecovDate (str): 反弹确认日期
            - Check_DropDate (str): 回调确认日期
            - Check_Above_Step1Drop (bool): 是否维持在step1回调位之上
            - Check_FirstDrop_Recov (bool): 是否出现首次回调后反弹
            - Check_Drop2Recov_LastDays (int): 回调到反弹的天数
    """
    import config.config_Ali as config
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')

    # 获取交易日历
    trade_dates = get_trade_date()
    trade_dates_dt = pd.to_datetime(trade_dates)
    if isinstance(index_turndates, str):
        index_turndates = [index_turndates]

    # 获取每个日期后lag_num天的日期
    all_dates = []
    for turn_date in index_turndates:
        turn_date = pd.to_datetime(turn_date)
        future_dates = trade_dates_dt[trade_dates_dt >= turn_date][:lag_num+1]
        all_dates.extend(future_dates.strftime('%Y-%m-%d').tolist())

    # 去重并排序
    unique_dates = sorted(list(set(all_dates)))
    pick_dates = ','.join(["'%s'" % item for item in unique_dates])
    store_sql = f"""select * from stocksfit.stkpick_droprecov 
                    where Pick_Date in ({pick_dates})"""
    result_store = pd.read_sql(store_sql, engine)
    engine.dispose()
    result_store = result_store.drop_duplicates(
        subset=['ts_code'], keep='last')
    print('备选日期范围：', '/'.join(unique_dates))
    print('备选品种数量：', len(result_store))

    result_store['Now_SecDiff'] = result_store.apply(
        lambda fn: conf_nowdiff(
            fn['Now_SecDate'], index_turndates, trade_dates),
        axis=1)

    stk_startdate = pd.to_datetime(
        result_store['PostPeak_Recent_Neg4_DropDate']).min().strftime('%Y-%m-%d')
    stock_data = get_stock_data(stk_code=result_store['ts_code'].unique().tolist(),
                                start_date=stk_startdate, end_date=check_date)
    stock_data = stock_data.sort_values(
        ['ts_code', 'trade_date'], ascending=[True, True])
    stock_data = stock_data.set_index(['ts_code', 'trade_date'], drop=False)

    result_now = get_result_3(
        end_date=check_date, stk_list=result_store['ts_code'].unique().tolist())
    result_combin = pd.merge(result_now, result_store[['ts_code', 'Pick_Date', 'PostPeak_Recent_Neg4_DropDate',
                                                       'PostPeak_Recent_Neg4_DropDate_Close',
                                                       'PreNowSec_Sec_AvgTurnover',
                                                       'PostSecStart_PGV_MaxRollAvg', 'Now_SecDate', 'Now_SecDiff',
                                                       'Efficiency_VolatilityRatio',
                                                       'PostPreNowPeak_MaxTO_Eff',
                                                       'PostPreNowPeak_MaxTO_Eff_Band', 'Latest_TO_Eff_Band']],
                             on='ts_code', how='left')

    # 筛选条件：1. 确认日期与Now_SecDate的差值小于2天；
    result_combin = result_combin.query('Now_SecDiff<=2').copy()

    result_combin['Check_MaxPRA_Ratio'] = round(
        result_combin['PostSecStart_PGV_MaxRollAvg_x']/result_combin['PostSecStart_PGV_MaxRollAvg_y'], 3)

    # 初始化所有需要计算的列
    result_combin['Below_3DayAvg_Ratio'] = 1.0
    result_combin['PostPick_MaxCls2Now_LastDays'] = 99
    result_combin['Latest2Pick_Open_Ratio'] = 0.0
    result_combin['Keep_Rise_Trend'] = False
    result_combin['Under_PickOpen_Days'] = 99
    result_combin['MovAvg_MaxRatio'] = 0.0
    result_combin['Over7Ratio_Days'] = 0.0
    result_combin['Check_RecovDate'] = None
    result_combin['Check_DropDate'] = None
    result_combin['Check_DropDate_Open'] = None
    result_combin['Check_Above_Step1Drop'] = False
    result_combin['Check_FirstDrop_Recov'] = False
    result_combin['Check_Drop2Recov_LastDays'] = 100
    result_combin['Check_IndexTurnDate'] = None
    result_combin['Check_PostNowSec_AvgTurnover'] = 0.0

    # 合并两个循环的处理逻辑
    for idx, row in result_combin.iterrows():
        ts_code = row['ts_code']
        pick_date = row['Pick_Date']
        now_secdate_y = row['Now_SecDate_y']
        stk_data = stock_data.loc[ts_code].copy()

        # 计算技术指标
        stk_data['avg_price'] = (stk_data['close']+stk_data['open'])/2
        stk_data['ma3'] = stk_data['avg_price'].rolling(window=3).mean()
        stk_data['stk_ratio'] = stk_data['close'].pct_change() * 100
        stk_data['stk_ratio_3'] = (
            stk_data['close'] / stk_data['close'].shift(3) - 1) * 100

        # 获取Pick_Date到Check_Date期间的数据
        period_data = stk_data.loc[now_secdate_y:check_date].copy()
        if len(period_data) == 0:
            continue

        pick_nowsecdate_open = min(stk_data.loc[now_secdate_y:, 'open'].iloc[1],
                                   stk_data.loc[pick_date, 'open'])

        # 计算第一部分指标
        below_days = (period_data['avg_price'] < period_data['ma3']).sum()
        result_combin.loc[idx, 'Below_3DayAvg_Ratio'] = round(
            below_days / len(period_data), 3)
        postpick_maxcls_date = period_data['close'].idxmax()
        result_combin.loc[idx, 'PostPick_MaxCls2Now_LastDays'] = len(
            period_data.loc[postpick_maxcls_date:]) - 1
        result_combin.loc[idx, 'Latest2Pick_Open_Ratio'] = round(
            (period_data.loc[check_date, 'close'] / pick_nowsecdate_open - 1) * 100, 3)
        result_combin.loc[idx, 'Keep_Rise_Trend'] = len(
            period_data.loc[postpick_maxcls_date:]) - 1 < max(5, len(period_data)/2)
        result_combin.loc[idx, 'Under_PickOpen_Days'] = period_data.loc[
            pick_date:].query('close<@pick_nowsecdate_open').shape[0]
        result_combin.loc[idx, 'MovAvg_MaxRatio'] = round(
            period_data['stk_ratio_3'].max(), 3)
        result_combin.loc[idx, 'Over7Ratio_Days'] = period_data.query(
            'stk_ratio>=7').shape[0]
        result_combin.loc[idx, 'Check_PostNowSec_AvgTurnover'] = round(
            period_data['turnover'].mean(), 3)
        # 计算第二部分指标
        if stk_data.loc[pick_date:check_date, 'close'].min() > row['PostPeak_Recent_Neg4_DropDate_Close_y']:
            result_combin.loc[idx, 'Check_Above_Step1Drop'] = True

            set_threshold = -2.5
            if stk_data.loc[pick_date:check_date, 'stk_ratio'].min() <= set_threshold:
                ratio_threshold = set_threshold
            else:
                ratio_threshold = min(-1.5,
                                      stk_data.loc[pick_date:check_date, 'stk_ratio'].min())

            if stk_data.loc[pick_date:check_date, 'stk_ratio'].min() <= ratio_threshold:
                drop_dates = stk_data.loc[pick_date:check_date,
                                          'trade_date'][stk_data.loc[pick_date:check_date, 'stk_ratio'] <= ratio_threshold].index
                drop_date = drop_dates[-1]
                result_combin.loc[idx, 'Check_DropDate'] = drop_date
                result_combin.loc[idx,
                                  'Check_DropDate_Open'] = stk_data.loc[drop_date, 'open']

                drop_lagdate = stk_data.loc[drop_date:].index[1] if len(
                    stk_data.loc[drop_date:]) > 1 else None
                if drop_lagdate is not None and stk_data.loc[drop_lagdate:, 'high'].max() > stk_data.loc[drop_date, 'open']:
                    recovdate = stk_data.loc[drop_lagdate:][stk_data.loc[drop_lagdate:, 'high'] >
                                                            stk_data.loc[drop_date, 'open']].index[0]
                    result_combin.loc[idx, 'Check_RecovDate'] = recovdate
                    result_combin.loc[idx, 'Check_Drop2Recov_LastDays'] = len(
                        stk_data.loc[drop_date:recovdate]) - 1
                    result_combin.loc[idx, 'Check_IndexTurnDate'] = stk_data.loc[drop_date:recovdate, 'close'].idxmin(
                    )

                    # 判断是否为首次回复
                    is_first = True
                    if len(drop_dates) > 1:
                        for prev_drop_date in drop_dates[:-1]:
                            prev_drop_lagdate = stk_data.loc[prev_drop_date:].index[1] if len(
                                stk_data.loc[prev_drop_date:]) > 1 else None
                            if prev_drop_lagdate is not None:
                                if stk_data.loc[prev_drop_lagdate:drop_date, 'high'].max() > stk_data.loc[prev_drop_date, 'open']:
                                    is_first = False
                                    break
                    result_combin.loc[idx, 'Check_FirstDrop_Recov'] = is_first

    # 定义基础筛选条件
    base_conditions = (
        'Latest2Pick_Open_Ratio>=0 & '  # 当前价格高于选股时开盘价
        'Under_PickOpen_Days==0 & '     # 无低于选股开盘价的交易日
        'MovAvg_MaxRatio<=20'        # 移动平均最大比率不超过20
        # 'Over7Ratio_Days<=2'             # 日涨幅超过7%的天数少于2天
    )

    # 定义回复型筛选条件
    latest_turndate = index_turndates[-1] if len(index_turndates) > 0 else None
    recov_conditions = (
        '((Check_RecovDate.notna() & '                           # 存在回复日期
        'Check_RecovDate==Cal_Date & '                         # 回复日期为当前日期
        f'Check_IndexTurnDate=="{latest_turndate}") | '
        f'Pick_Date>="{latest_turndate}")'                    # 或选股日期在最新转折点之后
    )

    # 组合筛选条件
    combined_conditions = base_conditions + ' & ' + recov_conditions

    # 定义排序字段
    sort_fields = [
        'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays',
        'PostSecStart_PGV_MaxRollAvg2Now_LastDays'
    ]

    # 一次性筛选并排序
    result_recov = result_combin.query(combined_conditions).sort_values(
        by=sort_fields,
        ascending=[True, False]
    )

    # if len(result_recov) > 0:
    #     cols_to_front = ['ts_code', 'name', 'industry', 'Pick_Date',
    #                      'PostNowSec_LastDays',
    #                      'Check_Drop2Recov_LastDays', 'Check_MaxPRA_Ratio',
    #                      'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays_y',
    #                      'PostSecStart_PGV_MaxRollAvg2Now_LastDays_y',
    #                      'Now_SecDate_y']
    #     other_cols = [col for col in result_recov.columns if col not in cols_to_front]
    #     result_recov = result_recov[cols_to_front + other_cols]

    # 调用函数筛选上涨行业中的股票
    result_check = result_combin.rename(
        columns={'Now_SecDate_y': 'Now_SecDate'}).copy()
    if industry is not None:
        result_check_indus = result_check.query('industry in @industry').copy()
    else:
        result_check_indus = filter_stocks_by_bottom_industries(
            result_check, check_date, trade_dates, indus_num=5)
    result_check_indus_strict = result_check.query(
        'PreNowBottom2PostNowPeak_STrend==1 & PreNowBottom2PostNowPeak_STrend_UpDev<0.15').copy().sort_values(
            by=['PostPick_MaxCls2Now_LastDays', 'Latest2Pick_Open_Ratio'],
            ascending=[True, False])

    return result_recov, result_check_indus


def cal_period_deviation(result, stk_data, start_date, end_date):
    """计算指定区间的deviation数据

    主要功能:
    - 计算指定区间内的偏离度指标
    - 判断股价走势是否维持缓步上行
    - 统计连续上涨天数和区间特征

    参数:
    result: 待计算的股票数据
    stk_data: 股票历史行情数据
    start_date: 计算起始日期
    end_date: 计算结束日期

    返回:
    result: 添加deviation计算结果的DataFrame"""
    # ts_code = result['ts_code']
    result['Check_SlowTrend_State'] = '-'
    result['Check_TrendCOR_ConsecuState'] = '-'
    result['Check_SlowTrend_Upper_Deviation'] = 0
    result['Check_SlowTrend_Lower_Deviation'] = 0
    result['Check_SlowTrend_Sec_MaxAvgRatio'] = 0
    result['Check_SlowTrend_Sec_LastDays'] = 0
    result['PostSlowTrend_PeakDate'] = None
    if len(stk_data.loc[start_date:end_date]) > 0:
        # now_secdate = result['Now_SecDate_x']
        cal_startdate = stk_data.loc[start_date:end_date, 'close'].idxmin()
        cal_enddate = stk_data.loc[cal_startdate:end_date, 'close'].idxmax()
        period_deviation = calculate_deviation(
            stk_data.loc[cal_startdate:cal_enddate])
        trend_consecutive = cal_trend_consecutive(
            stk_data=stk_data.loc[cal_startdate:cal_enddate].copy())
        if (len(period_deviation) > 0
                and abs(period_deviation['sec_maxavgratio']) <= 2
                and period_deviation['sec_lastdays'] >= 10
                and 10 > abs(period_deviation['upper_deviation'])
                and ((max(abs(period_deviation['upper_deviation']), 3) >= abs(period_deviation['lower_deviation']))
                     or (trend_consecutive['narrow_prop'] >= 0.6
                         and abs(period_deviation['lower_deviation']) < 10))):
            # and (stk_data.loc[cal_startdate:end_date, 'close'].max() /
            #     stk_data.loc[start_date:cal_startdate, 'close'].max()) < 1.1
            # and stk_data.loc[cal_startdate:end_date, 'close'].max() /
            #     stk_data.loc[now_secdate:end_date, 'close'].max() < 1.05):
            result['Check_SlowTrend_State'] = 'True'
        if len(trend_consecutive) > 0 and (trend_consecutive['max_consecutive_days'] >= 5
                                           or trend_consecutive['narrow_prop'] >= 0.5):
            result['Check_TrendCOR_ConsecuState'] = 'True'
        result['Check_SlowTrend_Upper_Deviation'] = period_deviation['upper_deviation']
        result['Check_SlowTrend_Lower_Deviation'] = period_deviation['lower_deviation']
        result['Check_SlowTrend_Sec_MaxAvgRatio'] = period_deviation['sec_maxavgratio']
        result['Check_SlowTrend_Sec_LastDays'] = period_deviation['sec_lastdays']
        result['PostSlowTrend_PeakDate'] = stk_data.loc[cal_startdate:end_date,
                                                        'close'].idxmax()
        result['Check_SlowTrend_NarrowConsecutive_LastDays'] = trend_consecutive['max_consecutive_days']
        result['Check_SlowTrend_NarrowProp'] = trend_consecutive['narrow_prop']
        result['Check_Trend_COR_Prop'] = trend_consecutive['trend_cor_prop']
        result['Check_Trend_COR_LastDays'] = trend_consecutive['max_cor_consecutive_days']
    return result


# def cal_trend_consecutive(stk_data=None, ts_code=None, start_date=None, end_date=None):
#     """统计价格序列中连续天数的特征指标。

#     主要功能:
#     - 计算日均价格变动幅度低于阈值(2%)的连续天数
#     - 统计收盘价高于开盘价的连续天数
#     - 计算两种特征的占比

#     参数:
#         stk_data (DataFrame): 股票行情数据,包含OHLC价格,可选
#         ts_code (str): 股票代码,stk_data为None时必填
#         start_date (str): 起始日期,stk_data为None时必填
#         end_date (str): 结束日期,stk_data为None时必填

#     返回:
#         dict: 包含以下指标:
#             - max_consecutive_days (int): 日均价格变动幅度低于阈值的最大连续天数
#             - narrow_prop (float): 日均价格变动幅度低于阈值的天数占比
#             - max_cor_consecutive_days (int): 收盘价高于开盘价的最大连续天数
#             - rise_cor_prop (float): 收盘价高于开盘价的天数占比
#     """
#     if stk_data is None:
#         stk_data = get_stock_data(start_date=start_date, end_date=end_date, stk_code=ts_code)
#     if start_date is not None and end_date is not None:
#         stk_data = stk_data.loc[start_date:end_date].copy()
#     stk_data['avg_price'] = round(stk_data[['close', 'open', 'high', 'low']].mean(axis=1), 3)
#     stk_data['daily_ratio'] = round(stk_data['avg_price'].pct_change()*100, 3)
#     stk_data['clsopen_ratio'] = round((stk_data['close'] / stk_data['open'] - 1)*100, 3)
#     stk_data['ratio_signal'] = abs(stk_data['daily_ratio']) <= 2
#     stk_data['ratio_signal_cor'] = stk_data['clsopen_ratio'] >= 0
#     count = 0
#     consecutive_days = 0
#     max_consecutive_days = 0
#     for index in stk_data.index:
#         if stk_data.loc[index, 'ratio_signal']:
#             consecutive_days += 1
#         else:
#             count += 1
#             max_consecutive_days = consecutive_days \
#                 if max_consecutive_days < consecutive_days else max_consecutive_days
#             consecutive_days = 0
#     count_cor = 0
#     consecutive_days_cor = 0
#     max_consecutive_days_cor = 0
#     for index in stk_data.index:
#         if stk_data.loc[index, 'ratio_signal_cor']:
#             consecutive_days_cor += 1
#         else:
#             count_cor += 1
#             max_consecutive_days_cor = consecutive_days_cor \
#                 if max_consecutive_days_cor < consecutive_days_cor else max_consecutive_days_cor
#             consecutive_days_cor = 0
#     return {'max_consecutive_days': max_consecutive_days,
#             'narrow_prop': round(sum(stk_data['ratio_signal'])/len(stk_data), 3),
#             'max_cor_consecutive_days': consecutive_days_cor,
#             'rise_cor_prop': round(sum(stk_data['ratio_signal_cor'])/len(stk_data), 3),}


def stkpick_droprecov_filter(pick_date, industry_list):
    """
    根据指定日期和行业筛选股票

    参数:
        pick_date (str): 筛选日期,如'2024-11-21'
        industry_list (list): 行业列表,如['食品饮料', '纺织服饰', '商贸零售']

    返回:
        DataFrame: 包含筛选后的股票信息,包括以下字段:
            - name: 股票名称
            - ts_code: 股票代码
            - Section_StartDate: 区间起始日期
            - Now_SecDate: 当前区间日期
            - PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays: 最大均值到最小均值的天数
            - PostSecStart_PGV_MaxRollAvg2Now_LastDays: 区间起始到最大均值的天数
    """
    import config.config_Ali as config
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')
    # 从数据库获取指定日期和行业的数据
    if isinstance(industry_list, str):
        industry_list = [industry_list]
    indus_list = ','.join(["'%s'" % item for item in industry_list])
    sql = f"""
    SELECT *
    FROM stocksfit.stkpick_droprecov 
    WHERE Pick_Date = '{pick_date}'
    AND industry in {(indus_list)}
    """
    df = pd.read_sql(sql, engine)
    df = df.drop_duplicates(subset=['ts_code'], keep='last')
    engine.dispose()

    # 根据条件筛选
    result = df[
        (df['PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays'] >= 1) &
        (df['DownConsecutive2Now_LastDays'] <= 2)
    ].sort_values(
        by=['PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays',
            'PostSecStart_PGV_MaxRollAvg2Now_LastDays'],
        ascending=[True, False]
    )[['name', 'ts_code', 'Section_StartDate', 'Now_SecDate',
       'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays',
       'PostSecStart_PGV_MaxRollAvg2Now_LastDays',
       'PreNowPeak2Now_LastDays']]

    return result


def track_indus_head(track_startdate, check_date, industry_list=None):
    """获取转折点在track_startdate之后的行业列表，获取行业totalmv排名居前的股票，筛选转折点与行业转折点相同的股票"""
    bottom_list, _ = cal_swindex_state(
        end_date=check_date, index_preturn_date=track_startdate)
    bottom_list_pick = bottom_list.query('end_date>=@track_startdate').copy()
    if industry_list is not None:
        bottom_list_pick = bottom_list_pick.query(
            'indus in @industry_list').copy()
    if len(bottom_list_pick) == 0:
        print(f'{track_startdate}日期后无Bottom转折行业')
        return pd.DataFrame()
    else:
        bottom_list_pick = bottom_list_pick.sort_values(
            by='End2Max_Ratio', ascending=False)
        print(f'{track_startdate}日期后的Bottom转折行业列表:',
              bottom_list_pick[['indus', 'end_date', 'End2Max_Ratio']])
    indus_list = bottom_list_pick['indus'].values.tolist()
    result_now = get_result_3(end_date=check_date)
    result_indus = result_now.query('industry in @indus_list')
    result_indus = result_indus.sort_values(by=['industry', 'Total_MV'],
                                            ascending=[True, False]).groupby('industry').head(20).copy()
    trade_dates = get_trade_date()
    bottom_list_pick = bottom_list_pick.rename(
        columns={'end_date': 'indus_bottomdate', 'indus': 'industry'})
    result_indus = pd.merge(result_indus, bottom_list_pick[['industry', 'indus_bottomdate']],
                            on='industry', how='left')
    result_indus['Now_SecDiff'] = result_indus.apply(
        lambda fn: conf_nowdiff(
            fn['Now_SecDate'], fn['indus_bottomdate'], trade_dates, fn['ts_code']),
        axis=1)
    result_indus_pick = result_indus.query('Now_SecDiff<=2').copy()
    return result_indus_pick


def track_pullstart_stocks(end_date, industry_list=[], trend_startdate=None,
                           recent_turndate=None, rise_stop_signal=True,
                           limit_num=80, store_mode=False,
                           recent_indus=[], recentindus_calstartdate=None
                           ):
    """计算指定行业内股票的强弱状态

    Args:
        industry_list (str|list): 行业名称或行业列表
        start_date (str): 起始日期，格式'YYYY-MM-DD'
        end_date (str): 结束日期，格式'YYYY-MM-DD'
        recent_turndate (str|list): 近期转折日期，可以是单个日期字符串或日期字符串列表

    Returns:
        pd.DataFrame: 包含行业内所有股票强弱指标的数据框，包括:
            - ts_code: 股票代码
            - stock_name: 股票名称
            - industry: 所属行业
            - cum_rel_return: 累计相对收益率(%)
            - bottom_return: 相对底部的反弹幅度(%)
            - bottom_days: 距离相对底部的天数
            - strength_ratio: 强势天数占比
            - max_strength: 最大强势幅度(%)
            - recent_strength: 近期强势状态
    """
    trade_dates = get_trade_date()

    # 处理recent_turndate参数，确保它是列表格式
    if recent_turndate is None:
        recent_turndate = [end_date]
    elif isinstance(recent_turndate, str):
        recent_turndate = [recent_turndate]
    elif not isinstance(recent_turndate, list):
        recent_turndate = [str(recent_turndate)]
    
    pre1month_date = (pd.to_datetime(end_date) - pd.DateOffset(months=1)).replace(day=1).strftime('%Y-%m-%d')
    
    recent_turndate = [date for date in recent_turndate if date > pre1month_date and date < end_date]
    
    # cal_swindex_state函数只接受单个日期，使用列表中的第一个日期
    Bottom_List, CumRet_List, _ = cal_swindex_state(
        end_date=end_date, index_preturn_date=recent_turndate[-1])
    Bottom_List = Bottom_List.sort_values(
        ['end_date', 'MaxDrop', 'Drop_Lastdays'], ascending=[False, True, False])
    Bottom_List = Bottom_List.reset_index(drop=True)
    Bottom_List = pd.merge(Bottom_List, CumRet_List[['indus', 'MinCumRet_Date', 'Ret2Now_Ratio',
                                                     'MinCumRet_DropDays', 'RetMax2Now_LastDays']],
                           on='indus', how='left')
    
    if industry_list is None or len(industry_list) == 0:

        # 针对recent_turndate中的每个日期进行循环筛选
        Cumret_List_Trend = pd.DataFrame()

        for turn_date in recent_turndate:
            # 计算MinCumRet_Date与turn_date的交易日间隔
            filtered_cumret = []
            for _, row in CumRet_List.iterrows():
                min_cumret_date = row['MinCumRet_Date']
                retmax2now_lastdays = row['RetMax2Now_LastDays']
                if pd.notnull(min_cumret_date):
                    # 计算交易日间隔
                    date_diff = abs(len(trade_dates[(trade_dates >= min(min_cumret_date, turn_date)) &
                                                   (trade_dates <= max(min_cumret_date, turn_date))]) - 1)
                    # 筛选间隔小于等于2天的条目
                    if date_diff <= 2 and retmax2now_lastdays < 10:
                        filtered_cumret.append(row)
            
            if filtered_cumret:
                # 转换为DataFrame并按Ret2Now_Ratio排序，选取前2个
                temp_df = pd.DataFrame(filtered_cumret)
                temp_df = temp_df.sort_values(by='Ret2Now_Ratio', ascending=False).head(3)
                # 汇总到结果DataFrame
                Cumret_List_Trend = pd.concat([Cumret_List_Trend, temp_df], ignore_index=True)

        Cumret_List_Strong = CumRet_List.query('RetMax2Now_LastDays<10 & MinCumRet_Date<@end_date & MinCumRet_DropDays>=2').copy()
        Cumret_List_Strong = Cumret_List_Strong.sort_values(by=['Ret2Now_Ratio'], ascending=[False]).head(8)
        
        Cumret_List_Trend = pd.concat([Cumret_List_Trend, Cumret_List_Strong], ignore_index=True)
        
        # 去重并获取行业列表
        if not Cumret_List_Trend.empty:
            Cumret_List_Trend = Cumret_List_Trend.drop_duplicates(subset=['indus'], keep='first')
            industry_list = Cumret_List_Trend['indus'].values.tolist()
        else:
            print('industry_list参数为空，且筛选结果亦为空')
            return

    if recent_indus is None:
        recent_indus = industry_list[0]
    
    if isinstance(recent_indus, str):
        recent_indus = [recent_indus]

    if recent_indus is not None and recentindus_calstartdate is None:
        print('recentindus_calstartdate参数必须不得为None')
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
    
    # indus_pickstart = index_turndate[-1]
    # if collect_indus:
    #     bottom_list_recent_ret = Bottom_List.query('MinCumRet_Date>=@trend_startdate & Ret2Now_Ratio>1 & '
    #                                             '((RetMax2Now_LastDays<=3 & MinCumRet_DropDays>=10) | '
    #                                             '(RetMax2Now_LastDays<2 & Ret2Now_Ratio>2))'
    #                                             ).sort_values(by=['MinCumRet_Date', 'Ret2Now_Ratio'],
    #                                                             ascending=[False, False])
        
    #     bottom_list_recent_ret_list = bottom_list_recent_ret['indus'].values.tolist()
    # else:
    bottom_list_recent_ret_list = []
    if len(industry_list)==0:
        if len(recent_indus)>0:
            industry_list = list(
                set(recent_indus + bottom_list_recent_ret_list))
            industry_df = Bottom_List.query('indus in @industry_list')[['indus', 'start_date', 'end_date',
                                                                        'MinCumRet_Date', 'End2Max_MaxDate',
                                                                        'Ret2Now_Ratio', 'Drop_Lastdays',
                                                                        'MaxDrop']].copy()
        industry_list = industry_df['indus'].values.tolist()

    elif isinstance(industry_list, str) or isinstance(industry_list, list):
        if isinstance(industry_list, str):
            industry_list = [industry_list]
        industry_list = list(
            set(industry_list + bottom_list_recent_ret_list))
        if len(recent_indus)>0:
            industry_list = list(set(recent_indus + industry_list))
        industry_df = Bottom_List.query('indus in @industry_list')[[
            'indus', 'start_date', 'end_date', 'MinCumRet_Date', 'End2Max_MaxDate',
            'Ret2Now_Ratio', 'Drop_Lastdays', 'MaxDrop']].copy()
    else:
        print(f'industry_list应为列表格式或为None')
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

    # 打印行业名称和日期,按日期由近及远排序
    sorted_df = industry_df[['indus', 'MinCumRet_Date', 'End2Max_MaxDate', 'Ret2Now_Ratio', 
                             'Drop_Lastdays', 'MaxDrop']].sort_values(
        by=['MinCumRet_Date', 'End2Max_MaxDate'], ascending=[False, False])
    for _, row in sorted_df.iterrows():
        print(f'行业: {row["indus"]}, 回撤转折日期: {row["MinCumRet_Date"]}, '
              f'转折日期: {row["End2Max_MaxDate"]}, 转折回升幅度: {row["Ret2Now_Ratio"]:.2f}, '
              f'下行天数: {row["Drop_Lastdays"]}, '
              f'下行幅度: {row["MaxDrop"]:.2f}')

    # 获取行业股票列表
    stock_info = get_stock_info()
    industry_stocks = stock_info[stock_info['industry'].isin(
        industry_df['indus'])].copy()

    # 初始化结果DataFrame
    result_data = []

    # 获取所有股票的最早起始日期
    earliest_start_date = None
    for industry, industry_group in industry_stocks.groupby('industry'):
        industry_row = industry_df[industry_df['indus'] == industry].iloc[0]
        industry_end_dt = pd.to_datetime(industry_row['end_date'])
        industry_start_day = (
            industry_end_dt - pd.DateOffset(months=2)).replace(day=1).strftime('%Y-%m-%d')
        industry_start_date = trade_dates[trade_dates >= industry_start_day][0]
        if earliest_start_date is None or industry_start_date < earliest_start_date:
            earliest_start_date = industry_start_date

    # 一次性获取所有股票数据
    if recentindus_calstartdate is not None and earliest_start_date > recentindus_calstartdate:
        earliest_start_date = recentindus_calstartdate
    all_stock_codes = industry_stocks['ts_code'].values.tolist()
    all_stock_data = get_stock_data(stk_code=all_stock_codes,
                                    start_date=earliest_start_date,
                                    end_date=end_date)

    result_store = get_result_3(end_date=end_date, stk_list=all_stock_codes, mode='all')
    result_store = result_store.drop_duplicates(subset=['ts_code'], keep='last')
    if result_store is None or len(result_store) == 0:
        print(f'{end_date}日期后无股票指标数据')
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
    
    result_store_secdate = result_store.query('ts_code in @all_stock_codes')['Section_StartDate'].dropna().min() \
              if not result_store.query('ts_code in @all_stock_codes')['Section_StartDate'].isna().all() else None
    
    if result_store_secdate is not None and result_store_secdate < earliest_start_date:
        earliest_start_date = result_store_secdate
    
    all_stock_codes = industry_stocks['ts_code'].values.tolist()
    all_stock_data = get_stock_data(stk_code=all_stock_codes,
                                    start_date=earliest_start_date,
                                    end_date=end_date)
    
    # 获取行业指数数据
    industry_data = get_swindex_data(
        start_date=earliest_start_date, end_date=end_date)

    if industry_data.index[-1] != all_stock_data['trade_date'].max():
        print(f'行业指数数据日期与股票日期不匹配，股票结束日期为{all_stock_data["trade_date"].max()},'
              f'行业指数结束日期为{industry_data.index[-1]}')

    # max_indexturn_date = max(
    #     [date for date in index_turndate if date <= end_date])

    # 按行业分组处理
    for industry, industry_group in industry_stocks.groupby('industry'):
        end2max_maxdate = industry_df[industry_df['indus']
                                      == industry]['End2Max_MaxDate'].iloc[0]
        mincumret_date = industry_df[industry_df['indus']
                                     == industry]['MinCumRet_Date'].iloc[0]
        indus_drop_enddate = industry_df[industry_df['indus']
                                   == industry]['end_date'].iloc[0]
        max2min_days = len(trade_dates[(trade_dates >= end2max_maxdate) & (
            trade_dates <= mincumret_date)]) - 1
        if max2min_days > 8:
            industry_start_date = end2max_maxdate
        # elif index_peakdate is not None:
        #     industry_start_date = index_peakdate
        else:
            industry_start_date = earliest_start_date

        # 获取行业指数
        industry_index = industry_data[industry]

        # 遍历行业内股票
        for code in industry_group['ts_code'].values:
            try:
                stock_info_row = industry_group[industry_group['ts_code']
                                                == code].iloc[0]
                stock_data = all_stock_data[all_stock_data['ts_code'] == code].copy()
                
                if len(stock_data) == 0:
                    continue
                
                stock_data = stock_data.set_index('trade_date')
                
                section_startdate = result_store.query('ts_code==@code')['Section_StartDate'].iloc[0]
                postsecstart_peakdate = result_store.query('ts_code==@code')['PostSecStart_PeakDate'].iloc[0]
                now_secdate = result_store.query('ts_code==@code')['Now_SecDate'].iloc[0]
                prenow_peakdate = result_store.query('ts_code==@code')['PreNow_PeakDate'].iloc[0]
                
                # if code == "601083.SH":
                #     print(code)
                
                # 检查是否有足够的数据
                if len(stock_data) < 10 or stock_data.index[0]>mincumret_date or pd.isnull(now_secdate):
                    continue
                
                # stock_data = stock_data[stock_data['trade_date']
                #                         >= industry_start_date].copy()
                

                # 计算日收益率前先检查close列是否有缺失值
                if stock_data['close'].isnull().any():
                    print(f"股票 {code} 存在停牌缺失数据,跳过处理")
                    continue

                stock_data['daily_ratio'] = stock_data['close'].pct_change()
                stock_data['daily_high_ratio'] = stock_data['high'] / stock_data['pre_close'] - 1
                stock_data['mov3day_sumratio'] = stock_data['daily_ratio'].rolling(
                    3).sum()

                stop_level = 0.95 * \
                    0.2 if code[:3] == '688' or code[0] == '3' or code[:2] == '83' else 0.95*0.1

                # 对齐数据日期
                common_dates = sorted(
                    list(set(stock_data.index) & set(industry_index.index)))

                # 检查对齐后的数据是否足够
                if len(common_dates) < 2:
                    print(f"股票 {code} 与行业指数对齐后数据不足,跳过处理")
                    continue

                # 计算日收益率
                stock_returns = stock_data.loc[common_dates, 'close'].pct_change(
                )
                # stock_data = stock_data.loc[common_dates].copy()
                industry_returns = industry_index[common_dates].pct_change()

                # 计算相对收益率及其累计值,检查是否有无效值
                relative_returns = (stock_returns - industry_returns).fillna(0)
                if relative_returns.isnull().any():
                    print(f"股票 {code} 相对收益率计算存在无效值,跳过处理")
                    continue

                cum_relative_returns = (1 + relative_returns).cumprod() - 1

                # 检查累计收益率序列是否为空或存在无效值
                if len(cum_relative_returns) == 0 or cum_relative_returns.isnull().any():
                    print(f"股票 {code} 累计收益率计算结果无效,跳过处理")
                    continue

                # 计算相对底部反弹指标
                bottom_idx = cum_relative_returns.idxmin()
                bottom_return = cum_relative_returns.iloc[-1] - \
                    cum_relative_returns.min()

                # 检查mincumret_date是否在common_dates中
                # if mincumret_date not in common_dates:
                #     print(f"股票 {code} 在行业回撤日期 {mincumret_date} 缺失数据,跳过处理")
                #     continue
                
                # if code == '600060.SH':
                #     print(code)
                
                
                cumret_return = cum_relative_returns.iloc[-1] - \
                    cum_relative_returns.loc[mincumret_date:].iloc[0]
                bottom_days = len(common_dates) - \
                    list(common_dates).index(bottom_idx) - 1
                post_lateturn_over5num = stock_data.loc[trend_startdate:]['daily_ratio'].apply(
                    lambda x: 1 if x > 0.05 else 0).sum()
                # postnowsec_over7num = len(
                #     stock_data.loc[now_secdate:].query('daily_ratio>0.05'))
                postnowsec_risestop_num = len(
                    stock_data.loc[now_secdate:].query('daily_high_ratio>=@stop_level'))
                postsecstart_risestop_num = len(
                    stock_data.loc[section_startdate:prenow_peakdate].query('daily_high_ratio>=@stop_level')) \
                        if section_startdate<prenow_peakdate else len(
                    stock_data.loc[section_startdate:].query('daily_high_ratio>=@stop_level'))
                
                prerise_close = stock_data['close'].iloc[-2]
                recent5day_mincls = stock_data.iloc[-6:-1]['close'].min()
                recent5day_maxsumratio = round((prerise_close / recent5day_mincls - 1) * 100, 2)
                # postcumret_mindailyratio = stock_data.loc[mincumret_date:]['daily_ratio'].min()
                postcumret_mindailyratio_date = stock_data.loc[mincumret_date:]['daily_ratio'].idxmin()
                mincumret2sec_sumratio = round(
                    (stock_data.loc[mincumret_date:, 'close'].iloc[0] / 
                     stock_data.loc[section_startdate:, 'close'].iloc[0] - 1) * 100, 2)
                postnowsec_rise_avgratio = stock_data.loc[now_secdate:].query('daily_ratio>0')['daily_ratio'].mean() \
                    if len(stock_data.loc[now_secdate:].query('daily_ratio>0')) > 0 else 1
                # postsecstart_length = min(len(stock_data.loc[section_startdate:postsecstart_peakdate]), 5)
                postsecstart_rise_avgratio = stock_data.loc[section_startdate:postsecstart_peakdate
                    ].query('daily_ratio>0')['daily_ratio'].mean() \
                    if len(stock_data.loc[section_startdate:]) > 5 \
                        and len(stock_data.loc[section_startdate:postsecstart_peakdate
                            ].query('daily_ratio>0')) > 0 \
                    else 1
                postnowsec2postsec_riseavgratio_diffratio = round(postnowsec_rise_avgratio/postsecstart_rise_avgratio - 1, 3)
                
                # 找到与section_startdate最接近的recent_turndate
                closest_recent_turndate = min(recent_turndate,
                                            key=lambda x: abs(len(trade_dates[(trade_dates >= min(section_startdate, x)) &
                                                                              (trade_dates <= max(section_startdate, x))])))

                prerecentturn_maxidx = stock_data.loc[min(section_startdate, closest_recent_turndate):max(section_startdate, closest_recent_turndate),
                                                      'close'].idxmax()
                if section_startdate < closest_recent_turndate and prerecentturn_maxidx < closest_recent_turndate:
                    recent_bottom_date = stock_data.loc[prerecentturn_maxidx:]['close'].idxmin()
                    peak2recentbottom_lastdays = len(stock_data.loc[prerecentturn_maxidx:recent_bottom_date]) - 1
                    peak2recentbottom_sumratio = round(
                        (stock_data.loc[recent_bottom_date, 'close'] / stock_data.loc[prerecentturn_maxidx, 'close'] - 1)*100, 2)
                    peak2recentbottom_avgratio = round(peak2recentbottom_sumratio/peak2recentbottom_lastdays, 2) \
                        if peak2recentbottom_lastdays > 0 else 0
                    if peak2recentbottom_lastdays == 0:
                        peak2recentbottom_lastdays = result_store.query('ts_code==@code')['PreNowPeak2Now_LastDays'].iloc[0]
                        peak2recentbottom_sumratio = result_store.query('ts_code==@code')['PreNowPeak2Now_SumRatio'].iloc[0]
                        peak2recentbottom_avgratio = result_store.query('ts_code==@code')['PreNowPeak2Now_AvgRatio'].iloc[0]
                else:
                    recent_bottom_date = now_secdate
                    prerecentturn_maxidx = prenow_peakdate
                    peak2recentbottom_lastdays = result_store.query('ts_code==@code')['PreNowPeak2Now_LastDays'].iloc[0]
                    peak2recentbottom_sumratio = result_store.query('ts_code==@code')['PreNowPeak2Now_SumRatio'].iloc[0]
                    peak2recentbottom_avgratio = result_store.query('ts_code==@code')['PreNowPeak2Now_AvgRatio'].iloc[0]
                
                bottom2now_lastdays = len(stock_data.loc[recent_bottom_date:])
                prepostbottom_days_diff = bottom2now_lastdays - peak2recentbottom_lastdays
                
                # break_prerecentpeak_ratio = round(
                #     (stock_data.loc[now_secdate:, 'close'].max() / 
                #      max(stock_data.loc[prerecentturn_maxidx, 'close'], stock_data.loc[prerecentturn_maxidx, 'open']) - 1) * 100, 3)
                
                # 计算与recent_bottom_date最接近的recent_turndate的间隔天数
                recent2bottom_days = min([
                    len(stock_data.loc[rt:recent_bottom_date]) - 1 if rt < recent_bottom_date
                    else len(stock_data.loc[recent_bottom_date:rt]) - 1
                    for rt in recent_turndate
                ])
                
                postsecstart_maxsumratio = round(
                    (stock_data.loc[postsecstart_peakdate, 'close'] / 
                     stock_data.loc[section_startdate, 'close'] - 1)*100, 3)
                secstart2peak_lastdays = len(stock_data.loc[section_startdate:postsecstart_peakdate]) - 1
                if secstart2peak_lastdays > 0:
                    secstart2peak_avgratio = round(
                        postsecstart_maxsumratio / secstart2peak_lastdays, 3)
                else:
                    secstart2peak_avgratio = 0
                
                pre1date = stock_data.index[-2]
                if pre1date > mincumret_date:
                    postcumret_maxidx = stock_data.loc[mincumret_date:pre1date, 'close'].idxmax()
                #     postcumret_maxidx2pre1day_lastdays = len(
                #         stock_data.loc[postcumret_maxidx:pre1date]) - 1
                #     postcumret_maxidx2pre1day_avgratio = round((stock_data.loc[pre1date, 'close'] / 
                #                                                 stock_data.loc[postcumret_maxidx, 'close'] - 1) /
                #                                                postcumret_maxidx2pre1day_lastdays * 100, 2) \
                #     if postcumret_maxidx2pre1day_lastdays > 0 else 0
                else:
                    postcumret_maxidx = '-'
                #     postcumret_maxidx2pre1day_lastdays = 99
                #     postcumret_maxidx2pre1day_avgratio = 99

                postsec2pre1date_sumratio = round(
                    (stock_data['close'].iloc[-2]/stock_data.loc[section_startdate, 'close'] - 1) * 100, 2) \
                    if section_startdate < pre1date else 0
                
                # 检查open列是否存在缺失值
                if pd.isnull(stock_data.loc[postcumret_mindailyratio_date, 'open']):
                    print(
                        f"股票 {code} 在 {postcumret_mindailyratio_date} 开盘价缺失,跳过处理")
                    continue

                # postcumret_mindailyratio_threshold = stock_data.loc[
                #     postcumret_mindailyratio_date, 'open']
                # postcumret_maxdailyratio = stock_data.loc[mincumret_date:]['daily_ratio'].max()
                pre3day_sumratio = stock_data['daily_ratio'].iloc[-4:-1].sum()
                postcumret_max_mov3day_sumratio = stock_data.loc[mincumret_date:]['mov3day_sumratio'].max(
                )
                now_daily_ratio = stock_data['daily_ratio'].iloc[-1]
                now_high_ratio = stock_data['high'].iloc[-1] / \
                    stock_data['pre_close'].iloc[-1] - 1

                # 检查mincumret_date后的数据是否足够
                if len(stock_data.loc[mincumret_date:]) <= 2:
                    cumret2pre1day_avgratio = 0.99
                    cumret2pre1day_avgturnover = 99
                else:
                    cumret2pre1day_avgratio = (stock_data['close'].iloc[-2] / stock_data.loc[mincumret_date:]['close'].iloc[0] - 1) / \
                        (len(stock_data.loc[mincumret_date:]) - 2)
                    cumret2pre1day_avgturnover = stock_data.loc[mincumret_date:,
                                                                'turnover'].iloc[:-1].mean()

                stk_data_postcumret = stock_data.loc[mincumret_date:].copy()
                stk_data_postcumret['turnover_rank'] = stk_data_postcumret['turnover'].rank(
                    ascending=False)
                postcumret_now_turnover_rank = stk_data_postcumret['turnover_rank'].iloc[-1]
                postcumret_now2avgturnover_ratio = round(
                    stk_data_postcumret['turnover'].iloc[-1] / cumret2pre1day_avgturnover - 1, 3)

                stock_data_bfrecentbottom = stock_data.loc[:recent_bottom_date].copy()
                if stock_data_bfrecentbottom['close'].iloc[-1] <= stock_data_bfrecentbottom['close'].iloc[-4:-1].min():
                    recentbottom_und3min = 'True'
                else:
                    recentbottom_und3min = 'False'
                

                # 判断是否超过前一交易日收盘价
                now_overpre1day = 99
                now_overpre1day_prop = 99
                stock_data_aftbottom = stock_data.loc[recent_bottom_date:].copy()
                if len(stock_data_aftbottom) > 1:
                    stock_data_aftbottom['min_clsopen'] = stock_data_aftbottom[['close', 'open']].min(axis=1)
                    stock_data_aftbottom['pre_minclsopen'] = stock_data_aftbottom['min_clsopen'].shift(1)
                    if not stock_data_aftbottom.iloc[1:].empty:
                        now_overpre1day = len(stock_data_aftbottom.iloc[1:].query('pre_minclsopen>=close'))
                        now_overpre1day_prop = round(now_overpre1day / len(stock_data_aftbottom.iloc[1:]), 3)

                recentbottom_low = stock_data_bfrecentbottom['low'].iloc[-1]
                if len(stock_data_bfrecentbottom.iloc[:-1].query('close < @recentbottom_low')) > 0:
                    cover_startdate = stock_data_bfrecentbottom.iloc[:-1].query('close < @recentbottom_low').index[-1]
                    recentbottom_downcoverdays = len(stock_data_bfrecentbottom.loc[cover_startdate:recent_bottom_date]) - 1
                else:
                    recentbottom_downcoverdays = len(stock_data_bfrecentbottom) - 1
                
                # postcumret_break_date = '-'
                # break_lastdays = 99
                # if len(stock_data.loc[postcumret_mindailyratio_date:]) > 0 and \
                #         postcumret_mindailyratio < -0.02:
                #     close_data = stock_data.loc[postcumret_mindailyratio_date:, 'close']
                #     if len(close_data) > 1:
                #         max_close = close_data.iloc[1:].max()
                #         if max_close > postcumret_mindailyratio_threshold:
                #             # 检查是否有满足条件的值
                #             condition_met = close_data[1:][close_data.values[1:]
                #                                            > postcumret_mindailyratio_threshold]
                #             if not condition_met.empty:
                #                 postcumret_break_date = condition_met.index[0]
                #                 break_lastdays = len(
                #                     stock_data.loc[postcumret_mindailyratio_date:postcumret_break_date]) - 1

                # 判断是否涨停
                now_stop_flag = 1 if now_daily_ratio >= stop_level else 0

                # 计算连续超过7%的天数
                consecutive_over7num = (stock_returns.loc[mincumret_date:] > 0.07).astype(
                    int).groupby((stock_returns.loc[mincumret_date:] <= 0.07).cumsum()).sum().max()

                # 计算bottom_date之前的最大累计相对收益率
                bottom_date_idx = common_dates.index(bottom_idx)
                pre_bottom_returns = cum_relative_returns.iloc[:bottom_date_idx]
                max_pre_bottom_return = pre_bottom_returns.max() if len(
                    pre_bottom_returns) > 0 else 0
                bottom_max_diff = cum_relative_returns[bottom_idx] - \
                    max_pre_bottom_return

                # 计算检查周期内累计收益率
                stk_checkperiod_sumratio = 0
                if recent_indus is not None and industry in recent_indus:
                    if recentindus_calstartdate < mincumret_date:
                        stk_checkperiod_sumratio = round(
                            (stock_data.loc[:mincumret_date]['close'].iloc[-1] /
                             stock_data.loc[recentindus_calstartdate:]['close'].iloc[0] - 1) * 100, 3)
                    else:
                        print('recentindus_calstartdate日期输入错误，晚于mincumret_date')
                    
                # 计算与所有recent_turndate的最小差异比率
                nowsec_recentturn_diffratio = round(min([
                    abs((stock_data.loc[now_secdate, 'close'] / stock_data.loc[rt, 'close'] - 1) * 100)
                    for rt in recent_turndate if rt in stock_data.index
                ]), 2) if any(rt in stock_data.index for rt in recent_turndate) else 99
                
                # 汇总股票结果
                result_data.append({
                    'ts_code': code,
                    'name': stock_info_row['name'],
                    'industry': industry,
                    'cum_rel_return': round(cum_relative_returns.iloc[-1] * 100, 2),
                    'relative_bottom_return': round(bottom_return * 100, 2),
                    'cumret2now_return': round(cumret_return * 100, 2),
                    'relative_bottom_days': bottom_days,
                    'relative_bottom_date': bottom_idx,
                    'Indus_DropEndDate': indus_drop_enddate,
                    'MinCumRet_Date': mincumret_date,
                    'relative_bottom_max_diff': round(bottom_max_diff * 100, 2),
                    'post_lateturn_over5num': post_lateturn_over5num,
                    'postcumret_consecutive_over7num': consecutive_over7num,
                    # 'postnowsec_over7num': postnowsec_over7num,
                    'postnowsec_risestop_num': postnowsec_risestop_num,
                    'postsecstart_risestop_num': postsecstart_risestop_num,
                    'recent5day_maxsumratio': recent5day_maxsumratio,
                    # 'postcumret_break_lastdays': break_lastdays,
                    # 'postcumret_nowbreak': 1 if postcumret_break_date == end_date else 0,
                    # 'postcumret_mindailyratio_date': postcumret_mindailyratio_date,
                    # 'postcumret_maxdailyratio': round(postcumret_maxdailyratio * 100, 2),
                    'postcumret_pre3day_sumratio': round(pre3day_sumratio * 100, 2),
                    'postcumret_max_mov3day_sumratio': round(postcumret_max_mov3day_sumratio * 100, 2),
                    # 'cumret2pre1day_avgratio': round(cumret2pre1day_avgratio * 100, 2),
                    'postcumret_maxidx': postcumret_maxidx,
                    # 'postcumret_maxidx2pre1day_lastdays': postcumret_maxidx2pre1day_lastdays,
                    # 'postcumret_maxidx2pre1day_avgratio': postcumret_maxidx2pre1day_avgratio,
                    'postcumret_now_turnover_rank': postcumret_now_turnover_rank,
                    'postcumret_now2avgturnover_ratio': postcumret_now2avgturnover_ratio,
                    'mincumret2sec_sumratio': abs(mincumret2sec_sumratio),
                    'postsec2pre1date_sumratio': postsec2pre1date_sumratio,
                    'stk_checkperiod_sumratio': stk_checkperiod_sumratio,
                    'postnowsec2postsec_riseavgratio_diffratio': postnowsec2postsec_riseavgratio_diffratio,
                    'peak2recentbottom_lastdays': peak2recentbottom_lastdays,
                    'peak2recentbottom_sumratio': peak2recentbottom_sumratio,
                    'peak2recentbottom_avgratio': peak2recentbottom_avgratio,
                    'recentbottom2now_lastdays': bottom2now_lastdays,
                    'prepost_recentbottom_daysdiff': prepostbottom_days_diff,
                    'secstart2peak_avgratio': secstart2peak_avgratio,
                    'recent_bottom_date': recent_bottom_date,
                    'recent2bottom_days': recent2bottom_days,        
                    'recentbottom_und3min': recentbottom_und3min,   
                    'recentbottom_downcoverdays': recentbottom_downcoverdays,
                    'now_daily_ratio': round(now_daily_ratio * 100, 2),
                    'now_high_ratio': round(now_high_ratio * 100, 2),
                    'rise_stop_flag': now_stop_flag,
                    'now_overpre1day': now_overpre1day,
                    'now_overpre1day_prop': now_overpre1day_prop,
                    'nowsec_recentturn_diffratio': nowsec_recentturn_diffratio
                    # 'break_prerecentpeak_ratio': break_prerecentpeak_ratio
                })

            except Exception as e:
                print(f"处理股票 {code} 时出错: {str(e)}")
                continue

    # 转换为DataFrame并排序
    result_df = pd.DataFrame(result_data)
    if len(result_df) > 0:
       merge_cols = list(set(result_store.columns) - set(result_df.columns)) + ['ts_code']
       result_df = pd.merge(
            result_df, result_store[merge_cols], on='ts_code', how='left', suffixes=('', '_store')).copy()
       result_df = result_df.sort_values(
                ['industry', 'cumret2now_return'], ascending=[True, False])   # result_df = result_df.query('cumret2now_return>0')
    else:
        print(f"没有找到符合条件的股票")
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
 
    # 合并行业end_date信息
    # industry_df = industry_df.rename(columns={'indus': 'industry'})
    # result_df = pd.merge(result_df, industry_df[['industry', 'MinCumRet_Date']], on='industry', how='left')

    # 计算bottom_date与industry_end_date的交易日间隔天数
    try:
        result_df['date_diff'] = result_df.apply(
            lambda x: len(trade_dates[(trade_dates >= min(x['relative_bottom_date'], x['MinCumRet_Date'])) &
                                      (trade_dates <= max(x['relative_bottom_date'], x['MinCumRet_Date']))]) - 1,
            axis=1)
    except Exception as e:
        print(f"计算date_diff时出错: {str(e)}")
        pdb.set_trace()
    result_df['sort_position'] = result_df.groupby('industry').cumcount() + 1

    # 通过get_result_3获取股票指标数据，计算Section_StartDate与industry_end_date的交易日间隔天数
    # result_store = get_result_3(end_date=end_date, stk_list=all_stock_codes)
    if len(result_df) > 0:
        result_df['Turn_Turnover_Diff'] = round(
            result_df['PostTurn_AvgTurnover'] - result_df['PreTurn_MaxTurnover'], 3)
        result_df['NowSec2Turn_PRV_MeanDiff'] = result_df.apply(
            lambda fn: round(fn['NowSec_PRV_Top3Mean'] /
                             fn['Turn_PRV_Top3Mean'], 3)
            if pd.notnull(fn['NowSec_PRV_Top3Mean']) and pd.notnull(fn['Turn_PRV_Top3Mean'])
            and fn['Period_TurnDate'] < fn['Now_SecDate'] and fn['Turn_PRV_Top3Mean'] > 0 else None,
            axis=1)
        result_df['PostSecStart_Max2SecPeak_PRV_Diff'] = result_df.apply(
            lambda fn: round((fn['PostSecStart_PGV_MaxRollAvg'] /
                             fn['SectionPeak_PRV_Top3Mean'] - 1) * 100, 3)
            if pd.notnull(fn['PostSecStart_PGV_MaxRollAvg']) and pd.notnull(fn['SectionPeak_PRV_Top3Mean'])
            and fn['SectionPeak_PRV_Top3Mean'] > 0 else 0,
            axis=1)
        result_df['PostNowSec_Max2PreNowPeak_PRV_Diff'] = result_df.apply(
            lambda fn: round((fn['PostNowSec_PGV_MaxRollAvg'] /
                             fn['PreNowPeak_PRV_Top3Mean'] - 1) * 100, 3)
            if pd.notnull(fn['PostNowSec_PGV_MaxRollAvg']) and pd.notnull(fn['PreNowPeak_PRV_Top3Mean'])
            and fn['PreNowPeak_PRV_Top3Mean'] != 0 else 0, axis=1)
        
        result_df['PostNowSec2PreNowPeak_PRV2Price_RatioDiff'] = round(result_df['PostNowSec_Max2PreNowPeak_PRV_Diff'] - 
            result_df['BreakPreNowPeak_Ratio'], 3)
        
        result_df['PostSecStart_MaxDrop_RatioDiff'] = result_df.apply(
            lambda fn: round(abs(fn['PostSecStart_MaxDrop']) / fn['PostSecStart_RiseRatio'], 3)
            if fn['PostSecStart_RiseRatio'] != 0 else 0, axis=1)
        result_df['PostSecStart_MaxDrop_LastDaysDiff'] = result_df.apply(
            lambda fn: round(fn['PostSecStart_MaxDrop_LastDays'] / fn['PostSecStart_LastDays'], 3)
            if fn['PostSecStart_LastDays'] != 0 else 0, axis=1)
        # result_df['PostTurn_Peak2Turn_PRV_Diff'] = round(
        #     result_df['PostTurnPeak_PRV_Top3Mean'] / result_df['Turn_PRV_Top3Mean'], 3)
        # result_df['PostSecStart_Peak2Start_PRV_Diff'] = round(
        #     result_df['PostSecPeak_PRV_Top3Mean'] / result_df['SectionStart_PRV_Top3Mean'], 3)
        result_df['SecStart2SecPeak_PRV_Diff'] = round(
            result_df['SectionStart_PRV_Top3Mean'] / result_df['SectionPeak_PRV_Top3Mean'], 3)
        result_df['NowSec2PreNowPeak_PRV_Diff'] = round(
            result_df['NowSec_PRV_Top3Mean'] / result_df['PreNowPeak_PRV_Top3Mean'], 3)
        
        result_df['SecConcave_AvgRatioBand'] = result_df.apply(
            lambda fn: round(fn['SecConcave_RatioBand'] / fn['SecConcave_LastDays'], 3)
            if fn['SecConcave_LastDays'] != 0 else None, axis=1)
        result_df['SecConcave_AftB_LastDays_Propotion'] = result_df.apply(
            lambda fn: round(fn['SecConcave_AftBottom_LastDays'] / fn['SecConcave_LastDays'], 3)
            if fn['SecConcave_LastDays'] != 0 else None, axis=1)
        result_df['Turn2NowSec_PRA_BandDiff'] = result_df.apply(
            lambda fn: round(fn['Turn2NowSec_PRA_UpBand'] / fn['Turn2NowSec_PRA_LowBand'], 3)
            if fn['Turn2NowSec_PRA_UpBand'] != 0 and fn['Turn2NowSec_PRA_LowBand'] != 0 else None, axis=1)
        
        result_df['PRA2Cls_Percentile_Diff'] = result_df.apply(
            lambda fn: round(fn['NowSec_MaxPRA_Percentile_PostTurn'] - fn['PostNowSec_MaxCls_Percentile_PostTurn'], 3),
            axis=1)
        
        result_df['PRA2Cls_Percentile_Ratio'] = result_df.apply(
            lambda fn: round(fn['NowSec_MaxPRA_Percentile_PostTurn'] / fn['PostNowSec_MaxCls_Percentile_PostTurn'], 3) 
            if fn['PostNowSec_MaxCls_Percentile_PostTurn'] != 0 else None,
            axis=1)
        
        result_df['PostNowSecMaxPRA_PostTurnRank5PRA_Diff'] = result_df.apply(
            lambda fn: round(fn['PostNowSec_PGV_MaxRollAvg'] / fn['PostTurn_Rank5_PRA'], 3)
            if fn['PostTurn_Rank5_PRA'] != 0 else None, axis=1)
        
        
        # result_df['secstart_diff'] = result_df.apply(
        #     lambda fn: conf_nowdiff(
        #         fn['Section_StartDate'], index_turndate, trade_dates),
        #     axis=1)
        result_df['nowsec_recent_diff'] = result_df.apply(
            lambda fn: conf_nowdiff(
                fn['Now_SecDate'], recent_turndate, trade_dates),
            axis=1)
        result_df['secstart_recent_diff'] = result_df.apply(
            lambda fn: conf_nowdiff(
                fn['Section_StartDate'], recent_turndate, trade_dates),
            axis=1)
        # result_df['nowsec_former_diff'] = result_df.apply(
        #     lambda fn: conf_nowdiff(
        #         fn['Now_SecDate'], index_turndate, trade_dates),
        #     axis=1) if len(index_turndate) > 1 else 99
        result_df['relative_bottom_date_diff'] = result_df.apply(
            lambda fn: conf_nowdiff(fn['relative_bottom_date'],
                                    fn['MinCumRet_Date'], trade_dates),
            axis=1)
        result_df['nowsec_minret_diff'] = result_df.apply(
            lambda x: len(trade_dates[(trade_dates >= min(x['MinCumRet_Date'], x['Now_SecDate'])) &
                                      (trade_dates <= max(x['MinCumRet_Date'], x['Now_SecDate']))]) - 1
            if pd.notnull(x['MinCumRet_Date']) and pd.notnull(x['Now_SecDate']) else None,
            axis=1)
        result_df['secstart_minret_diff'] = result_df.apply(
            lambda x: len(trade_dates[(trade_dates >= min(x['MinCumRet_Date'], x['Section_StartDate'])) &
                                      (trade_dates <= max(x['MinCumRet_Date'], x['Section_StartDate']))]) - 1
            if pd.notnull(x['MinCumRet_Date']) and pd.notnull(x['Section_StartDate']) else None,
            axis=1)

        result_df['PostNowSec_AvgRatio_Pre'] = result_df.apply(
            lambda x: round(
                (x['PostNowSec_SumRatio'] - x['Now_DayRatio']) / (x['PostNowSec_LastDays'] - 1), 3)
            if x['PostNowSec_LastDays'] > 1 else 99, axis=1)
        if 'NowSec_PRA2Close_CoverDays_Diff' not in result_df.columns or result_df['NowSec_PRA2Close_CoverDays_Diff'].sum() == 0:
            result_df['NowSec_PRA2Close_CoverDays_Diff'] = result_df['NowSec_PGV_MaxRollAvg_UpCoverDays'] - \
                result_df['NowSec_MaxClose_UpCoverDays']

        if 'SecStart_PRA2Close_CoverDays_Diff' not in result_df.columns or result_df['SecStart_PRA2Close_CoverDays_Diff'].sum() == 0:
            result_df['SecStart_PRA2Close_CoverDays_Diff'] = result_df['SecStart_PGV_MaxRollAvg_UpCoverDays'] - \
                result_df['SecStart_MaxClose_UpCoverDays']

        # 筛选交易日间隔在3天以内的数据

        result_df_turn = result_df.query('(secstart_minret_diff<3 | '
                                         '(Section_StartDate<MinCumRet_Date & '
                                         '(NowSec_LowBand_CoverDays>=38 | mincumret2sec_sumratio<10)))',
                                         #  'relative_bottom_date<=MinCumRet_Date & '
                                         engine='python').copy()

    else:
        # result_df_turn = result_df.query('relative_bottom_date<MinCumRet_Date',
        #                                  engine='python').copy()
        print('stk_result数据库缺失对应日期数据：', end_date)
        return None, None, None

    # 按行业分组并取前limit_num条数据
    result_df_turn = result_df_turn.query('sort_position<=@limit_num').copy()
    result_df_turn['turn_sort_position'] = result_df_turn.groupby(
        'industry').cumcount() + 1

    result_df_cal = result_df.groupby('industry').head(20).copy()
    indus_sort = result_df_cal[['industry', 'cumret2now_return']].groupby(
        'industry')['cumret2now_return'].mean()
    result_df_cal['rise_stop_num'] = result_df.groupby('industry')['rise_stop_flag'].transform('sum')
    result_df_cal['industry_total_num'] = result_df.groupby('industry')['ts_code'].transform('count')
    indus_sort_list = indus_sort.sort_values(ascending=False).index
    print('行业排序结果:')
    for i, industry in enumerate(indus_sort_list):
        rise_stop_num = result_df_cal.query("industry==@industry")["rise_stop_num"].iloc[0]
        industry_total_num = result_df_cal.query("industry==@industry")["industry_total_num"].iloc[0]
        print(f'{i+1}. {industry}: {indus_sort[industry]:.2f}, '
              f'涨停数/总数:{rise_stop_num}/{industry_total_num}')
    
    # 按照indus_sort_list的顺序重新排序result_df_turn
    result_df_turn['industry'] = pd.Categorical(
        result_df_turn['industry'], categories=indus_sort_list, ordered=True)
    result_df_turn = result_df_turn.sort_values(['industry', 'cumret2now_return', 'cum_rel_return'],
                                                ascending=[True, False, False])
    # result_df_turn = result_df_turn.sort_values(by=['industry', 'NowSec_PRA2Close_CoverDays_Diff'], ascending=[True, False])
    result_df_turn['Indus_ClassType'] = '-'

    result_df['industry'] = pd.Categorical(
        result_df['industry'], categories=indus_sort_list, ordered=True)
    result_df = result_df.sort_values(['industry', 'cumret2now_return', 'cum_rel_return'],
                                      ascending=[True, False, False])
    result_df_copy = result_df.copy()

    # min_mincumret_date = result_df_copy['MinCumRet_Date'].min()
    cumret_threshold = -10
    all_industry =  list(set(industry_list + recent_indus))

    
    low_threshold = -25   
    pull_start_list = result_df_copy.query(
        'industry in @all_industry & '
        # 'postcumret_consecutive_over7num<5 & '
        # 'Now_PRA_Rate>50 & '
        # 'PostNowSec_PRA_MaxRate_BreachCount<=2 & '
        # 'rise_stop_flag==1 & '
        '(now_overpre1day<=1 | '
        '(recentbottom2now_lastdays>8 & now_overpre1day_prop<0.5) |'
        'Cal_Date in @recent_turndate) & '
        # 'recentbottom_und3min=="True" & '
        'recentbottom_downcoverdays>3 & '
        'postcumret_consecutive_over7num<=2 & '
        '(nowsec_recent_diff<=1 | '
        '(recent2bottom_days<1 & Peak_Pres_Num>=2 ) | '
        '(nowsec_recent_diff<5 & nowsec_recentturn_diffratio<2)) & '
        # '(recent2bottom_days<1 & Peak_Pres_Num>=2 & '
        # 'Peak_Pres_RatioBand<100 & industry in @industry_list)) & '
        'postnowsec_risestop_num<2'
        # '@low_threshold<BreakPreNowPeak_Ratio<3'
        # 'Now_PRA_Rate>=50 & '
        # 'PostNowSec_PRA_MaxRate_BreachCount==1'
        # 'peak2recentbottom_lastdays<=3'
        # 'Now_Over3Mean=="True" & '
        # 'NowSec_PGV_MaxRollAvg_UpCoverDays>40 & '
        # 'PostNowSec_Max2PreNowPeak_PRV_Diff>0.5'
        # 'post_lateturn_over5num<=2 & '
        # 'recent5day_over7num<=2 & '
        # 'recent5day_risestop_num<2 & '
        # '((nowsec_recent_diff<=3 & Pre_PreNow_Sec_Over7Num<3) | '
        # '(nowsec_former_diff<=3 & PostNowSec_Over7Num<3)) & '
        # 'cumret2now_return>@cumret_threshold & '
        # 'NowSec_SecConcave_TO_Sum>100 & '
        # 'PostNowSec_MaxTurnover>1'
        ).copy()
    
    if rise_stop_signal:
       pull_start_list = pull_start_list.query(
            'rise_stop_flag==1').copy()
    else:
       pull_start_list = pull_start_list.query(
           '(Recent3Day_PRA_MaxRate_CoverDays>40 & Now_PRA_Rate==Recent3Day_PRA_MaxRate) | '
           '(Recent3Day_VRA_MaxRate_CoverDays>40 & Now_VRA_Rate==Recent3Day_VRA_MaxRate)'
       ).copy()
    # #    pull_start_list_risestop = pull_start_list.query(
    # #        'now_high_ratio>7').copy()
    #    if len(pull_start_list_risestop) > 0:
    #        pull_start_list = pull_start_list_risestop
    #    else:
    #        print('涨停筛选数量为零，结束输出pull_start_list')
    # else:
    #     pull_start_list = result_df_copy.query(
    #         'industry in @all_industry & '
    #         'rise_stop_flag==1 & '
    #         'nowsec_recent_diff<=1 & '
    #         # '| recent2bottom_days<=1) & '
    #         # 'recentbottom_und3min=="True" & '
    #         'recentbottom_downcoverdays>5 & '
    #         'postnowsec_risestop_num<=2 & '
    #         'postcumret_consecutive_over7num<=1'
    #     ).copy()

    pull_start_list['Indus_ClassType'] = pull_start_list.apply(
            lambda fn: 'Recent' if fn['industry'] in recent_indus else 'Former', axis=1)
    
    pull_start_list['In_TurnBreak'] = pull_start_list.apply(
        lambda x: result_df_turn.loc[result_df_turn['ts_code']
                                     == x['ts_code'], 'turn_sort_position'].iloc[0]
        if x['ts_code'] in result_df_turn['ts_code'].values else -1, axis=1)

    pull_start_list['industry'] = pd.Categorical(
        pull_start_list['industry'], categories=indus_sort_list, ordered=True)

    if not rise_stop_signal:
        pull_start_list = pull_start_list.sort_values(
        by=['Indus_ClassType', 'industry', 'BreakPreNowPeak_Ratio',],
        ascending=[True, True, False])
    else:
        pull_start_list = pull_start_list.sort_values(
        by=['Indus_ClassType', 'industry', 'NowSec_MaxPRA_Percentile_PostSectionPeak'], 
        ascending=[True, True, False])
    
    pull_start_list['pullstart_sort_position'] = pull_start_list.groupby('industry').cumcount() + 1
    
    result_df_turn['In_TurnBreak'] = 99
    result_df['In_TurnBreak'] = 99

    pullstart_col = ['ts_code', 'name', 'industry', 'MinCumRet_Date', 'Indus_DropEndDate',
                     'rise_stop_flag', 'In_TurnBreak', 'cumret2now_return',
                     'recentbottom_downcoverdays', 'peak2recentbottom_avgratio',
                     'Turn2NowSec_PRA_BandDiff', 
                     'PRA2Cls_Percentile_Diff',
                     'PRA2Cls_Percentile_Ratio',
                     'PostTurn_VGVRank5_NearNowDate_Days',
                     'NowSec_MaxPRA_Percentile_PostTurn', 
                     'PostNowSec_MaxCls_Percentile_PostTurn',
                     'Now_PRA_Rate',
                     'BreakPreNowPeak_Ratio',
                     'PostNowSecMaxPRA_PostTurnRank5PRA_Diff',
                     'Recent3Day_MaxPGV_Percentile_PostTurn',
                     'Recent3Day_MaxPGV_PostTurn_Rank',
                     'Recent3Day_MaxPRA_PostTurn_Rank',
                     'prepost_recentbottom_daysdiff',
                     'peak2recentbottom_lastdays',
                     'recentbottom2now_lastdays',
                     'PostNowSec_Max2PreNowPeak_PRV_Diff',
                     'PostNowSec2PreNowPeak_PRV2Price_RatioDiff',
                     'PostSecStart_Max2SecPeak_PRV_Diff', 
                     'PostSecStart_PRA_MaxRate_BreachCount',
                     'PostNowSec_PRA_MaxRate_BreachCount',
                     'SecValley_GapRatio',
                     'PostSecStart_PGV_Max2Mean_Ratio',
                     'PostSecStart_PGV_Now2Mean_Ratio',
                     'NowSec_PGV_MaxRollAvg_UpCoverDays',
                     'BreakPreNowSec_Ratio', 
                     'PostSecStart_MaxDrop_RatioDiff',
                     'PostSecStart_MaxDrop_LastDaysDiff',
                     'PostSecStart_Over3MovAvg_ContiDays',
                     'PostNowSec_Over3MovAvg_ContiDays',
                     'PreNowSec_PreSec_Over3MovAvg_ContiDays',
                     'PreNowSec_PreSec_Over3MovAvg_MaxRate',
                     'PreNowSec_PostSec_Und3MovAvg_MaxRate',
                     'PostSecStart_Over3MovAvg_MaxRate',
                     'PostSecStart_MaxTurnover',
                     'PostSecStart_MaxTurnover_Date',
                     'PreNowPeak_PRV_Top3Mean',
                     'PostPreNowPeak_PGV_MinRollAvg',
                     'PostPreNowPeak_PGV_MinRollAvg_Date',
                     'PostPreNowPeak_PostMinPRA_MaxPRA',
                     'PostPreNowPeak_PostMinPRA_MaxPRA2Now_Days',
                    #  'Peak2Sec_OverPre1Day_Days',
                    #  'Peak2Sec_OverPre1Day_Prop',
                    #  'Peak2Sec_OverPre1Day_MeanRatio',
                    #  'PostNowSec_BelowPre1Day_Days',
                    #  'PostNowSec_BelowPre1Day_Prop',
                    #  'PostNowSec_BelowPre1Day_MeanRatio',
                     'PreNowPeak2NowSec_LastDays',
                     'PreNowPeak2Now_SumRatio',
                     'PreNowSec_AvgRatio',
                     'PostSecStart_LastDays', 'PostSecStart_RiseRatio',
                     'Peak_Pres_Num', 'Peak_Pres_Lastdays',
                     'Peak_Pres_MaxPrice', 'Peak_Pres_MaxPrice_Ratio', 
                     'Peak_Pres_RatioBand',
                     'PostSecPeak_PRV_Top3Mean_CoverDays',
                     'PostSecPeak_PRA2Close_CoverDays_Diff',
                     'PostSecStart_VGV_Max2Mean_Ratio',
                     'Section_StartDate', 'Now_SecDate',
                     'SecConcave_AvgRatioBand',
                     'SecConcave_AftB_LastDays_Propotion',
                     'SecConcave_RatioBand',
                     'SecConcave_LastDays',
                     'SecConcave_AftBottom_LastDays',
                     'SecStart2SecPeak_PRV_Diff',
                     'NowSec2PreNowPeak_PRV_Diff',
                     'postcumret_now2avgturnover_ratio',
                     'NowSec_SecConcave_TO_Sum', 'NowSec_SecConcave_LastDays', 
                     'PostSecStart_Over7Num',
                     'postnowsec_risestop_num',
                     'postnowsec2postsec_riseavgratio_diffratio',
                     'recent5day_maxsumratio',
                     'Now_PGV_RollAvg_CoverDays',
                     'PostSecStart_Sec_MaxDrop_LastDays',
                     'PostSecStart_Sec_MaxDrop_SumRatio',
                     'Target_Ratio',
                     'NowSec_PRA_LastBreach_ContinuousDays',
                     'SecStart_PRA_LastBreach_ContinuousDays',
                     'postcumret_now_turnover_rank', 
                     'NowSec_PRA2Close_CoverDays_Diff',
                     'NowSec_MaxClose_UpCoverDays',
                     'PostNowSec_LastDays',
                     'NowSec_MaxPRA_Percentile_PostSectionPeak', 'NowSec_MinPRA_Percentile_PostSectionPeak',
                     'PostNowSec_AvgTurnover', 
                     'postcumret_consecutive_over7num', 'post_lateturn_over5num',
                     'PostSecStart_PGV_MaxRollAvg', 'Recent3Day_PGV_MeanRollAvg',
                     'sort_position', 
                     'Latest_EffPeak2NowSec_Diff',
                     'SecPeak2NowSec_PRA_UpBand', 'SecPeak2NowSec_PRA_LowBand',
                     'PostPreNow_MinDailyRatio_Recover_State',
                     'PostSecPeak_MinDailyRatio_Recover_State',
                     'PostPreNow_MinDailyRatio_Recover_Days',
                     'SecStart_PRA2Close_CoverDays_Diff',
                     'SecStart_PGV_MaxRollAvg_UpCoverDays', 'SecStart_MaxClose_UpCoverDays',
                     'NowSec_PGV_UpCoverPeriod_Max2Min_DiffRatio',
                     'SecStart_PGV_UpCoverPeriod_Max2Min_DiffRatio',
                     'Period_TurnDate',
                     'NowSec_PRV_Top3Mean', 'SectionStart_PRV_Top3Mean', 
                     'PostNowSec_PGV_MaxRollAvg',
                     'NowSec2Turn_PRV_MeanDiff', 
                     'Section_PeakDate', 'PostSecStart_PeakDate', 'PreNow_PeakDate',
                     'PreNowSec_PGV_MaxRollAvg', 
                     'NowSec_LowBand_CoverDays', 'NowSec2SecStart_Ratio',
                     'PostNowSec_Adverse2Trend', 'PostNowSecWLS_R2', 'PostNowSecWLS_Deviation',
                     'PostNowSec_Over7Num', 'PostNowSec_ExtreRatio']
    pull_start_list = pull_start_list[pullstart_col + [
        col for col in pull_start_list.columns if col not in pullstart_col]].copy()
    result_df_turn = result_df_turn[pullstart_col + [
        col for col in result_df_turn.columns if col not in pullstart_col]].copy()
    # result_df_turn['Check_Date'] = end_date

    if store_mode and len(pull_start_list)>0:
        # 优化数据库连接和查询
        import config.config_Ali as config
        conf = config.configModel()
        engine = create_engine(
            'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                conf.DC_DB_PORT) + '/stocksfit')

        # result_df_turn_store = result_df_turn.groupby(
        #     'industry').head(30).copy()
        
        # if len(pull_start_list)>0:
        #     pull_start_list_store = pull_start_list.copy()
        #     # 确保索引唯一
        #     result_df_turn_store = result_df_turn_store.reset_index(drop=True)
        #     pull_start_list_store = pull_start_list_store.reset_index(drop=True)
            
        #     result_df_turn_store['Class_Type'] = 'TurnBreak'
        #     pull_start_list_store['Class_Type'] = 'PullStart'
            
            # curr_store_data = pd.concat(
            #     [result_df_turn_store, pull_start_list_store], ignore_index=True)
            
        # else:
            # curr_store_data = result_df_turn_store.copy()
        
        pull_start_list_store = pull_start_list[pullstart_col].reset_index(drop=True).copy()
        pull_start_list_store['Check_Date'] = end_date
        # pull_start_list_store['Class_Type'] = 'PullStart'
        curr_store_data = pull_start_list_store.copy()
        
        # 替换无穷大为999
        curr_store_data.replace([np.inf, -np.inf], 999, inplace=True)

        # 将Class字段移到第一列
        # if 'Class_Type' in curr_store_data.columns:
        #     cols = curr_store_data.columns.tolist()
        #     cols.remove('Class_Type')
        #     curr_store_data = curr_store_data[['Class_Type'] + cols]

        if len(curr_store_data) > 0:
            # 检查表是否存在
            inspector = inspect(engine)
            if 'stkpick_pullstart' not in inspector.get_table_names(schema='stocksfit'):
                try:
                    pd.io.sql.to_sql(curr_store_data, 'stkpick_pullstart', engine, index=False, schema='stocksfit',
                                     if_exists='append')
                    print(f'{end_date} 筛选数据已存储', '\n已存储PullStart股票条目：', len(pull_start_list))
                except Exception as e:
                    print('An error occured:', e)
                    pdb.set_trace()
            else:
                industry_list = curr_store_data['industry'].unique().tolist()
                if not industry_list:
                    print("警告: 没有行业数据可存储")
                else:
                    check_indus = ','.join(
                        ["'%s'" % item for item in industry_list])
                    store_sql = f"""select * from stocksfit.stkpick_pullstart 
                                    where Check_Date='{end_date}'
                                    """
                    # from sqlalchemy.orm import sessionmaker
                    # from sqlalchemy.sql import text

                    Session = sessionmaker(bind=engine)
                    session = Session()
                    try:
                        turntrack_storedata = pd.read_sql(store_sql, engine)

                        if len(turntrack_storedata) > 0:
                            try:
                                delete_sql = text("delete from stocksfit.stkpick_pullstart "
                                                  "where Check_Date= :end_date")
                                with session.begin():
                                    session.execute(
                                        delete_sql, {"end_date": end_date})
                                session.commit()
                            except Exception as e:
                                session.rollback()
                                print('删除数据时发生错误:', e)
                    except Exception as e:
                        print('查询数据时发生错误:', e)
                    session.close

                try:
                    print(f'{end_date} 筛选数据存储', '\n存储PullStart股票条目：', len(pull_start_list))
                    pd.io.sql.to_sql(curr_store_data, 'stkpick_pullstart', engine, index=False, schema='stocksfit',
                                     if_exists='append')
                    print('存储成功')
                except Exception as e:
                    print('An error occured:', e)
                
        engine.dispose()

    else:
        print('未存储筛选股票数据')

    # pull_start_list = pull_start_list.query(
    #     'rise_stop_flag==1 & '
    #     'NowSec_SecConcave_TO_Sum>100 & PostNowSec_Max2PreNowPeak_PRV_Diff>1'
    #     ).sort_values(by=['postcumret_now2avgturnover_ratio',
    #                       'PostNowSec_Max2PreNowPeak_PRV_Diff'], ascending=[True, False])
    col_list = ['Indus_ClassType'] + \
        [col for col in pull_start_list.columns if col != 'Indus_ClassType']
    pull_start_list = pull_start_list[col_list].copy()

    pull_indus_count = pull_start_list[['ts_code', 'industry']].groupby('industry').count()
    pull_indus_count = pull_indus_count.rename(columns={'ts_code': 'count'})
    pull_indus_count = pull_indus_count.sort_values(by='count', ascending=False)
    print('PullStart股票行业分布：\n', pull_indus_count.iloc[:len(pull_indus_count)])


    # first_col_2 = ['ts_code', 'name', 'industry', 'stk_checkperiod_maxdailyratio',
    #                'NowSec_PRA2Close_CoverDays_Diff',
    #                'NowSec_PGV_MaxRollAvg_UpCoverDays', 'NowSec_MaxClose_UpCoverDays',
    #                'SecStart_PRA2Close_CoverDays_Diff',
    #                'SecStart_PGV_MaxRollAvg_UpCoverDays', 'SecStart_MaxClose_UpCoverDays',
    #                'NowSec_PGV_UpCoverPeriod_Max2Min_DiffRatio',
    #                'SecStart_PGV_UpCoverPeriod_Max2Min_DiffRatio',
    #                'DownConsecutive2Now_LastDays']

    # result_df_turn = result_df_turn[first_col_2 +
    #                                 [col for col in result_df_turn.columns if col not in first_col_2]]

    if recent_indus is not None:
        result_df_recentindus = result_df.query(
            'industry in @recent_indus').copy()
        if len(result_df_recentindus) > 0:
            result_df_recentindus = result_df_recentindus[pullstart_col +
                                                          [col for col in result_df_recentindus.columns
                                                           if col not in pullstart_col]]
            result_df_recentindus = result_df_recentindus.sort_values(
                by=['industry', 'stk_checkperiod_sumratio'], ascending=[True, False]).groupby('industry').head(20)
            # result_df_recentindus = result_df_recentindus.sort_values(
            #     by=['industry', 'NowSec_PRA2Close_CoverDays_Diff'], ascending=[True, False])
    else:
        result_df_recentindus = pd.DataFrame()

    pull_start_list2 = pull_start_list[['ts_code', 'name', 'industry',
                                        'Now_SecDate', 'Period_TurnDate', 'Section_PeakDate',
                                        'Now_PRA_Percentile_PostSectionPeak',
                                        'NowSec_MaxPRA_Percentile_PostSectionPeak',
                                        'NowSec_MaxPRA_Percentile_PostTurn',
                                        'PostNowSec_PRA_MaxRate',
                                        'PostNowSec_PRA_MaxRate_Date',
                                        'PostSecStart_PRA_MaxRate',
                                        'PostSecStart_PRA_MaxRate_Date',
                                        'Now_PRA_Rate',
                                        'PostNowSec_PRA_MaxRate_BreachCount',
                                        'PostSecStart_PRA_MaxRate_BreachCount',
                                        'PostNowSec_MaxRate_PRA_Percentile',
                                        'PostNowSec_MaxRate2Now_LastDays',
                                        'PostNowSec_LastDays',
                                        'SecPeak2NowSec_PRA_UpBand',
                                        'SecPeak2NowSec_PRA_LowBand',
                                        'Turn2NowSec_PRA_UpBand',
                                        'Turn2NowSec_PRA_LowBand',
                                        'NowSec_PGV_MaxRollAvg_UpCoverDays',
                                        'PostNowSec_Max2PreNowPeak_PRV_Diff']]
    # result_df_adj = result_df.query('PostNowSec_PRA_MaxRate>50 & '
    #                                 'PostNowSec_PRA_MaxRate_Date==@end_date & '
    #                                 'NowSec_PRA_FirstBreach_Date<=@end_date & '
    #                                 # 'Now_SecDate=="2025-01-27" & '
    #                                 # 'industry in @industry_list & '
    #                                 'PostNowSec_PRA_MaxRate_BreachCount==1'
    #                                 # 'NowSec_PGV_MaxRollAvg_UpCoverDays>30'
    #                                 # 'NowSec_MaxPRA_Percentile_PostSectionPeak<1'
    #                                 ).sort_values(by=['PostNowSec_PRA_MaxRate_Date',
    #                                                   'cumret2now_return'],
    #                                               ascending=[False, False]).copy()
    # result_df_adj = result_df.sort_values(by=['industry', 'Total_MV'], 
    #                                       ascending=[True, False]).groupby('industry').head(20)
    result_df_adj = result_df.copy()
    # adj_list = result_df_head['ts_code'].values.tolist() + result_df_recentindus['ts_code'].values.tolist
    
    # result_df_adj = result_df.query('Now_PRA_Rate>50 & ts_code in @adj_list'
    #                                 ).sort_values(['industry', 'cumret2now_return', 'cum_rel_return'],
    #                                   ascending=[True, False, False]).copy()
    
    # if recent_indus is not None:
        # result_df_adj = result_df_adj.query('industry in @recent_indus').copy()
    
    df_col = ['ts_code', 'name', 'industry',
              'SecConcave_RatioBand',
              'SecConcave_LastDays',
              'SecConcave_AftBottom_LastDays',
              'PostSecStart_LastDays',
              'PostSecStart_RiseRatio',
              'Section_StartDate', 'Now_SecDate', 'Period_TurnDate', 'Section_PeakDate',
              'Peak2Sec_LastDays', 'Peak2Sec_SumRatio',
              'PostSecPeak_PGV_MinRollAvg_Date',
              'PostSecPeak_MaxTO_Eff_Date',
              'PostSecPeak_PGV_MinRollAvg2Now_LastDays',
            #   'PostSecStart_MaxDrop_LastDays',
            #   'PostSecStart_MaxDrop',
              'PostSecStart_Sec_MaxDrop_LastDays',
              'PostSecStart_Sec_MaxDrop_SumRatio',
              'Target_Ratio',
              'SecStart_PRA_Breach_Count',
              'NowSec_PRA_Breach_Count',
              'NowSec_PRA_LastBreach_ContinuousDays',
              'PreNowSec_LastDays', 'PostNowSec_LastDays',
              'Now_PGV_RollAvg_CoverDays',
              'PostNowSec_PRA_MaxRate',
              'PostNowSec_PRA_MaxRate_Date',
              'PostSecStart_PRA_MaxRate',
              'PostSecStart_PRA_MaxRate_Date',
              'NowSec_PRA2Close_CoverDays_Diff',
              'NowSec_PGV_MaxRollAvg_UpCoverDays', 'NowSec_MaxClose_UpCoverDays',
              'NowSec_PGV_UpCoverPeriod_Max2Min_DiffRatio',
              'Now_PRA_Percentile_PostSectionPeak',
              'NowSec_MaxPRA_Percentile_PostSectionPeak',
              'NowSec_MaxPRA_Percentile_PostTurn',
              'Now_PRA_Rate',
              'PostNowSec_PRA_MaxRate_BreachCount',
              'PostSecStart_PRA_MaxRate_BreachCount',
              'PostNowSec_MaxRate_PRA_Percentile',
              'PostNowSec_MaxRate2Now_LastDays',
              'SecPeak2NowSec_PRA_UpBand',
              'SecPeak2NowSec_PRA_LowBand',
              'Turn2NowSec_PRA_UpBand',
              'Turn2NowSec_PRA_LowBand',
              'PostNowSec_Max2PreNowPeak_PRV_Diff',
              'PostNowSec_MaxRate_Post2Pre_DiffRatio',
              'SecStart_PRA_FirstBreach_Date',
              'NowSec_PRA_FirstBreach_Date',]
    result_df_adj = result_df_adj[df_col +
                                  [col for col in result_df_adj.columns if col not in df_col]]
    
    
    # pull_start_list_core['Max2SecPeak_PRV_Diff_Rank'] = pull_start_list_core['PostSecStart_Max2SecPeak_PRV_Diff'].rank(ascending=False)
    # pull_start_list_core['PostSecStart_PGV_Max2Mean_Ratio_Rank'] = pull_start_list_core['PostSecStart_PGV_Max2Mean_Ratio'].rank(ascending=False)
    # pull_start_list_core['BreakPreNowPeak_Ratio_abs'] = pull_start_list_core['BreakPreNowPeak_Ratio'].abs()
    # pull_start_list_core['BreakPreNowPeak_Ratio_Rank'] = pull_start_list_core['BreakPreNowPeak_Ratio_abs'].rank(ascending=False)
    # pull_start_list_core['SecStart_PRA_Breach_Count_Rank'] = pull_start_list_core['SecStart_PRA_Breach_Count'].rank(ascending=False)
    # pull_start_list_core['SecValley_GapRatio_Rank'] = pull_start_list_core['SecValley_GapRatio'].rank(ascending=False)
    # pull_start_list_core['pullstart_sort_position_Rank'] = pull_start_list_core['pullstart_sort_position'].rank(ascending=True)
    # pull_start_list_core['PostSecStart_AvgRatio_Rank'] = pull_start_list_core['PostSecStart_AvgRatio'].rank(ascending=True)
    # pull_start_list_core['PullStart_Score'] = pull_start_list_core['Max2SecPeak_PRV_Diff_Rank'] + pull_start_list_core['PostSecStart_PGV_Max2Mean_Ratio_Rank'] + \
    #                                             pull_start_list_core['BreakPreNowPeak_Ratio_Rank'] + pull_start_list_core['SecStart_PRA_Breach_Count_Rank'] + \
    #                                             pull_start_list_core['SecValley_GapRatio_Rank'] + pull_start_list_core['pullstart_sort_position_Rank'] + \
    #                                             pull_start_list_core['PostSecStart_AvgRatio_Rank']
    # pull_start_list_core = pull_start_list_core.sort_values(by=['industry', 'PullStart_Score'], ascending=[True, True])
    # pull_start_list_core = pull_start_list_core.drop(columns=['Max2SecPeak_PRV_Diff_Rank', 'PostSecStart_PGV_Max2Mean_Ratio_Rank', 
    #                                                           'BreakPreNowPeak_Ratio_Rank', 'SecStart_PRA_Breach_Count_Rank', 
    #                                                           'SecValley_GapRatio_Rank', 'pullstart_sort_position_Rank',
    #                                                           'PostSecStart_AvgRatio_Rank', 'BreakPreNowPeak_Ratio_abs'])
    
    pull_start_list['Max2SecPeak_PRV_Diff_Rank'] = pull_start_list['PostSecStart_Max2SecPeak_PRV_Diff'].rank(ascending=False)
    pull_start_list['PostSecStart_PGV_Max2Mean_Ratio_Rank'] = pull_start_list['PostSecStart_PGV_Max2Mean_Ratio'].rank(ascending=False)
    pull_start_list['BreakPreNowPeak_Ratio_abs'] = pull_start_list['BreakPreNowPeak_Ratio'].abs()
    pull_start_list['BreakPreNowPeak_Ratio_Rank'] = pull_start_list['BreakPreNowPeak_Ratio_abs'].rank(ascending=False)
    pull_start_list['SecStart_PRA_Breach_Count_Rank'] = pull_start_list['SecStart_PRA_Breach_Count'].rank(ascending=False)
    pull_start_list['SecValley_GapRatio_Rank'] = pull_start_list['SecValley_GapRatio'].rank(ascending=False)
    pull_start_list['pullstart_sort_position_Rank'] = pull_start_list['pullstart_sort_position'].rank(ascending=True)
    pull_start_list['PostSecStart_AvgRatio_Rank'] = pull_start_list['PostSecStart_AvgRatio'].rank(ascending=True)
    pull_start_list['PullStart_Score'] = pull_start_list['Max2SecPeak_PRV_Diff_Rank'] + pull_start_list['PostSecStart_PGV_Max2Mean_Ratio_Rank'] + \
                                                pull_start_list['BreakPreNowPeak_Ratio_Rank'] + pull_start_list['SecStart_PRA_Breach_Count_Rank'] + \
                                                pull_start_list['SecValley_GapRatio_Rank'] + pull_start_list['pullstart_sort_position_Rank'] + \
                                                pull_start_list['PostSecStart_AvgRatio_Rank']
    # pull_start_list = pull_start_list.sort_values(by=['industry', 'PullStart_Score'], ascending=[True, True])
    pull_start_list = pull_start_list.sort_values(by='PRA2Cls_Percentile_Diff', ascending=True)
    pull_start_list = pull_start_list.drop(columns=['Max2SecPeak_PRV_Diff_Rank', 'PostSecStart_PGV_Max2Mean_Ratio_Rank', 
                                                              'BreakPreNowPeak_Ratio_Rank', 'SecStart_PRA_Breach_Count_Rank', 
                                                              'SecValley_GapRatio_Rank', 'pullstart_sort_position_Rank',
                                                              'PostSecStart_AvgRatio_Rank', 'BreakPreNowPeak_Ratio_abs'])
    
    if 'pullstart_sort_position' in pull_start_list.columns:
                cols = pull_start_list.columns.tolist()
                cols.remove('pullstart_sort_position')
                pull_start_list = pull_start_list[['pullstart_sort_position'] + cols].copy()
    
    pull_start_list_core = pull_start_list.query('Now_PRA_Rate>=47 & PostNowSec_PRA_MaxRate_BreachCount<=1 & '
                                                 'Now_PRA_Rate==PostNowSec_PRA_MaxRate & PostNowSec_LastDays>2'
                                            ).copy()

    return result_df_adj, result_df_turn, pull_start_list, pull_start_list_core, result_df_recentindus


def cal_stock_industry_strength(stk_code, stock_data=None, start_date=None, end_date=None, bottom_list=None):
    """计算股票相对所属行业指数的强势特征

    Args:
        stock_code (str): 股票代码
        start_date (str, optional): 起始日期，格式'YYYY-MM-DD'
        end_date (str, optional): 结束日期，格式'YYYY-MM-DD'

    Returns:
        pd.DataFrame: 包含强势特征的DataFrame,包括:
            - stock_returns: 股票日收益率序列
            - industry_returns: 行业指数日收益率序列 
            - relative_returns: 相对收益率序列
            - cumulative_relative_returns: 累计相对收益率序列
            - relative_return: 总体相对收益率
            - strength_days: 强势天数
            - max_strength_ratio: 最大强势幅度
            - recent_strength: 近期强势状态
    """

    # 获取股票所属行业
    stock_info = get_stock_info()
    industry = stock_info.query('ts_code==@stk_code')['industry'].iloc[0]

    if stock_data is None:
        # 获取同行业股票代码
        same_industry_stocks = stock_info[stock_info['industry']
                                          == industry]['ts_code'].tolist()
        # 获取股票数据
        stock_data = get_stock_data(stk_code=same_industry_stocks,
                                    start_date=start_date, end_date=end_date)
        stock_data = stock_data.sort_values(
            ['ts_code', 'trade_date'], ascending=[True, True])
        if len(stock_data) == 0:
            return pd.DataFrame()
    stk_data = stock_data[stock_data['ts_code'] == stk_code].copy()
    stk_data = stk_data.set_index('trade_date')

    if bottom_list is None:
        trade_dates = get_trade_date()
        recentdrop_startdate = trade_dates[trade_dates <= end_date][-5]
        bottom_list, _ = cal_swindex_state(
            end_date=end_date, index_preturn_date=recentdrop_startdate)
    bottom_list = bottom_list.sort_values(
        ['end_date', 'MaxDrop', 'Drop_Lastdays'], ascending=[False, True, False])
    bottom_list = bottom_list.reset_index(drop=True)

    # 检查行业是否存在于Bottom_List中
    if industry not in bottom_list['indus'].values:
        return pd.DataFrame()  # 如果行业不存在则返回空DataFrame

    # 获取行业对应的End2Max_MaxDate作为计算起始日期
    industry_maxdate = bottom_list[bottom_list['indus']
                                   == industry]['End2Max_MaxDate'].iloc[0]
    industry_startdate = min(bottom_list[bottom_list['indus'] == industry]['start_date'].iloc[0], start_date) \
        if start_date is not None else bottom_list[bottom_list['indus'] == industry]['start_date'].iloc[0]
    industry_enddate = bottom_list[bottom_list['indus']
                                   == industry]['end_date'].iloc[0]
    industry_end2max_ratio = bottom_list[bottom_list['indus']
                                         == industry]['End2Max_Ratio'].iloc[0]

    # 获取行业和指数数据，使用industry_maxdate作为起始日期
    industry_data = get_swindex_data(
        start_date=industry_startdate, end_date=end_date)
    if industry not in industry_data.columns:
        return pd.DataFrame()  # 如果行业数据不存在则返回空DataFrame

    zz800_data = get_index_data(
        stk_code='000906.SH', start_date=industry_startdate, end_date=end_date)
    if zz800_data is None or len(zz800_data) == 0:
        return pd.DataFrame()  # 如果指数数据不存在则返回空DataFrame

    zz800_data = zz800_data.set_index('trade_date')

    # 获取行业指数数据并确保日期对应
    industry_index = industry_data[industry]
    zz800_index = zz800_data['close']

    # 检查数据是否为空
    if len(stock_data) == 0 or len(industry_index) == 0 or len(zz800_index) == 0:
        return pd.DataFrame()

    # 分别计算股票相对行业和行业相对指数的收益率
    # 股票相对行业使用股票的日期范围
    try:
        stk_common_dates = sorted(list(set(stk_data.index) &
                                       set(industry_index.index)))
        if not stk_common_dates:  # 如果没有共同日期则返回空DataFrame
            return pd.DataFrame()

        stock_returns = stk_data.loc[stk_common_dates, 'close'].pct_change()
        industry_returns_for_stock = industry_index.loc[stk_common_dates].pct_change(
        )
        relative_returns = (
            stock_returns - industry_returns_for_stock).fillna(0)
        cumulative_relative_returns = (1 + relative_returns).cumprod() - 1

        # 行业相对指数使用行业的日期范围
        industry_common_dates = sorted(list(set(industry_index.loc[industry_maxdate:].index) &
                                            set(zz800_index.loc[industry_maxdate:].index)))
        if not industry_common_dates:  # 如果没有共同日期则返回空DataFrame
            return pd.DataFrame()

        industry_returns = industry_index[industry_common_dates].pct_change()
        zz800_returns = zz800_index[industry_common_dates].pct_change()
        industry_relative_returns = (
            industry_returns - zz800_returns).fillna(0)
        industry_cumulative_relative_returns = (
            1 + industry_relative_returns).cumprod() - 1

        # 计算各个cumulative_returns最小值对应日期
        stk_min_cumret_date = cumulative_relative_returns.idxmin()
        industry_min_cumret_date = industry_cumulative_relative_returns.idxmin()

        # 计算各个cumulative_returns最小值对应下跌幅度
        stk_min_cumret_drop = cumulative_relative_returns.min(
        ) - cumulative_relative_returns.loc[:stk_min_cumret_date].max()
        industry_min_cumret_drop = industry_cumulative_relative_returns.min(
        ) - industry_cumulative_relative_returns.loc[:industry_min_cumret_date].max()

        # 计算从stk_min_cumret_date至最新日期的相对收益变动值
        stk_recent_ret = cumulative_relative_returns.iloc[-1] - \
            cumulative_relative_returns.loc[stk_min_cumret_date]

        # 计算行业内所有股票从industry_min_cumret_date到end_date的收益率
        industry_stocks = stock_info[stock_info['industry']
                                     == industry]['ts_code'].tolist()
        industry_returns_rank = []
        for ind_stk in industry_stocks:
            try:
                ind_stk_data = stock_data[(stock_data['ts_code'] == ind_stk) &
                                          (stock_data['trade_date'] >= industry_min_cumret_date)].copy()
                ind_stk_data = ind_stk_data.set_index('trade_date')
                if len(ind_stk_data) >= 2:
                    ret = (ind_stk_data['close'].iloc[-1] /
                           ind_stk_data['close'].iloc[0]) - 1
                    industry_returns_rank.append(ret)
            except:
                continue

        # 计算当前股票的收益率排名分位
        if len(industry_returns_rank) > 0:
            current_ret = (stk_data.loc[end_date, 'close'] /
                           stk_data.loc[industry_min_cumret_date, 'close']) - 1
            rank_percentile = sum(
                r >= current_ret for r in industry_returns_rank) / len(industry_returns_rank)
        else:
            rank_percentile = None

        # 创建时序数据DataFrame,使用index=[0]避免scalar values错误
        ts_df = pd.DataFrame({
            'ts_code': [stk_code],
            'stock_name': [stock_info.query('ts_code==@stk_code')['name'].iloc[0]],
            'industry': [industry],
            'stk_min_cumret_date': [stk_min_cumret_date],
            'industry_min_cumret_date': [industry_min_cumret_date],
            'stk_min_cumret_drop': [round(stk_min_cumret_drop*100, 4)],
            'industry_min_cumret_drop': [round(industry_min_cumret_drop*100, 4)],
            # 新增从最低点至今的相对收益变动值
            'stk_recent_ret': [round(stk_recent_ret*100, 4)],
            'industry_rank_percentile': [round(rank_percentile, 4) if rank_percentile is not None else None],
            'industry_drop_enddate': [industry_enddate],  # 新增行业end_date
            'industry_rise_maxdate': [industry_maxdate],
            # 新增End2Max_Ratio
            'industry_end2max_ratio': [round(industry_end2max_ratio, 4)],
        })

        return ts_df, cumulative_relative_returns

    except Exception as e:
        print(f"处理股票 {stk_code} 时出错: {str(e)}")
        return pd.DataFrame(), pd.Series()


def cal_target_pgv(stk_list, check_date):
    """计算stk_list中股票的目标PGV值"""
    if isinstance(stk_list, str):
        stk_list = [stk_list]
    result_store = get_result_3(end_date=check_date, stk_list=stk_list)
    result_store['In_MaxValleyGapValue'] = round(
        (result_store['PostNowSec_MaxValleyGapValue'] + result_store['PostSecStart_MaxValleyGapValue'])/2, 3)
    result_store['In_MedianValleyGapValue'] = round(
        (result_store['PostNowSec_MedianValleyGapValue'] + result_store['PostSecStart_MedianValleyGapValue'])/2, 3)
    result_store['In_Quntl_ValleyGapValue'] = round(
        (result_store['PostNowSec_MedianValleyGapValue'] + result_store['PostSecStart_ValleyGapValue_HighQuntl'])/2, 3)
    result_store['Out_MaxPeakGapValue'] = round(
        (result_store['PostNowSec_MaxPeakGapValue'] + result_store['PostSecStart_MaxPGV'])/2, 3)
    result_store['Out_MedianPeakGapValue'] = round(
        (result_store['PostNowSec_MedianPeakGapValue'] + result_store['PostSecStart_PeakGapValue_HighQuntl'])/2, 3)
    result_store['Break_Pre1Day_Threshold'] = result_store.apply(lambda fn: round(fn['Now_PGV_RollAvg']*3 - fn['Recent2Day_MeanPeakGapValue']*2, 3), 
                                                                 axis=1)
    result_store['Break_MaxPRA_Threshold'] = result_store.apply(lambda fn: round(fn['PostSecStart_PGV_MaxRollAvg']*3 - fn['Recent2Day_MeanPeakGapValue']*2, 3), 
                                                                axis=1)
    
    result_store['Break_PostTurn_Rank5PRA_PGV'] = result_store.apply(lambda fn: round(fn['PostTurn_Rank5_PRA']*3 - fn['Recent2Day_MeanPeakGapValue']*2, 3) 
                                                                     if fn['PostTurn_Rank5_PRA']>0 else 0, axis=1)

    In_target_pgv = result_store[['ts_code', 'name',
                                  'industry', 
                                  'In_MedianValleyGapValue',
                                  'In_Quntl_ValleyGapValue',
                                  'In_MaxValleyGapValue',
                                  'Recent3Day_MaxValleyGapValue', 'PostNowSec_MedianValleyGapValue',
                                  'PostSecStart_ValleyGapValue_HighQuntl',
                                  'PostSecStart_MaxValleyGapValue', 
                                  'Section_StartDate']].copy()
 
    Out_target_pgv = result_store[['ts_code', 'name',
                                   'industry', 
                                   'Break_PostTurn_Rank5PRA_PGV',
                                   'PostTurn_Rank5_PGV',
                                   'Out_MaxPeakGapValue',
                                   'Break_Pre1Day_Threshold', 
                                   'Break_MaxPRA_Threshold', 'PostSecStart_MaxPGV',
                                   'Out_MedianPeakGapValue',
                                   'Recent3Day_MaxPeakGapValue', 'PostNowSec_MaxPeakGapValue',
                                   'PRA2Cls_Percentile_Diff', 
                                   'NowSec_PRA2Close_CoverDays_Diff', 'PostNowSecMaxPRA_PostTurnRank5PRA_Diff',
                                   ]].copy()

    Out_target_pgv['Out_Break_target'] = None

    for index in result_store.index:
        sectionpeak_prv_top3mean = result_store.loc[index,
                                                    'SectionPeak_PRV_Top3Mean']
        postturnpeak_prv_top3mean = result_store.loc[index,
                                                     'PostTurnPeak_PRV_Top3Mean']
        prenowpeak_prv_top3mean = result_store.loc[index,
                                                   'PreNowPeak_PRV_Top3Mean']
        postsecpeak_prv_top3mean = result_store.loc[index,
                                                    'PostSecPeak_PRV_Top3Mean']
        # 避免None值导致的报错，使用0替代None进行比较
        postsecstart_maxpgv = result_store.loc[index,
                                               'PostSecStart_PGV_MaxRollAvg']
        postturn_top3pgv = result_store.loc[index, 'PostTurnPeak_PRV_Top3Mean']
        threshold_level = max(0 if pd.isna(postsecstart_maxpgv) else postsecstart_maxpgv,
                              0 if pd.isna(postturn_top3pgv) else postturn_top3pgv)
        # first_level = max(0 if pd.isna(prenowpeak_prv_top3mean) else prenowpeak_prv_top3mean,
        #                   0 if pd.isna(postsecpeak_prv_top3mean) else postsecpeak_prv_top3mean)
        # second_level = max(0 if pd.isna(sectionpeak_prv_top3mean) else sectionpeak_prv_top3mean,
        #                    0 if pd.isna(threshold_level) else threshold_level)

        recent2day_pgv = result_store.loc[index,
                                          'Recent2Day_MeanPeakGapValue']*2

        Out_target_pgv.loc[index, 'PGV_BreakLimit_target'] = round(
            threshold_level*3-recent2day_pgv, 3)
        Out_target_pgv.loc[index, 'BelowStop_PGV_target'] = result_store.loc[index,
                                                                             'Pre3Date_PeakGapValue']

        # Out_target_pgv.loc[index, 'Out_target_Two'] = round(second_level*3-recent2day_pgv, 2)

    return In_target_pgv.sort_values(by=['ts_code'], ascending=[True]), \
        Out_target_pgv.sort_values(
            by=['ts_code'], ascending=[True])


def double_check_pullstart_stocks(double_checkdate=None, start_date=None, end_date=None,
                                  industry_list=None, class_type='PullStart'):
    """检查stkpick_pullstart表中存储指定日期区间的股票品种，在检查日期的指标是否维持上行趋势"""
    import pandas as pd
    from sqlalchemy import create_engine
    import config.config_Ali as config
    from tqdm import tqdm

    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit',
        pool_size=10,
        max_overflow=20
    )

    if end_date is None:
        end_date = double_checkdate
    if start_date is None:
        start_date = end_date
    if class_type is None:
        class_type = ['PullStart']
    if isinstance(class_type, str):
        class_type = [class_type]

    # 初始化变量
    turnbreak_data = pd.DataFrame()
    pullstart_data = pd.DataFrame()

    if 'TurnBreak' in class_type:
        turnbreak_sql = f"""select * from stocksfit.stkpick_pullstart 
                        where Check_Date<='{end_date}' and Check_Date>='{start_date}' 
                        and Class_Type='TurnBreak' and turn_sort_position<=10
                    """
        turnbreak_data = pd.read_sql(turnbreak_sql, engine)
    if 'PullStart' in class_type:
        pullstart_sql = f"""select * from stocksfit.stkpick_pullstart 
                        where Check_Date<='{end_date}' and Check_Date>='{start_date}' 
                        and Class_Type='PullStart'
                    """
        pullstart_data = pd.read_sql(pullstart_sql, engine)
        pullstart_data = pullstart_data.query('recentbottom_downcoverdays>=10 & nowsec_recent_diff<=1')

    # 初始化一个空的DataFrame
    store_data = pd.DataFrame()

    # 将非空df拼接
    if len(turnbreak_data) > 0:
        store_data = turnbreak_data

    if len(pullstart_data) > 0:
        if len(store_data) > 0:
            store_data = pd.concat([store_data, pullstart_data], axis=0)
        else:
            store_data = pullstart_data

    if len(store_data) > 0:
        store_data = store_data.drop_duplicates(
            subset=['ts_code', 'Class_Type'], keep='first')
    else:
        print('stkpick_pullstart表中没有符合存储数据')
        return pd.DataFrame()

    if industry_list is not None:
        store_data = store_data.query('industry in @industry_list').copy()

    print('stkpick_pullstart表中获取的股票品种数量：', len(store_data))

    if len(store_data) > 0 and 'id' in store_data.columns:
        store_data = store_data.drop(columns=['id'])

    if double_checkdate is not None and len(store_data) > 0:
        store_list = list(set(store_data['ts_code'].values.tolist()))
        result_store = get_result_3(
            end_date=double_checkdate, stk_list=store_list)

        if 'MinCumRet_Date' not in store_data.columns:
            store_data['MinCumRet_Date'] = None

        if 'sort_position' not in store_data.columns:
            store_data['sort_position'] = 0

        store_data = store_data.rename(columns={'Now_SecDate':'PullStart_Now_SecDate'})
        store_data = pd.merge(store_data[['Class_Type', 'ts_code', 'Check_Date', 'sort_position',
                                          'MinCumRet_Date', 'PullStart_Now_SecDate', 'recent_bottom_date']],
                              result_store, on='ts_code', how='left')

        # 初始化新增字段
        store_data['PostRBottom_RiseRatio'] = 0
        store_data['PostRBottom_Over7Num'] = 0
        store_data['PostRBottom_WLS_Slope'] = 0
        store_data['PostRBottom_WLS_R2'] = 0
        store_data['PostRBottom_WLS_Deviation'] = 0
        store_data['PostRBottom_OverBottomCls'] = 0

        stk_startdate = None
        if 'Section_StartDate' in store_data.columns and not store_data['Section_StartDate'].isna().all():
            stk_startdate = store_data.loc[store_data['Section_StartDate'].notna(
            ), 'Section_StartDate'].min()

        if stk_startdate is None:
            stk_startdate = start_date

        # 一次性获取所有股票数据
        stock_data = get_stock_data(
            stk_code=store_list, start_date=stk_startdate, end_date=double_checkdate)
        trade_dates = get_trade_date()

        for index in tqdm(store_data.index):
            try:
                stk_code = store_data.loc[index, 'ts_code']
                stk_data = stock_data[stock_data['ts_code'] == stk_code].copy(
                ) if not stock_data.empty else pd.DataFrame()

                if stk_data.empty:
                    continue

                stk_data = stk_data.set_index('trade_date')
                stk_data['stk_ratio'] = stk_data['close'].pct_change()*100
                stk_data['mov3day_sumratio'] = stk_data['stk_ratio'].rolling(
                    3).sum()

                # min_cumret_date = store_data.loc[index, 'MinCumRet_Date']
                recent_bottom_date = store_data.loc[index, 'recent_bottom_date']

                postbottom_rise_ratio = 0
                if recent_bottom_date in stk_data.index and 'close' in stk_data.columns:
                    try:
                        max_close = stk_data.loc[recent_bottom_date:, 'close'].max(
                        )
                        first_close = stk_data.loc[recent_bottom_date:,
                                                   'close'].iloc[0]
                        if first_close > 0:
                            postbottom_rise_ratio = round(
                                (max_close/first_close - 1)*100, 2)
                    except Exception as e:
                        print(f"计算postbottom_rise_ratio时出错: {str(e)}")

                # 更新store_data中的字段
                store_data.loc[index,
                               'PostRBottom_RiseRatio'] = postbottom_rise_ratio
                store_data.loc[index, 'PostRBottom_Over7Num'] = len(
                    stk_data.loc[recent_bottom_date:].query('stk_ratio>7'))
                store_data['PostRBottom_WLS_Slope'], store_data['PostRBottom_WLS_R2'], \
                    store_data['PostRBottom_WLS_Deviation'] \
                    = cal_trend_wls(stk_data.loc[recent_bottom_date:])
                recent_bottom_cls = stk_data.loc[recent_bottom_date, 'close']
                if len(stk_data.loc[recent_bottom_date:].query('close<@recent_bottom_cls')) == 0:
                    store_data['PostRBottom_OverBottomCls'] = 1
                
            except Exception as e:
                print(f"处理股票 {stk_code} 数据时出错: {str(e)}")

        double_check_result = store_data.query(
            'Now_SecDate<=PullStart_Now_SecDate & PostRBottom_OverBottomCls==1'
        ).sort_values(by=['industry', 'break_prerecentpeak_ratio'], ascending=[True, False])
        
        required_columns = ['ts_code', 'name', 'industry', 'break_prerecentpeak_ratio'
                            'Now_PGV_RollAvg', 'NowSec_PGV_MaxRollAvg_UpCoverDays',
                            'PostRBottom_RiseRatio', 'PostRBottom_Over7Num',
                            'PostRBottom_WLS_Slope', 'PostRBottom_WLS_R2', 'PostRbottom_WLS_Deviation',
                            'SectionPeak_PRV_Top3Mean', 'PostSecPeak_PRV_Top3Mean', 'PreNowPeak_PRV_Top3Mean',
                            'PostSecStart_AvgTurnover', 'PostNowSec_AvgTurnover', 'PostCumRet_RiseRatio',
                            ]

        # 确保所有需要的列都存在
        for col in required_columns:
            if col not in double_check_result.columns:
                double_check_result[col] = None

        double_check_result = double_check_result[required_columns]
        return double_check_result
    else:
        return pd.DataFrame()


if __name__ == '__main__':
    # end_date = '2024-10-30'
    # threshold = -0.2
    # result = get_result_3(end_date=end_date)
    # result_pick = result.query('Now_PGVRollAvg_DownCount>=3 & '
    #                            '(PostSecMaxRollAvg_PGV_MinRollAvg_Date==Cal_Date | '
    #                            'PostSecPeak_PGV_MinRollAvg_Date==Cal_Date) & '
    #                            'MinRollAvg_Truncated_Diff!=0 & '
    #                            'MinRollAvg_Truncated_Diff>@threshold & '
    #                            '(PostSecPeak_DropTrend_Prop>=0.5 | '
    #                            'PostPreNowPeak_DropTrend_Prop>=0.5)'
    #                            ).sort_values(by='MinRollAvg_Truncated_Diff', ascending=False)
    #
    # turn_peak_industry = ['计算机', '传媒', '家用电器']
    # bottom_industry = ['食品饮料', '农林牧渔', '基础化工', '石油石化']
    # strong_quota, turn_quota, risesec_quota = get_turntrack_result(pick_date='2024-11-04',
    #                                                                sec_pickdate=['2024-10-17', '2024-11-04'],
    #                                                                pull_startdate='2024-08-05',
    #                                                                peak_industry=turn_peak_industry,
    #                                                                bottom_industry=bottom_industry, nowpick_style=True)
    #
    # # Rise阶段品种
    # result = get_result_3(end_date='2024-11-07')
    # result_pick = result.query(
    #     'Now_PGVRollAvg_DownCount==3 & Cal_Date==PostNowSec_PeakDate & PostNowSec_LastDays>=Now_PGVRollAvg_DownCount'
    #      ).sort_values(
    #     by='PostSecStart_PGV_MeanRollAvg', ascending=False)
    #
    # # Section_StartDate转折品种
    # result_pick = result.query(
    #     'Section_StartDate==Now_SecDate & PostSecStart_LastDays<=5 & '
    #     'Break_DownSecutiveStart_First2Now_LastDays<=5 & '
    #     'PostNowSec_PGV_MeanRollAvg>PreNowSec_PGV_MeanRollAvg')

    # result_output_pick, result_output = stkpick_dailytrack_step1(turntrack_startdate='2024-10-08',
    #                                                       recentdrop_startdate='2024-11-07', end_date='2024-11-18')

    # output_droprecov, output_droprecov_indus, result_totalmv, model_droprecov = get_turnbreak_stocks(turntrack_startdate='2024-10-17',
    #                                                                                           recentdrop_startdate='2024-12-30',
    #                                                                                           end_date='2025-01-14',
    #                                                                                           scope='all',
    #                                                                                           model_adj=True,
    #                                                                                           store_mode=True,
    #                                                                                           bottom_industry=None,
    #                                                                                           index_turndate=None)

    # output_recov, output_recov_indus, output_above =track_turnbreak_stocks(index_turndates=['2025-01-06'], lag_num=4, check_date='2025-01-10')

    # industry_stocks_strength, industry_stocks_strength_turn = cal_industry_stocks_strength(end_date='2024-01-14', industry_list=['电子','机械设备','计算机'],limit_num=50)

    # target_pgv = cal_target_pgv(stk_list=['300795.SZ', '000838.SZ', '002678.SZ'], check_date='2025-02-14')

    # double_check_result = double_check_pullstart_stocks(double_checkdate='2025-02-12', start_date='2025-02-01', end_date='2025-02-11', class_type='PullStart')

    from function_ai.Func_Base import get_trade_date
    trade_date = ['2025-08-14']
    for date in trade_date:
        print('测算日期：', date)
        trend_industry_list = None
        # recent_industry_list = ['通信', '环保', '基础化工']
        recent_industry_list = None
        # industry_list = None
        # '2025-03-24', '2025-03-10', '2025-02-28', '2025-02-18', 
        end_date, trend_startdate = date, '2025-04-07'
        recent_turndate = ['2025-05-23', '2025-06-10', '2025-06-19', '2025-06-27', '2025-07-07', '2025-07-15', '2025-07-23', '2025-07-31', '2025-08-07']
        # recent_turndate = ['2025-07-15', '2025-08-01']
        result_df_head, result_df_turn, pull_start_list, pull_start_list_core, result_df_recentindus = track_pullstart_stocks(
            end_date=end_date, trend_startdate=trend_startdate,
            recent_turndate=recent_turndate,
            rise_stop_signal=True,
            industry_list=trend_industry_list,
            limit_num=80, store_mode=True,
            recent_indus=recent_industry_list,
            recentindus_calstartdate='2025-03-18')
        
    # industry_stocks_strength, industry_stocks_strength_turn = cal_industry_stocks_strength(end_date='2023-07-20', limit_num=20)

    # relative_return = cal_stock_industry_strength(stk_code='002917.SZ', start_date='2024-12-09', end_date='2025-01-17')
    # bottom_industry = ['公用事业','银行', '石油石化']
    # result_sec_pick, result_bottom_pull, result_total = get_turntrack_result(pickdate='2024-11-13',
    #                                                                          pull_startdate='2024-09-18',
    #                                                                          bottom_industry=bottom_industry)
    # result_cal = get_result_3(end_date='2024-11-07')
    # result_cal = result_cal.rename(columns={'Now_SecDate':'Now_SecDate_x'})
    # stk_data = get_stock_data(stk_code='301066.SZ', start_date='2024-10-08', end_date='2024-11-07')
    # stk_data = stk_data.set_index('trade_date')
    # start_date, end_date = '2024-10-08', '2024-11-07'
    # period_deviation = cal_period_deviation(result_cal.query('ts_code=="301066.SZ"').iloc[-1],
    #                                         stk_data, start_date, end_date)
