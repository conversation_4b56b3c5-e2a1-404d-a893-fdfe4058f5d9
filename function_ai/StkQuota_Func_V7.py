import pdb
import time
from datetime import datetime
import numpy as np
import pandas as pd
from regex import D
import sqlalchemy.exc
from dateutil.relativedelta import relativedelta

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sympy import sec
from tqdm import tqdm
import statsmodels.api as sm
import matplotlib.pyplot as plt
import math

import os
import sys

# 获取项目根目录路径
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from function_ai.Func_Base import (cal_movingavg_stat, cal_maxcontisum,
                                   cal_recent_stat, cal_target_price,
                                   coef_regress, get_index_data,
                                   get_stock_data, get_stock_info, period_stat,
                                   retracement_from_high, section_stat, count_und_num, cal_movavg_bias)
from function_ai.swindex_funcs import get_swindex_data
from function_ai.wkoff_funcs import (identify_SpringDate, identify_ShockDate,
                                     identify_SOSDate, identify_StepBackDate)
from function_ai.DailyGap_Func import gap_multiprocess, cal_turnpoint, calculate_extreme_percentile_band
from function_ai.StkPick_Func_V7 import get_result_3

"""重构的二级筛选程序,将get_Result_Break,get_breakpick和cal_period_ratio的筛选条件合并"""


def stksfit_result_3(end_date=None, stock_data=None, cal_mode='All', 
                     stk_code=None, storemode=True, freqdata_source='api'):
    """重构二级筛选程序，用于筛选股票和计算技术指标。
    
    主要功能:
    - 根据mode参数执行不同的筛选逻辑:
        1. pick模式: 对所有股票进行条件筛选
        2. ADJ模式: 对指定股票进行指标验证
    - 计算各项技术指标和统计数据
    - 支持多进程并行计算提高效率
    
    参数:
    end_date: 计算截止日期
    stock_data: 股票历史数据,默认为None
    mode: 运行模式,可选'pick'或'ADJ'
    stk_code: 股票代码,仅ADJ模式需要
    storemode: 是否存储结果,默认True
    
    返回:
    ResultBreak: 计算结果DataFrame"""
    start_time = datetime.now()
    print('starttime: ', start_time)
    import config.config_Ali as config
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')

    Result_Common = get_stock_info()
    Result_Common = Result_Common[['ts_code', 'name', 'industry', 'area']]
    common_column_names, gapvalue_column_names = set_resultindexs()
    # column_names = common_column_names + gapvalue_column_names
    Result_Common = pd.concat([Result_Common, pd.DataFrame(columns=common_column_names)], sort=False, axis=1)
    Result_GapValue = pd.concat([Result_Common[['ts_code']], pd.DataFrame(columns=gapvalue_column_names)], sort=False, axis=1)

    sql = """select trade_date from stocksfit.stock_data"""
    Trade_Date = pd.read_sql_query(sql, con=engine)
    if end_date is None:
        end_date = Trade_Date['trade_date'].iloc[-1]
    elif end_date not in Trade_Date['trade_date'].values:
        print('股票数据尚未更新至指定日期!')
        return
    engine.dispose()

    start_date = (pd.to_datetime(end_date, format='%Y-%m-%d') - relativedelta(years=5)).strftime('%Y-%m-%d')
    
    signal = False
    
    if cal_mode == 'ADJ' and stk_code is not None:
        Result_Common = Result_Common.query('ts_code==@stk_code').copy()
        Result_GapValue = Result_GapValue.query('ts_code==@stk_code').copy()
        if stock_data is None:
            Stock_Data = get_stock_data(stk_code=stk_code, start_date=start_date,
                                        end_date=end_date, enginesignal=engine)
            Stock_Data = Stock_Data.query('ts_code==@stk_code'
                                          ).sort_values('trade_date', ascending=True).set_index('trade_date',
                                                                                                drop=False)
        else:
            Stock_Data = stock_data
        if len(Stock_Data) == 0:
            print('所选择时间区间' + stk_code + '股票数据!')
            return
    else:
        if stock_data is None:
            Stock_Data = get_stock_data(start_date=start_date, end_date=end_date, enginesignal=engine)
        else:
            Stock_Data = stock_data
        if len(Stock_Data) == 0:
            print('所选择时间区间无股票数据!')
            return

    engine.dispose()
    index_data = get_index_data(stk_code='000906.SH', end_date=end_date).set_index('trade_date')

    # 调用函数计算预设指标
    if cal_mode.lower() in ['adj', 'first_half', 'all']:
        ResultCommon = multiprocess_3(Result_Common, Stock_Data, index_data, mode=cal_mode)
        ResultCommon['Cal_Date'] = end_date
        if storemode:
            signal = restore_resultbreak(ResultCommon, conf, end_date, mode='first_half')
            if signal:
                print('FirstHalf ResultCommon has been stored!')
            else:
                print('FirstHalf ResultCommon has not been stored!')
    else:
        ResultCommon = get_result_3(end_date=end_date, mode='First_Half')
        print('已读取存储的First_Half数据!')
    # if mode.lower() == 'pick' and storemode:
    #     signal = restore_resultbreak(ResultCommon, conf, end_date, signal, mode='first_half')
    #     if signal:
    #         print('FirstHalf ResultCommon has been stored!')
    #     else:
    #         print('FirstHalf ResultCommon has not been stored!')
    time.sleep(30)
    if cal_mode.lower() in ['adj', 'second_half', 'all']:
        if len(ResultCommon) == 0:
            print('First_Half数据未更新！无法用于Second_Half指标计算')
            return
        ResultGapValue = gap_multiprocess(Result_GapValue, ResultCommon, end_date, data_source=freqdata_source)
        ResultGapValue['Cal_Date'] = end_date
        if storemode:
            signal = restore_resultbreak(ResultGapValue, conf, end_date, mode='second_half')
            if signal:
                print('SecondHalf ResultGapValue has been stored!')
            else:
                print('SecondHalf ResultGapValue has not been stored!')
    else:
        ResultGapValue = Result_GapValue
    end_time = datetime.now()
    print('endtime: ', end_time)
    print('耗时： ', end_time - start_time)
    print('finish date: ', end_date)
    ResultBreak = pd.merge(ResultCommon, ResultGapValue, on=['ts_code', 'Cal_Date'], how='left')
    return ResultBreak, signal


def restore_resultbreak(ResultBreak, conf, end_date, mode=None):
    if mode.lower() not in ['first_half', 'second_half']:
        print('请输入正确的mode参数(first_half 或 second_half)！')
        return
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')
    Sesstion = sessionmaker(bind=engine)
    session = Sesstion()
    if mode.lower() == 'first_half':
        check_sql = f"""select * from stocksfit.stk_results_common
                        where Cal_Date='{end_date}'"""
    elif mode.lower() == 'second_half':
        check_sql = f"""select * from stocksfit.stk_results_gapvalue
                        where Cal_Date='{end_date}'"""
    else:
        print('请输入正确的mode参数以提供给check_sql(first_half 或 second_half)！')
        return
    check_result = pd.read_sql(check_sql, engine)
    if len(check_result) > 0:
        try:
            if mode.lower() == 'first_half':
                delete_sql = text("delete from stocksfit.stk_results_common where Cal_Date= :end_date")
            elif mode.lower() == 'second_half':
                delete_sql = text("delete from stocksfit.stk_results_gapvalue where Cal_Date= :end_date")
            else:
                print('请输入正确的mode参数以提供给delete_sql(first_half 或 second_half)！')
                return
            with session.begin():
                session.execute(delete_sql, {"end_date": end_date})
            session.commit()
        except Exception as e:
            session.rollback()
            print('An error occured:', e)
    if mode.lower() == 'first_half':
        table_name = 'stk_results_common'
    elif mode.lower() == 'second_half':
        table_name = 'stk_results_gapvalue'
    else:
        print('请输入正确的mode参数(first_half 或 second_half)！')
        return
    for _ in range(3):
        try:
            pd.io.sql.to_sql(ResultBreak, table_name, engine, index=False, schema='stocksfit',
                             if_exists='append')
        except Exception as e:
            print('An error occured:', e)
            time.sleep(3)
            signal = False
        else:
            signal = True
            break
    session.close()
    engine.dispose()
    return signal


def set_resultindexs():
    """设定待计算指标序列
    
    主要功能:
    - 初始化Result_Break的指标字段列表
    - 包含技术指标、统计指标、趋势指标等多个维度
    - 确保所有指标字段的完整性和一致性
    
    参数:
    Result_Break: 待初始化的DataFrame
    
    返回:
    Result_Break: 添加指标字段后的DataFrame"""
    common_column_names = ['Total_MV', 'Total_Share', 'Target_Price',
                           'Target_Ratio', 'Stop_Lose_Price',
                           'Turn_Target_Price', 'Turn_Target_Ratio',
                           'Section_Target_Price', 'Section_Target_Ratio',
                           'Return_Risk_Ratio', 'TargetRatio_High', 'Period_TurnDate',
                           'PostTurn_RiseRatio', 'PostTurn_RiseAvg', 'PostTurn_MaxDrop', 'PostTurn_LastDays',
                           'PostTurn2Peak_LastDays',
                           'PostTurn_MaxContiSum', 'PostTurn_MaxContiAvg', 'PostTurn_MaxContiDays',
                           'Turn2Peak_MaxContiDrop',
                           'Turn2Peak_MaxContiDropDays',
                           'PostTurn_MaxTurnover', 'PostTurn_AvgTurnover', 'PostTurn_Period_RiseNum',
                           'PostTurn_TO_Over10Num', 'TurnConcave_LastDays', 'PostTurn_BfPeak_MaxDrop',
                           'PostTurn_Sec_TrendRatio_Diff', 'PostTurn_COR_Mean', 'PostTurn_COR_Std',
                           'PostTurn_COR_Und2Poxn',
                           'Turn_1stTODate_Ratio', 'Turn_1stTODate_LastDays', 'PostTurn_1stTODate2Top_Ratio',
                           'Long_Trend', 'Period_Trend',
                           'PreTurn_Period_Lastdays', 'PreTurn_Period_SumRatio',
                           'PreTurn_Period_AvgRatio', 'PreTurn_Period_R2', 'PreTurn_Period_Sum2Std',
                           'PreTurn_MaxTurnover',
                           'PreTurn_Turnover2Rise', 'PreTurn_Turnover2Drop', 'PreTurn_Turnover2Drop_Last4Rank',
                           'PreTurn_PeakDate', 'PostTurn_PeakDate', 'PreTurnPeak_Sec_StartDate',
                           'Peak2Turn_LastDays', 'Peak2Turn_SumRatio', 'Peak2Turn_AvgRatio',
                           'Peak2Turn_MaxContiRise', 'Peak2Turn_MaxContiRiseDays',
                           'Peak2Turn_COR_Mean', 'Peak2Turn_COR_Std',
                           'Peak2Turn_COR_Und2Poxn', 'Peak2Turn_Reg_R2', 'Peak2Turn_Reg_Sum2Std',
                           'PeakN2Y_2Turn_LastDays',
                           'Now_Period_StartDate', 'Now_Period_Lastdays', 'Now_Period_SumRatio',
                           'Now_Period_AvgRatio', 'Now_Period_R2',
                           'LongTrend_PeriodNum_Rise', 'LongTrend_PeriodNum_Drop',
                           'Bottom_Date', 'PostBottom_RiseRatio', 'Bottom2Now_Ratio',
                           'PostBottom_Lastdays', 'PostBottom_PeriodNum',
                           'PostBottom_MaxDrop', 'PostBottom_MaxTurnover',
                           'PostBottom_TO_Over10Num', 'PostBottom_MaxContiSum',
                           'Long_PeakDate',
                           'Concave_Break_Date', 'Concave_Break_Days2Now',
                           'CoverDaysDiff_10', 'CoverRatioBand_10',
                           'CoverDaysDiff_20', 'CoverRatioBand_20', 'SecConcave_StartDate',
                           'SecConcave_LastDays', 'NowSec_SecConcave_LastDays', 'SecConcave_BfBottom_LastDays',
                           'SecConcave_AftBottom_LastDays', 'SecConcave_BfBottom_TO_Sum',
                           'SecConcave_AftBottom_TO_Sum', 'SecConcave_TO_Sum', 'NowSec_SecConcave_TO_Sum',
                           'SecConcave_RatioBand', 'SecPeakConcave_CoverDays',
                           'Period_Break_Ratio', 'Period_Break_Date', 'PostTurn_BreakRatio',
                           'PostSec_1stSec_BreakRatio',
                           'CoverDays_Bf_PostturnMaxDate', 'CoverDays_Aft_PostturnMaxDate',
                           'Peak_Pres_Num', 'Peak_Pres_Lastdays', 'Valley_Pres_Num', 'Valley_Pres_Lastdays',
                           'Peak_Pres_MaxPrice', 'Peak_Pres_MaxPrice_Ratio', 'Valley_Pres_MaxPrice',
                           'Valley_Pres_MaxPrice_Ratio', 'Peak_Pres_RatioBand',
                           'Peak_Pres_AftBottom_Lastdays', 'Peak_Pres_AftBottom_DaysProp',
                           'Turn_Period_GapRatio', 'PreTurn_Period_GapRatio',
                           'PostTurn_Sec_DropNum', 'PostTurn_Sec_Min_SumRatio', 'PostTurn_Sec_Min_ExtreRatio',
                           'PostTurn_Sec_RiseNum', 'PostTurn_Sec_Max_SumRatio', 'PostTurn_Sec_Avg_LastDays',
                           'PostTurn_Sec_Max_ExtreRatio',
                           'PostTurn_Peak2Now_Lastdays', 'PostTurn_Peak2Now_SumRatio', 'PostTurn_Peak2Now_AvgRatio',
                           'Period_Valley_GapRatio',
                           'PreTurn_LastSec_Lastdays', 'PreTurn_LastSec_AvgRatio',
                           'PreTurn_LastSec_SumRatio', 'PreTurn_LastSec_ExtreRatio',
                           'PreTurn_LastSec_AvgTurnover', 'PreTurn_LastPeriod_SecNum',
                           'PostTurn_1stSec_Lastdays', 'PostTurn_1stSec_AvgRatio',
                           'PostTurn_1stSec_SumRatio', 'PostTurn_1stSec_ExtreRatio',
                           'PostTurn_1stSec_AvgTurnover',
                           'Now_DayRatio', 'Now_Over5Min', 'Now_Over3Max', 'Now_Over3Mean', 'Now_Und3Min',
                           'Now_Und3Mean',
                           'Now_Recent3Mean', 'Now_Recent3Min', 'Now_MaxSum',
                           'Now_State', 'Now_Vol_Trend', 'NowSec_AvgRatio', 'NowSec_LastDays', 'NowSec_Und2ContiDays',
                           'Now_HighLow_Ratio', 'Now_Turnover', 'Now_Turnover_Signal',
                           'Now_Turnover_Quntl', 'Now_Turnover_Rank', 'Now_Turnover_MinGap',
                           'BfRise_Turnover_Threshold',
                           'Pre_SecStart_MaxHLRatio', 'Post_SecStart_MaxHLRatio',
                           'Section_PeakDate', 'PreSec_StartDate', 'Section_StartDate',
                           'Now_SecDate', 'PreNow_SecDate', 'PostSecStart_PeakDate',
                           'PostNowSec_PeakDate', 'PostNowSec_Peak2Now_LastDays',
                           'PreNow_BottomDate', 'PreNowPeak_BottomDate',
                           'Sec2Now_PeakDate', 'SectionStart_Position',
                           'PostSecStart_LastDays', 'PostSecStart_SumRatio', 'PostSecStart_MeanClose',
                           'PostSecStart_AvgRatio', 'PostSecStart_MaxDailyRatio', 'PostSecStart2PreNowSec_Beta',
                           'PostSecStart_MinDailyRatio', 'PostSecPeak_MinDaily2Now_LastDays',
                           'PostSecStart_MinQuntlRatio', 'PostSecPeak2Now_LastDays',
                           'PostSecPeak2Now_AvgTurnover', 'PostSecPeak2Now_SumRatio', 'PostSecPeak2Now_AvgRatio',
                           'PreSecPeak_Sec_LastDays', 'PreSecPeak_Sec_SumRatio', 'PreSecPeak_Sec_AvgRatio',
                           'PreSecPeak_Sec_StartDate', 'PostSecPeak_Over_StartCls_State',
                           'PostSecStart_AvgTurnover', 'PostSecStart_Adverse2Trend',
                           'PostSecStart_Sec_Max_SumRatio', 'PostSecStart_Sec_Max_AvgRatio',
                           'PostSecStart_Sec_Max_LastDays', 
                           'PostSecStart_Sec_MaxDrop_SumRatio',
                           'PostSecStart_Sec_MaxDrop_LastDays',
                           'PostSecStart_Sec_MaxDrop_AvgRatio',
                           'PostSecStart_TO_Over10Num', 'PostSecStart_Avg_LastDays', 'PostSecStart_DropDayRatio_Quntl',
                           'PostSecStart_DropDayRatio_Min', 'PostSecStart_MaxContiDrop',
                           'PostSecStart_MaxContiDropDays',
                           'PostSecStart_MaxContiSum', 'PostSecStart_MaxContiAvg', 'PostSecStart_MaxContiDays',
                           'PostSecStart_COR_Mean', 'PostSecStart_COR_Std', 'PostSecStart_COR_Und2Poxn',
                           'PostSecStart_Und2ContiDays', 'PostSecStart_Und2SumDays',
                           'PostSecStart_Drop2Rise_SecMaxDaysDiv', 'PostSecStart_Drop2Rise_SecSumDaysDiv',
                           'PostSecStart_Drop2Rise_SecMaxSumDiv',
                           'PostSecStart_MovAvg_BiasMean', 'PostSecStart_MovAvg_BiasStd',
                           'PostSecMax2Peak_Ratio',
                           'PostNowSec_SumRatio', 'PostNowSec_LastDays', 'PostNowSec_AvgRatio',
                           'PostNowSec_ExtreRatio', 'PostNowSec_Adverse2Trend',
                           'PostNowSec_COR_Mean', 'PostNowSec_COR_Std',
                           'PostNowSec_COR_Und2Poxn', 'PostNowSec_AvgTurnover', 'PostNowSec_MaxTurnover',
                           'PreNowSec_Sec_AvgTurnover', 'PreNowSec_Sec_MaxTurnover', 'BreakPreNowSec_Ratio',
                           'BreakPreNowPeak_Ratio',
                           'PreNowSec_SumRatio', 'PreNowSec_AvgRatio', 'PreNowSec_LastDays', 'PreNowSec_Und2ContiDays',
                           'PreNowSec_MaxClsOpenRatio', 'PreNowSec_ExtreRatio', 'PreNowSec_BelowMinDaily',
                           'PreNowSec_Adverse2Trend', 'PreNowSec_COR_Mean', 'PreNowSec_COR_Std',
                           'PreNowSec_COR_Und2Poxn',
                           'PreNowPeak2Now_LastDays', 'PreNowPeak2Now_SumRatio', 'PreNowPeak2Now_AvgRatio',
                           'PreNowPeak2Now_AvgTurnover', 'PreNowPeak2Now_MaxTurnover', 'PreNow_PeakDate',
                           'PreNowSec_SecDropDays_QuntlRatio', 'PreNowSec_SecDropAvg_QuntlRatio',
                           'Pre_PreNowSec_Und2ContiDays', 'Pre_PreNowSec_Adverse2Trend',
                           'Pre_PreNow_Sec_LastDays', 'Pre_PreNow_Sec_SumRatio',
                           'Pre_PreNow_Sec_AvgRatio', 'Pre_PreNow_Sec_ExtreRatio',
                           'Pre_PreNow_Sec_Over7Num',
                           'PreNow2Now_Volab_ProP', 'Sec2PreNow_Volab_ProP', 'UndVolab_LastDays',
                           'Recent5D_BfPreNow_Volab_Num', 'Recent5D_AftPreNow_Volab_Num',
                           'PreSec_LastDays', 'PreSec_Und2ContiDays',
                           'PreSec_SumRatio', 'PreSec_AvgRatio', 'PreSec_ExtreRatio',
                           'PreSec_AvgTurnover', 'PreSec_MaxTurnover', 'PreSec_Adverse2Trend',
                           'PreSec_DDR_Quntl', 'PreSec_DDR_Min', 'Latst2Now_DDRQ_Days',
                           'PreSec_Sec_OMA_Prop', 'PreSec_Sec_OMA_Days',
                           'PreSec_Turnover2Rise', 'PreSec_Turnover2Drop', 'PreSec_Turnover2Drop_Last4Rank',
                           'PreSec_Turnover2Drop_State', 'PreSec_SecDropDays_QuntlRatio',
                           'PreSec_SecDropAvg_QuntlRatio',
                           'Post_PreSecPeak_Und4Count', 'Post_PreSecPeak_RecovCount',
                           'Post_PreSecPeak_RecovRatio', 'Post_PreSecPeak_MeanRecovDays',
                           'Post_PreSecPeak_Over35Count', 'Post_PreSecPeak_DepresCount',
                           'Post_PreSecPeak_DepresRatio', 'Post_PreSecPeak_MeanDepresDays',
                           'Post_SecPeak_Und4Count', 'Post_SecPeak_RecovCount',
                           'Post_SecPeak_RecovRatio', 'Post_SecPeak_MeanRecovDays',
                           'Now_RiseSec_Turnover2Rise', 'Now_RiseSec_Turnover2Drop',
                           'Now_FallSec_Turnover2Drop', 'Now_RiseSec_LastDays',
                           'Now_Turnover2Rise_State', 'Now_Turnover2Drop_State', 'Bf_SecStart_5D_MinRatio',
                           'Recent_MeanRatio', 'Recent_MaxRatio', 'Recent_Mean_ClsOpenRatio',
                           'PreNow_MeanHLRatio', 'PreNow_MHLR_Over4Num', 'Recent_HLR_Date', 'Recent_HLRatio_Chg',
                           'Sec_IndexDiff_Signal', 'Sec_IndexDiff_Ratio', 'Sec_IndexDiff_StartDate',
                           'NowSec_BreakSecPeak_Ratio', 'Break_D100_Ratio',
                           'PostSecStart_RiseRatio', 'PostSecStart_MaxDrop', 'PostSecStart_MaxDrop_LastDays',
                           'PostSecStart_MaxTurnover', 'PostSecStart_MaxTurnover_Date',
                           'PostSecStart_RiseSecNum', 'PostSecStart_SecRiseState',
                           'SecStart_1stTODate_Ratio', 'SecStart_1stTODate2Now_LastDays',
                           'Section_PostPeak_MaxDrop',
                           'Recent4P_MaxLastDays', 'PostTurn_Over7Num',
                           'PostNowSec_Over7Num', 'PostNowSec_HighOver7Num',
                           'PostNowSec_ContiOver7Days',
                           'PostTurn_Recent_Neg4_DropDate', 'PostTurn_Recent_Neg4_RecovDays',
                           'PostTurn_Recent_Neg4_DropRatio', 'PostTurn_Recent_Neg4_RecovDate',
                           'PostTurn_Recent_Neg4_Recov2Now_LastDays', 'PostTurn_Recent_Neg4_Threshold',
                           'PostPeak_Recent_Neg4_DropDate', 'PostPeak_Recent_Neg4_RecovDays',
                           'PostPeak_Recent_Neg4_DropRatio', 'PostPeak_Recent_Neg4_RecovDate',
                           'PostPeak_Recent_Neg4_Recov2Now_LastDays', 'PostPeak_Recent_Neg4_Threshold',
                           'PostPeak_Recent_Neg4_DropDate_Close',
                           'PostSecStart_Over7Num', 'PostSecStart_HighOver7Num',
                           'Peak2Sec_SumRatio', 'Peak2Sec_LastDays', 'Peak2Sec_Sec_Max_LastDays',
                           'Peak2Sec_AvgRatio', 'Peak2Sec_MaxContiRise', 'Peak2Sec_MaxContiRiseDays',
                           'Peak2Sec_COR_Mean', 'Peak2Sec_COR_Std', 'Peak2Sec_COR_Und2Poxn',
                           'Peak2Sec_Reg_R2', 'Peak2Sec_Reg_Sum2Std', 'Peak2Sec_Und2ContiDays', 'Peak2Sec_Und2SumDays',
                           'Peak2Sec_Und3MovAvg_Prop', 'PostSecStart_Over3MovAvg_Prop',
                           'PostTurn_Reg_R2', 'PostTurn_Reg_Sum2Std',
                           'PostTurn_Reg_Lastdays', 'PostTurn_Reg_EndDate',
                           'PostSecStart_Reg_R2', 'PostSecStart_Reg_Sum2Std',
                           'PreSec_Reg_R2', 'PreSec_Reg_Sum2Std',
                           'PrePeak_Over4_Date', 'Convex_Break_DropDate',
                           'GapRatio2Sec',
                           'PostPeak_Gap2Peak_Ratio',
                           'PostPeak_GapLastDays', 'PostPeak_GapCovDays', 'PostPeak_Gap2NowDays',
                           'TurnDiff_Period_AvgRatio_Div', 'TurnDiff_Period_LastDays_Div',
                           'TurnDiff_Period_AvgTurnover_Div', 'TurnDiff_Sec_Recov_Position',
                           'TurnDiff_Sec_LastDays_Div', 'TurnDiff_Recoved_Sec_Num',
                           'TurnDiff_Recoved_Sec_LastDays_Div', 'PostTurn_BMA_Proportion',
                           'SecValley_GapRatio', 'SecDiff_Sec_Recov_Position',
                           'SecDiff_Sec_LastDays_Div', 'SecDiff_Sec_AvgRatio_Div',
                           'SecDiff_Sec_AvgTurnover_Div', 'CoverDays_BreakDate_RatioBand',
                           'Narrow_LastDays', 'Narrow_E2N_Days',
                           'Narrow_E2N_Return', 'Narrow_E2N_MaxReturn',
                           'Narrow_E2N_MinReturn',
                           'PreNow_Rise_BMA_Prop', 'PreNow_Rise_BMA_Days',
                           'PostNowSec_Rise_BMA_Prop', 'PostNowSec_Rise_BMA_Days',
                           'PreNow_Rise_Recent_MeanRatio', 'PreNow_Rise_Recent_MaxRatio',
                           'PreNow_Rise_Recent_ClsOpenRatio',
                           'PreNow_Rise_MaxContiSum', 'PreNow_Rise_MaxContiAvg', 'PreNow_Rise_MaxContiDays',
                           'LastDrop_ClsOpenRatio_Avg', 'LastDrop_COR_Ratio',
                           'Peak2SecWLS_Slope', 'Peak2SecWLS_R2', 'Peak2SecWLS_Deviation',
                           # 'Peak2SecWLS_OverMid_Ratio', 'Peak2SecWLS_OverUp_Ratio', 'Peak2SecWLS_UndLow_Ratio',
                           'PreSecWLS_Slope', 'PreSecWLS_R2', 'PreSecWLS_Deviation',
                           # 'PreSecWLS_OverMid_Ratio', 'PreSecWLS_OverUp_Ratio', 'PreSecWLS_UndLow_Ratio',
                           'PostSecWLS_Slope', 'PostSecWLS_R2', 'PostSecWLS_Deviation',
                           'PostNowSecWLS_Slope', 'PostNowSecWLS_R2', 'PostNowSecWLS_Deviation',
                           'PostPreNowBottomWLS_Slope', 'PostPreNowBottomWLS_R2', 'PostPreNowBottomWLS_Deviation',
                           # 'PostSecWLS_OverMid_Ratio', 'PostSecWLS_OverUp_Ratio', 'PostSecWLS_UndLow_Ratio',
                           'PreNowPeak2NowSec_LastDays', 'PostPreNowBottom_LastDays',
                           'PreNowPeak2NowSec_SlowTrend_Deviation', 'PostPreNowBottom_SlowTrend_Deviation',
                           'PostNowSec_SlowTrend_Deviation',
                           'PreNowPeak2PreNowSec_SlowTrend_Deviation', 'PostPreNowBottom2PreNowSec_SlowTrend_Deviation',
                           'PreTurn_ShockDate', 'PostTurn_SOSDate',
                           'PostTurn_StepBackDate', 'PostTurn_StepBack_GapRatio', 'PostTurn_StepBack_LastDays',
                           'PostSecPeak_ShockDate', 'PostSecPeak_SOSDate',
                           'PostSecPeak_StepBackDate', 'Now_StepBack_State',
                           'PostSecPeak_StepBack_GapRatio', 'PostSecPeak_StepBack_LastDays',
                           'PreSec_ShockDate', 'PostSec_SOSDate', 'PostSec_StepBackDate',
                           'PostSec_StepBack_GapRatio', 'PostSec_StepBack_LastDays',
                           'Shock_Date', 'Shock_Date_Last', 'Spring_Date', 'SOS_Date', 'Shrink_Date',
                           'Now_Shock_Date', 'Now_Spring_Date',
                           'Break_PreSec', 'Break_TurnShock', 'BreakShock_Date', 'BreakShockLast_Date',
                           'PostSec_Max2PreShock_Ratio', 'Now_PostTurn_SecDropState',
                           'Now_PostSec_SecDropState', 'PostSec_SecDrop_MaxClsDiff',
                           'Now_PostSec_SecRise_ClsDiff', 'Now_PostSec_SecRise_Portion',
                           'Now_PostSec_SecRiseState', 'Now_PostTurn_SecRiseState',
                           'PostPreNowPeak_CumSum2IdxRatio_MinDate', 'PostNowSec_CumSum2IdxRatio_MaxDate',
                           'PreNowSec_AvgTurnover2Drop', 'PostNowSec_AvgTurnover2Rise', 'Now_Turnover2Ratio',
                           'PreNowPeak2NowSec_MinRatioMovAvg', 'PreNowPeak2NowSec_MinRatioMovAvg_Date',
                           'PreNowPeak2NowSec_RatioMovAvg_NowSecRank',
                           'PostNowSec_Recover_TopOpen_Days', 'PostNowSec_Recover_TopOpen_Date',
                           'PostNowSec_Recover_TopOpen_Close', 'PostNowSec_Recover_TopOpen_Open',
                           'PostSecStart_RiseSecOver10Days_StartDate',
                           'PostSecStart_RiseSecOver10Days_EndDate',
                           'PreNowSec_DropSecOver10Days_StartDate',
                           'PreNowSec_DropSecOver10Days_EndDate',
                           'Pre_PostSecPeak_Sec_SumTO', 'PostSecPeak2Now_SumTO',
                           'PostSecPeak_Support_Breach_CostDays',
                           'Peak2Turn_AvgSecDays',
                           'Turn2Now_AvgSecDays',
                           'Peak2Sec_STrend', 'SecStart2Now_STrend',
                           'PreNowBottom2PostNowPeak_STrend',
                           'PreNowBottom2PostNowPeak_STrend_UpDev',
                           'PreNowBottom2PostNowPeak_STrend_LowDev',
                           'PostSecStart_SectionStable_Num',
                           'RiseSec_SectionStable2NowSec_Days',
                           'DropSec_SectionStable2NowSec_Days',
                           'PreNowSec_Trend_Consistency',
                           'PreNowSec_Channel_Stability',
                           'PreNowSec_Turnover2Change_Rank',
                           'PreNowSec_Turnover2Change',
                           'PreNowSec2NowSec_MeanStdSum',
                           'PreNowBottom2Now_MeanStdDiff',
                           'NowSec_SecConcave_StartDate',
                           'PostPreNow_MinDailyRatio_Date', 'PostPreNow_MinDailyRatio',
                           'PostPreNow_MinDailyRatio_Recover_Date', 'PostPreNow_MinDailyRatio_Recover_Days',
                           'PostPreNow_MinDailyRatio_Recover_State',
                           'PostSecPeak_MinDailyRatio_Date', 'PostSecPeak_MinDailyRatio',
                           'PostSecPeak_MinDailyRatio_Recover_Date', 'PostSecPeak_MinDailyRatio_Recover_Days',
                           'PostSecPeak_MinDailyRatio_Recover_State',
                           'PostSecStart_DailyDropRecov_Count', 'PostSecStart_DailyDropRecov_RecovRatio',
                           'PostSecStart_DailyDropRecov_MeanRecovDays',
                           'NowSec_LowBand_CoverDays', 'NowSec2SecStart_Ratio',
                           'NowSec_MaxClose_UpCoverDays', 'NowSec_MinClose_DownCoverDays',
                           'SecStart_MaxClose_UpCoverDays', 'SecStart_MinClose_DownCoverDays',
                           'PostSecPeak_DropSec_Num',
                           'Peak2Sec_OverPre1Day_Days',
                           'Peak2Sec_OverPre1Day_Prop',
                           'Peak2Sec_OverPre1Day_MeanRatio',
                           'PreNowPeak2Now_OverPre1Day_Days',
                           'PreNowPeak2Now_OverPre1Day_Prop',
                           'PreNowPeak2Now_OverPre1Day_MeanRatio',
                           'PostSecStart_BelowPre1Day_Days',
                           'PostSecStart_BelowPre1Day_Prop',
                           'PostSecStart_BelowPre1Day_MeanRatio',
                           'PostNowSec_BelowPre1Day_Days',
                           'PostNowSec_BelowPre1Day_Prop',
                           'PostNowSec_BelowPre1Day_MeanRatio',
                           'SecAvgRatio_PreNowSec2PostSecDrop_Ratio',
                           'SecAvgRatio_PreSec2PostSecPeakDrop_Ratio',
                           'PostSec_TO_TurnP_Date',
                           'PostSec_TO_TurnP_AbsChange',
                           'PostSec_TO_TurnP_RelaChange',
                           'PostSec_TO_PostTurnP_LastDays',
                           'Peak2Sec_TO_TurnP_Date',
                           'Peak2Sec_TO_TurnP_AbsChange',
                           'Peak2Sec_TO_TurnP_RelaChange',
                           'Peak2Sec_TO_PostTurnP_LastDays',
                           'Section_BottomDate',
                           'SectionPeak_PreSecRise_LastDays',
                           'SectionPeak_PreSecRise_SumRatio',
                           'SectionPeak_PreSecRise_AvgRatio',
                           'SectionPeak_PrePostDays_Ratio',
                           'SectionPeak_PrePostSum_Ratio',
                           'SectionPeak_PrePostAvg_Ratio',
                           'PreNowPeak_PrePostDays_Ratio',
                           'PreNowPeak_PrePostSum_Ratio',
                           'PreNowPeak_PrePostAvg_Ratio',
                           'PostSecStart_Over3MovAvg_ContiDays',
                           'Peak2Sec_Und3MovAvg_ContiDays',
                           'PreNowPeak_PreSec_Over3MovAvg_ContiDays',
                           'PreNowPeak_PreSec_Over3MovAvg_Prop',
                           'PostNowSec_Over3MovAvg_ContiDays',
                           'PostNowSec_Over3MovAvg_Prop',
                           'PostSecStart_Over3MovAvg_MaxRate',
                           'Peak2Sec_Und3MovAvg_MaxRate',
                           'PreNowPeak_PreSec_Over3MovAvg_MaxRate',
                           'PreNowPeak2NowSec_Und3MovAvg_MaxRate',
                           'PostNowSec_Over3MovAvg_MaxRate',
                           'PreNowSec_PreSec_Over3MovAvg_ContiDays',
                           'PreNowSec_PreSec_Over3MovAvg_Prop',
                           'PreNowSec_PreSec_Over3MovAvg_MaxRate',
                           'PreNowSec_PostSec_Und3MovAvg_MaxRate',
                           'PostNowSec_MaxCls_Percentile_PostTurn',
                           'Cal_Date'
                           ]

    # GapValue指标
    gapvalue_column_names = ['PostSecStart_MaxPeakGap', 'PostSecStart_MaxPGV',
                             'PostSecStart_MaxValleyGap', 'PostSecStart_MaxValleyGapValue',
                             'PostSecStart_MedianPeakGap', 'PostSecStart_MedianPeakGapValue',
                             'PostSecStart_MedianValleyGap', 'PostSecStart_MedianValleyGapValue',
                             'PostSecStart_MinPeakGap', 'PostSecStart_MinValleyGap',
                             'PostSecPeak_MaxValleyGapValue', 'PostSecPeak_MaxPeakGapValue',
                             'PostSecPeak_MinValleyGapValue', 'PostSecPeak_MinPeakGapValue',
                             'PostSecPeak_MedianValleyGapValue', 'PostSecPeak_MedianPeakGapValue',
                             'PostNowSec_MaxPeakGap', 'PostNowSec_MaxPeakGapValue',
                             'PostNowSec_MaxValleyGap', 'PostNowSec_MaxValleyGapValue',
                             'PostNowSec_MedianPeakGap',
                             'PostNowSec_MedianPeakGapValue', 'PostNowSec_MedianValleyGapValue',
                             'PostNowSec_MinPeakGap',
                             'PostSecBottom_MaxPeakGap', 'PostSecBottom_MaxValleyGap',
                             'Peak2Sec_MaxPeakGap', 'Peak2Sec_MaxValleyGap',
                             'Peak2Sec_MaxPeakGapValue', 'Peak2Sec_MaxValleyGapValue',
                             'Now_PeakGap', 'Now_PeakGapValue',
                             'Now_ValleyGap', 'Now_ValleyGapValue',
                             'Recent3Day_MaxPeakGapValue', 'Recent3Day_MaxValleyGapValue',
                             'Recent3Day_MinPeakGapValue', 'Recent3Day_MinValleyGapValue',
                             'Peak3Day_MaxPeakGapValue',
                             'PostSecStart_PeakGap_HighQuntl', 'PostSecStart_PeakGapValue_HighQuntl',
                             'PostSecStart_PeakGap_LowQuntl', 'PostSecStart_PeakGapValue_LowQuntl',
                             'PostSecStart_ValleyGap_HighQuntl', 'PostSecStart_ValleyGapValue_HighQuntl',
                             'PostSecStart_ValleyGap_LowQuntl', 'PostSecStart_ValleyGapValue_LowQuntl',
                             'PostTurn_MedianPeakGapValue', 'PostTurn_PeakGapValue_HighQuntl',
                             'PostTurn_MaxPeakGapValue', 'PostTurn_MedianValleyGapValue',
                             'PostTurn_ValleyGapValue_HighQuntl', 'PostTurn_MaxValleyGapValue',
                             'Peak2Sec_PeakGap_HighQuntl', 'Peak2Sec_PeakGap_LowQuntl',
                             'Peak2Sec_ValleyGap_HighQuntl', 'Peak2Sec_MedianValleyGapValue',
                             'Peak2Sec_PeakGapValue_HighQuntl', 'Peak2Sec_ValleyGapValue_HighQuntl',
                             'PreNowSec_MinPeakGap', 'PreNowSec_MinPeakGap_Date',
                             'PreNowSec_MinValleyGap', 'PreNowSec_MaxValleyGap', 'PreNowSec_MaxValleyGapValue',
                             'PreNowSec_MedianPeakGap', 'PreNowSec_MedianValleyGap',
                             'NowSec_Recent3DValley_Over_HighQuntl_Num',
                             'NowSec_Recent3D_MaxValleyGapValue',
                             'PreTurnPeak_Sec_MaxPeakGapValue', 'PreSecPeak_Sec_MaxPeakGapValue',
                             #    'PostSecStart_MaxPeakGap2Yesd_Ratio',
                             #    'PostSecStart_PeakGapNow2Med_Ratio',
                             #    'PostSecStart_UndMed_SecondDate',
                             #    'PostSecStart_PeakGapValue_TrackSignal', 'PostSecPeak_PeakGapUndHighQuntl_Signal',
                             #    'PostSecPeak_Over_Btw_Num', 'PostPreNowSec_Over_Btw_Num',
                             #    'PostSecPeak_Over_HighQuntl_Num', 'PostPreNowSec_Over_HighQuntl_Num',
                             #    'PostNowSec_Over_HighQuntl_Num', 'PostNowSec_Over_Btw_Num',
                             #    'Recent3DValley_Over_HighQuntl_Num', 'Recent3DPeak_Over_HighQuntl_Num',
                             #    'Recent5DValley_Over_Median_Num', 'Recent5DPeak_Over_Median_Num',
                             #    'PostSecPeak_Rank3_ValleyGapValue',
                             #    'PostSecPeak_DownConsecutive_AvgLastDays', 'PostSecMaxRollAvg_DownConsecutive_AvgLastDays',
                             #    'PostNowSec_UpConsecutive_AvgLastDays', 'PostSecStart_UpConsecutive_AvgLastDays',
                             #    'PostPreSecPeak_DownConsecutive_AvgLastDays',
                             'Now_PostSecPeak_VGV_Desc_Rank', 'Now_PostSecPeak_VGV_MaxCoverDays',
                             'Now_PostSecStart_VGV_MaxCoverDays',
                             'Now_PostNowSec_PGV_Desc_Rank', 'Now_PostNowSec_PGV_MaxCoverDays',
                             'PostSecPeak_MaxVGV2Now_LastDays',
                             'PostSecPeak_VGV_MinRollAvg2MaxRatio', 'PostSecPeak_VGV_NowRollAvg2MaxRatio',
                             'PostSecPeak_VGV_MinRollAvg2Now_LastDays', 'Now_PostSecPeak_VGV_RollAvg_Asc_Rank',
                             'Now_PostSecPeak_VGV_RollAvg_MinCoverDays',
                             'PostSecStart_PGV_MaxRollAvg2MinRatio', 'PostSecStart_PGV_MaxRollAvg2MeanRatio',
                             'PostSecStart_PGV_MaxRollAvg2Now_LastDays',
                             'PostSecStart_PGV_NowRollAvg2MinRatio', 'Now_PostSecStart_PGV_RollAvg_Desc_Rank',
                             'Now_PostSecStart_PGV_RollAvg_Asc_Rank',
                             'Now_PGV_RollAvg_CoverDays', 'PostSecStart_PGV_MinRollAvg2Now_LastDays',
                             'PostSecPeak_PGV_MinRollAvg2MaxRatio', 'PostSecPeak_PGV_NowRollAvg2MaxRatio',
                             'PostSecPeak_PGV_MinRollAvg2Now_LastDays', 'Now_PostSecPeak_PGV_RollAvg_Asc_Rank',
                             'Now_PostSecPeak_PGV_RollAvg_MinCoverDays',
                             'PostSecStart_VGV_MaxRollAvg2MinRatio',
                             'PostSecStart_VGV_NowRollAvg2MinRatio', 'Now_PostSecStart_VGV_RollAvg_Desc_Rank',
                             'Now_VGV_RollAvg_CoverDays',
                             'PostSecPeak_PGV_MinRollAvg', 'PostSecPeak_PGV_MaxRollAvg',
                             'Recent2Day_MeanPeakGapValue', 'Recent2Day_MeanValleyGapValue',
                             'Recent9Day_MaxValleyGapValue', 'Recent9Day_MaxPeakGapValue',
                             'PostSecStart_PGV_MaxRollAvg', 'PostSecStart_PGV_MinRollAvg',
                             'PostSecStart_VGV_MaxRollAvg',
                             'PostSec_Peak_PGV_RollAvg', 'PostSec_Peak_PGV_RollAvg_Desc_Rank',
                             'PostPreNowBottom_PGV_MinRollAvg', 'Now_PostPreNowBottom_PGV_RollAvg_Asc_Rank',
                             'Now_PGV_RollAvg', 'Now_VGV_RollAvg', 'PostSecPeak_PGV_MinRollAvg_Date',
                             'PGV_Post_MinRollAvg_MaxVGV_CoverDays', 'PostNowSec_PGV_MinRollAvg',
                             'PostNowSec_PGV_MaxRollAvg', 'PostNowSec_PGV_MaxRollAvg_Date',
                             'Recent3Day_PGV_MinRollAvg', 'Recent3Day_PGV_MaxRollAvg',
                             'Recent3Day_PGV_MeanRollAvg',
                             'PostSecMaxRollAvg_PGV_MinRollAvg', 'PostSecMaxRollAvg_PGV_MinRollAvg_Date',
                             'PostSecMaxRollAvg_PGV_MinRollAvg2MaxRatio',
                             'PostSecMaxRollAvg_PGV_MinRollAvg2Now_LastDays',
                             'PostSecMaxRollAvg_PGV_PostMin_MaxRollAvg2Max_Ratio',
                             'PreSecMaxRollAvg_PGV_MinRollAvg', 'Post2Pre_PGV_MinRollAvg_Ratio',
                             'PostSecMaxRollAvg_PGV_RollAvg_Now2Min_Ratio', 'PreSecMaxRollAvg_PGV_MaxRollAvg2MinRatio',
                             'PreSecMaxRollAvg_PGV_MeanRollAvg', 'PostSecPeak_PGV_MaxRollAvg_Quarter',
                             'PostNowSec_PGV_MeanRollAvg', 'PostSecStart_PGV_MeanRollAvg',
                             'PostSecStart_PGV_MaxRollAvg_Date', 'PostSecStart_PeakDate_Diff',
                             'AftPreTurnPeak_PGV_MinRollAvg', 'AftPreTurnPeak_PGV_MinRollAvg2MaxRatio',
                             'AftPreTurnPeak_PGV_MinRollAvg2Now_LastDays', 'PreNowPeak_PGV_MeanRollAvg',
                             'PostSecMaxRollAvg_MinPGV', 'PostSecMaxRollAvg_MinPGV2Now_LastDays',
                             'PostSecMaxRollAvg_MinPGV2MaxRatio', 'Pre3Date_PeakGapValue', 'PostSecStart_MaxPGV_Date',
                             'PostMinRollAvg_RiseTrend_Prop', 'PostSecStart_RiseTrend_Prop', 'MinRollAvg_NowSec_Diff',
                             'PostSecPeak_DropTrend_Prop', 'PostPreNowPeak_DropTrend_Prop', 'MinRollAvg_Truncated_Diff',
                             'Now_PGVRollAvg_DownCount', 'Now_PGVRollAvg_UpCount',
                             'BfMinRollAvg_PGVRollAvg_DownCount', 'PostSec_MaxRollAvg_PeakDate_Diff',
                             'PostSecPeak_PGV_MeanRollAvg_TruncatedValue',
                             'SecConcave_PGV_MinRollAvg', 'SecConcave_PGV_MinRollAvg2MeanRatio',
                             'SecConcave_PGV_MinRollAvg_Date', 'SecConcave_PGV_MinRollAvg2Now_LastDays',
                             'Peak2Sec_PGV_MinRollAvg', 'Peak2Sec_PGV_MinRollAvg_Date',
                             'Peak2Sec_PGV_MinRollAvg2MeanRatio',
                             'PostSecPeak_DownConsecutive_Num', 'PostSecMaxRollAvg_DownConsecutive_Num',
                             'PostNowSec_UpConsecutive_Num', 'PostSecStart_UpConsecutive_Num',
                             'PreNowSec_PGV_MeanRollAvg', 'PostNowSec_MaxPeakGapValue_Date',
                             'DownConsecutive2Now_LastDays', 'UpConsecutive2Now_LastDays',
                             'PostNow2PostSec_PGV_MeanRollAvg_Ratio', 'PreNow2PostSec_PGV_MeanRollAvg_Ratio',
                             'DownConsecutive_PGVRollAvg_DiffRatio', 'UpConsecutive_PGVRollAvg_DiffRatio',
                             'DownConsecutive_SumRatio', 'UpConsecutive_SumRatio',
                             'DownConsecutive_Start_PGVRollAvg', 'UpConsecutive_Start_PGVRollAvg',
                             'Break_DownSecutiveStart_First2Now_LastDays', 'Break_UpSecutiveStart_First2Now_LastDays',
                             'DownConsecutive_Last2Break_LastDays', 'UpConsecutive_Last2Break_LastDays',
                             'PostNow2PreNow_PGV_MeanRollAvg_Ratio',
                             'PostSecMaxRollAvg_PGV_MaxRollAvg2Min_SumRatio',
                             'PostSecPeak_DownConsecutive_MaxLastDays', 'PostSecMaxRollAvg_DownConsecutive_MaxLastDays',
                             'PostNowSec_UpConsecutive_MaxLastDays', 'PostSecStart_UpConsecutive_MaxLastDays',
                             'PostPreSecPeak_DownConsecutive_Num',
                             'PostPreSecPeak_DownConsecutive_MaxLastDays',
                             'PostPreNowBottom_UpConsecutive_Num', 'PostPreNowBottom_UpConsecutive_MaxLastDays',
                             'PostPreNowBottom_DownConsecutive_Num', 'PostPreNowBottom_DownConsecutive_MaxLastDays',
                             'PreNowPeak_PGV_MinRollAvg2Now_LastDays', 'PreNowPeak_PGV_MinRollAvg2MaxRatio',
                             'PostSecMaxRollAvg_PGV_MinRollAvg2Now_SumRatio', 'PostSecPeak_PGV_MinRollAvg2Now_SumRatio',
                             'Peak2Sec_PGV_MinRollAvg2Sec_LastDays', 'Peak2Sec_PGV_MinRollAvg2Sec_SumRatio',
                             'PreNowPeak_PGV_MaxRollAvg', 'PreNowSec_PGV_MaxRollAvg',
                             'Peak2Sec_PGV_MaxRollAvg', 'Peak2Sec_PGV_MeanRollAvg',
                             'Peak2Sec_PGV_RollAvg_LowQuntl', 'PGVRollAvg_Now2PreSecLowQuntl_Ratio',
                             'PostSecStart_PGV_RollAvg_LowQuntl',
                             'PostSecStart_PGV_RollAvg_HighQuntl', 'PostSec2PreSec_PGV_MaxRollAvg_Ratio',
                             'PGVRollAvg_Now2PostSecLowQuntl_Ratio', 'PGVRollAvg_Now2PostSecHighQuntl_Ratio',
                             'PostSecMaxRollAvg_PGV_MeanRollAvg',
                             'Peak2Sec_PGV_RollAvg_VolRange', 'PostSecStart_PGV_RollAvg_VolRange',
                             'PostSecMaxRollAvg_PGV_RollAvg_VolRange',
                             'PostSec_RiseSec10Days_PGV_MaxRollAvg', 'PreNowSec_DropSec10Days_PGV_MinRollAvg',
                             'Is_Bottom_Reversal', 'Is_Expanding', 'Is_More_Volatile',
                             'PostSecPeak_MaxTO_Eff', 'PostSecPeak_MaxTO_Eff_Date',
                             'PostSecPeak_MaxTO_Eff2Now_LastDays',
                             'PostSecPeak_TO_Eff_Max2Min_Ratio',
                             'Now_TO_Eff',
                             'PostPreNowPeak_MaxTO_Eff', 'PostPreNowPeak_MaxTO_Eff_Date',
                             'PostPreNowPeak_MaxTO_Eff2Now_LastDays',
                             'PostPreNowPeak_MaxTO_Eff_CoverDays', 'PostPreNowPeak_TO_Eff_Max2Min_Ratio',
                             'PostSec_MinTO_Eff', 'PostSec_MinTO_Eff_Date', 'PostSec_MinTO_Eff2Now_LastDays',
                             'PostSec_MinTO_Eff_CoverDays', 'PostSec_TO_Eff_Min2Max_Ratio',
                             'PostPreNowPeak_U2D_MaxTO_Eff', 'PostPreNowPeak_U2D_MaxTO_Eff_Date',
                             'PostPreNowPeak_U2D_MaxTO_Eff2Now_LastDays', 'PostPreNowPeak_U2D_MaxTO_Eff_CoverDays',
                             'Eff_Recent2Previous_MinChange',
                             'Is_LowBound_Oscillation',
                             'Recent_EffPeak_ChangeRate', 'Recent_EffPeak_ChangeRate_Percentile',
                             'PostPreNowPeak_MaxTO_Eff_Band', 'PostSec_MinTO_Eff_Band',
                             'PostPreNowPeak_U2D_MaxTO_Eff_Band',
                             'Latest_TO_Eff_Band',
                             'Eff_Recent2Previous_Change',
                             'Eff_Avg_Peak_Period', 'Eff_Avg_Valley_Period',
                             'Eff_Peak_Intensity', 'Eff_Valley_Intensity',
                             'Days_From_Last_Peak', 'Days_From_Last_Valley',
                             'PostSecMaxRollAvg_PGV_MinRollAvg_Band', 'PostSecMaxRollAvg_PGV_MinRollAvg_CoverDays',
                             'PostSecStart_PGV_MaxRollAvg_Band', 'PostSecStart_PGV_MaxRollAvg_CoverDays',
                             'PostNowSec_PGV_MaxRollAvg_Band', 'PostNowSec_PGV_MaxRollAvg_CoverDays',
                             'Now_PGV_RollAvg_Rank',
                             'PGV_RollAvg_Recent2Previous_Change',
                             'PGV_RollAvg_NowSec2Previous_Change',
                             'PGV_RollAvg_VolatilityRatio',
                             'Efficiency_VolatilityRatio', 'U2D_Efficiency_VolatilityRatio',
                             'PostPreNowPeak_U2D_MinTO_Eff', 'PostPreNowPeak_U2D_MinTO_Eff_Date',
                             'PostPreNowPeak_U2D_MinTO_Eff2Now_LastDays',
                             'PostPreNowPeak_U2D_MinTO_Eff_CoverDays',
                             'Eff_Latest2Pre3Mean', 'UpEff_Latest2Pre3Mean',
                             'PostPreNow_MaxUpEff_Date', 'PostPreNow_MaxUpEff2Now_LastDays',
                             'Latest_Eff_Peak_Date', 'Latest_Eff_Peak2Now_Days',
                             'Latest_UpEff_Peak_Date', 'Latest_UpEff_Peak2Now_Days',
                             'PostPreNow_UpEff_MaxTO_Eff_Band', 'PostNowSec_PGVRollAvg_CoverDrop_Diff',
                             'PostNowSec_PGVRollAvg_CoverDrop_DiffRatio',
                             'PreTurnPeak_PRV_Top3Mean', 'SectionPeak_PRV_Top3Mean',
                             'SectionStart_PRV_Top3Mean', 'NowSec_PRV_Top3Mean',
                             'PreNowSec_PRV_Top3Mean', 'PreNowPeak_PRV_Top3Mean',
                             'Turn_PRV_Top3Mean', 'PostTurnPeak_PRV_Top3Mean', 'PostSecPeak_PRV_Top3Mean',
                             'PostSecPeak_PRV_Top3Mean_CoverDays',
                             'PostSecPeak_PRA2Close_CoverDays_Diff',
                             'Recent3Day_PRA_SectionStart_Rank', 'Recent3Day_PRA_NowSec_Rank',
                             'NowSec_PGV_MaxRollAvg_UpCoverDays', 'NowSec_PGV_MinRollAvg_DownCoverDays',
                             'NowSec_PGV_UpCoverPeriod_Max2Min_DiffRatio', 
                             'NowSec_PGV_DownCoverPeriod_Min2Max_DiffRatio',
                             'NowSec_PRA2Close_CoverDays_Diff',
                             'SecStart_PGV_MaxRollAvg_UpCoverDays', 'SecStart_PGV_MinRollAvg_DownCoverDays',
                             'SecStart_PGV_UpCoverPeriod_Max2Min_DiffRatio',
                             'SecStart_PGV_DownCoverPeriod_Min2Max_DiffRatio',
                             'SecStart_PRA2Close_CoverDays_Diff',
                             'NowSec_MaxPRA_Percentile_PostTurn',
                             'NowSec_MinPRA_Percentile_PostTurn',
                             'Now_PGV_Percentile_PostTurn',
                             'Recent3Day_MaxPGV_Percentile_PostTurn',
                             'Recent3Day_MaxPGV_PostTurn_Rank',
                             'Recent3Day_MaxPRA_PostTurn_Rank',
                             'Recent3Day_MinVGV_PostTurn_Rank',
                             'PostNowSec_MinVGV_PostTurn_Rank',
                             'PostTurn_Rank5_PGV', 
                             'PostTurn_Rank5_PRA',
                            #  'PostTurn_RevRank5_VGV',
                             'PostTurn_VGVRank5_NearNowDate',
                             'PostTurn_VGVRank5_NearNowDate_Days',
                             'NowSec_MaxPRA_Percentile_PostSectionPeak',
                             'NowSec_MinPRA_Percentile_PostSectionPeak',
                             'PostSecStart_MaxPRA_Percentile_PostSectionPeak',
                             'PostSecStart_MaxPRA_Percentile_PostTurn',
                             'SecPeak2NowSec_PRA_UpBand',
                             'SecPeak2NowSec_PRA_LowBand',
                             'Turn2NowSec_PRA_UpBand',
                             'Turn2NowSec_PRA_LowBand',
                             'Latest_EffPeak2NowSec_Diff',
                            #  'PostPreNowPeak_PRA_MaxRate',
                            #  'PostPreNowPeak_PRA_MaxRate_Date',
                            #  'PostPreNowPeak_PRA_MaxRate_Date2NowSec_Diff',
                            #  'PostPreNowPeak_MaxRate_PRA_Percentile',
                             'Now_PRA_Percentile_PostSectionPeak',
                             'PostNowSec_PRA_MaxRate',
                             'PostNowSec_PRA_MaxRate_Date',
                             'PostNowSec_MaxRate2Now_LastDays',
                             'PostNowSec_NowSec2MaxRate_LastDays',
                             'PostSecStart_PRA_MaxRate',
                             'PostSecStart_PRA_MaxRate_Date',
                             'PostSecStart_VRA_MaxRate',
                             'PostSecStart_VRA_MaxRate_Date',
                             'Now_PRA_Rate',
                             'Now_VRA_Rate',
                             'Recent3Day_PRA_MaxRate',
                             'Recent3Day_VRA_MaxRate',
                             'Recent3Day_PRA_MaxRate_CoverDays',
                             'Recent3Day_VRA_MaxRate_CoverDays',
                             'PostNowSec_PRA_MaxRate_BreachCount',
                             'PostSecStart_PRA_MaxRate_BreachCount',
                             'PostNowSec_MaxRate_PRA_Percentile',
                             'PostNowSec_MaxRate_Post2Pre_DiffRatio',
                             'SecStart_PRA_FirstBreach_Date',
                             'SecStart_FirstBreach2Now_LastDays',
                             'SecStart_PRA_LastBreach_Date',
                             'SecStart_LastBreach2Now_LastDays',
                             'SecStart_PRA_Breach_Count',
                             'SecStart_PRA_LastBreach_ContinuousDays',
                             'NowSec_PRA_FirstBreach_Date',
                             'NowSec_FirstBreach2Now_LastDays',
                             'NowSec_PRA_LastBreach_Date',
                             'NowSec_LastBreach2Now_LastDays',
                             'NowSec_PRA_Breach_Count',
                             'NowSec_PRA_LastBreach_ContinuousDays',
                             'PostSecStart_MaxPGVDate2Now_LastDays',
                             'PostSecStart_MaxVGV',
                             'PostSecStart_MaxVGV_Date',
                             'PostSecStart_MaxVGVDate2Now_LastDays',
                             'PostSecStart_PGV_Max2Mean_Ratio',
                             'PostSecStart_VGV_Max2Mean_Ratio',
                             'PostSecStart_PGV_Now2Mean_Ratio',
                             'PostNowSec_PGV_Now2Mean_Ratio',
                             'PostNowSec_PGV_Max2Mean_Ratio',
                             'PGV_Now2Pre3Days_Ratio',
                             'PostSec_PGV_TurnP_Date',
                             'PostSec_PGV_TurnP_AbsChange',
                             'PostSec_PGV_TurnP_RelaChange',
                             'PostSec_PGV_PostTurnP_LastDays',
                             'Peak2Sec_PGV_TurnP_Date',
                             'Peak2Sec_PGV_TurnP_AbsChange',
                             'Peak2Sec_PGV_TurnP_RelaChange',
                             'Peak2Sec_PGV_PostTurnP_LastDays',
                             'PostPreNowPeak_PGV_MinRollAvg',
                             'PostPreNowPeak_PGV_MinRollAvg_Date',
                             'PostPreNowPeak_PostMinPRA_MaxPRA',
                             'PostPreNowPeak_PostMinPRA_MaxPRA2Now_Days',
                             'PostSectionPeak_PGV_MinRollAvg',
                             'PostSectionPeak_PGV_MinRollAvg_Date',
                             'PostSectionPeak_PostMinPRA_MaxPRA',
                             'PostSectionPeak_PostMinPRA_MaxPRA2Now_Days',
                             'Cal_Date']
    # Result_Break = pd.concat([Result_Break, pd.DataFrame(columns=column_names)], sort=False, axis=1)
    return common_column_names, gapvalue_column_names


def multiprocess_3(Result_Break, Stock_Data, index_data, mode):
    """多进程计算指标结果
    
    主要功能:
    - 使用多进程并行计算每只股票的技术指标
    - 根据mode参数执行不同的计算逻辑:
        1. ADJ模式: 直接计算单只股票指标
        2. pick模式: 并行计算多只股票指标
    - 合并所有计算结果并返回
    
    参数:
    Result_Break: 待计算股票的基本信息DataFrame
    Stock_Data: 股票历史行情数据
    index_data: 指数历史数据
    mode: 运行模式,可选'ADJ'或'pick'
    
    返回:
    Result_Output: 计算完成的技术指标结果DataFrame"""
    from multiprocessing import Pool
    p = Pool(7)
    pool_data_list = []
    Results = pd.DataFrame()
    for index in tqdm(Result_Break.index):
        ts_code = Result_Break.loc[index, 'ts_code']
        if len(Result_Break) > 1:
            stk_data = Stock_Data.query('ts_code==@ts_code'
                                        ).sort_values('trade_date', ascending=True).set_index('trade_date', drop=False)
        else:
            stk_data = Stock_Data
        if len(stk_data) == 0:
            continue
        if mode == 'ADJ':
            print(Result_Break.loc[index, 'ts_code'])
            Result_Break.loc[index, :] = cal_metrics_3(Result_Break.loc[index, :].copy(), stk_data, index_data, mode)
        else:
            pool_data_list.append(p.apply_async(func=cal_metrics_3,
                                                args=(Result_Break.loc[index, :].copy(), stk_data, index_data, mode)))
    p.close()
    p.join()
    if mode == 'ADJ':
        Result_Output = Result_Break
    else:
        for pool_data in pool_data_list:
            if len(Results) == 0:
                Results = pd.DataFrame(pool_data.get()).T
            else:
                Results = pd.concat([Results, pd.DataFrame(pool_data.get()).T], axis=0)
        Result_Output = Results[Result_Break.columns].copy()
    return Result_Output


def cal_metrics_3(Result_Loc, stk_data, index_data, mode):
    """计算更新Result_Loc字段数据
    
    主要功能:
    - 计算股票的技术指标和统计数据
    - 更新Result_Loc中的各项指标字段
    - 分析股价走势特征和趋势变化
    
    参数:
    Result_Loc: 待更新的单只股票数据行
    stk_data: 股票历史行情数据
    index_data: 指数历史数据 
    mode: 运行模式,可选'ADJ'或'pick'
    
    返回:
    Result_Loc: 更新后的股票数据行
    
    主要计算指标包括:
    - 周期涨跌幅和趋势统计
    - 区段分析和拐点识别
    - 价格突破和回调分析
    - 成交量和换手率分析
    - 相对指数表现分析
    - 趋势拟合和偏离度计算
    - 波动率和风险指标
    - 技术形态识别
    """
    # 计算周期涨跌幅和趋势统计
    periodchang, trendstat = period_stat(
        stk_close=stk_data['close'], stk_open=stk_data['open'], turnover=stk_data['turnover'], style='Stock')

    # 准备基础数据
    stk_data = stk_data.copy()
    stk_ratio = (stk_data['close'] / stk_data['close'].shift(1) - 1) * 100
    stk_movavg = (stk_data['close'] / stk_data['close'].shift(3) - 1) * 100
    stk_data['stk_ratio'] = stk_ratio
    stk_data['abs_ratio'] = abs(stk_ratio)
    stk_data['stk_rollmax'] = stk_data['close'].rolling(window=3, closed='left').max()
    stk_data['stk_rollmin'] = stk_data['close'].rolling(window=3, closed='left').min()
    stk_data['stk_rollmean'] = stk_data['close'].rolling(window=3, closed='left').mean()
    stk_data['stk_rollmax_shift'] = stk_data['stk_rollmax'].shift(1)
    stk_data['stk_rollmin_shift'] = stk_data['stk_rollmin'].shift(1)
    stk_data['stk_rollmean_shift'] = stk_data['stk_rollmean'].shift(1)

    # 存储总股本
    Result_Loc['Total_Share'] = stk_data['total_share'].iloc[-1]

    # 计算当日涨跌幅
    Result_Loc['Now_DayRatio'] = round(stk_ratio.iloc[-1], 3)

    # 计算最高价涨幅
    stk_high_ratio = (stk_data['high'] / stk_data['close'].shift(1) - 1) * 100

    # 计算收盘开盘价差
    ClsOpen_Ratio = round(abs(stk_data['close'] / stk_data['open'] - 1) * 100, 3)
    stk_data['clsopen_ratio'] = ClsOpen_Ratio
    stk_data['max_ratio'] = stk_data[['abs_ratio', 'clsopen_ratio']].max(axis=1)

    # 计算总市值
    Result_Loc['Total_MV'] = stk_data['total_mv'].iloc[-1] \
        if pd.notnull(stk_data['total_mv'].iloc[-1]) \
        else round(stk_data['close'].iloc[-1] * stk_data['total_share'].iloc[-1], 0)

    # 如果没有周期数据则直接返回
    if len(periodchang) == 0:
        return Result_Loc

    # 计算上涨周期
    period_rise = periodchang.query('avg_ratio>0')
    if len(period_rise) > 1:
        # 寻找最低点日期
        pnum = -2
        low_date = stk_data.loc[period_rise['start_date'].iloc[pnum]:, 'close'].idxmin()

        # 判断周期特征
        while abs(pnum) < len(period_rise) \
                and (stk_data.loc[period_rise['start_date'].iloc[pnum - 2:pnum], 'close'].min() <
                     stk_data.loc[period_rise['start_date'].iloc[pnum + 1], 'close']) \
                and len(periodchang[periodchang['start_date'] > period_rise['start_date'].iloc[pnum + 1]]) > 0 \
                and (period_rise['last_days'].iloc[pnum + 1] >
                     periodchang[periodchang['start_date'] >
                                 period_rise['start_date'].iloc[pnum + 1]]['last_days'].iloc[0]) \
                and len(stk_data.loc[
                        stk_data.loc[
                        period_rise['start_date'].iloc[pnum - 1]:low_date, 'close'].idxmax():low_date]) < 40:
            pnum -= 1
            low_date = stk_data.loc[period_rise['start_date'].iloc[pnum]:, 'close'].idxmin()

        # 计算转折日期
        turn_date = stk_data.loc[period_rise['end_date'].iloc[pnum - 1]:, 'close'].idxmin() \
            if abs(pnum) < len(period_rise) \
            else stk_data.loc[periodchang['start_date'].iloc[0]:, 'close'].idxmin()
    else:
        turn_date = stk_data.loc[periodchang['start_date'].iloc[0]:, 'close'].idxmin()

    # 计算前半年最低收盘价日期
    half_year_ago = (pd.to_datetime(stk_data.index[-1]) - pd.Timedelta(days=180)).strftime('%Y-%m-%d')
    # 找到大于等于half_year_ago的第一个交易日
    half_year_start = stk_data.index[stk_data.index >= half_year_ago][0]
    half_year_period_start = periodchang.query('start_date<@half_year_start & avg_ratio<0')['start_date'].iloc[-1] \
        if len(periodchang.query('start_date<@half_year_start & avg_ratio<0')) > 0 else half_year_start
    half_year_min_date = stk_data.loc[half_year_period_start:, 'close'].idxmin()
    if turn_date > half_year_min_date:
        turn_date = half_year_min_date

    # 计算转折后上涨周期
    periodchang_up = periodchang.query('end_date>@turn_date & avg_ratio>0')
    Result_Loc['Period_TurnDate'] = turn_date

    # 计算结束日期
    cal_end_date = stk_data.loc[turn_date:, 'close'].idxmax()

    # 判断趋势状态
    if (len(periodchang_up) > 0
        and stk_data.loc[cal_end_date:, 'close'].min() >
        (stk_data.loc[turn_date, 'close'] + stk_data.loc[cal_end_date, 'close']) / 2) \
            or len(periodchang.query('avg_ratio<0')) == 0:
        Result_Loc['Period_Trend'] = '上行'
        Result_Loc['Now_Period_StartDate'] = turn_date
    else:
        Result_Loc['Period_Trend'] = '下行'
        Result_Loc['Now_Period_StartDate'] = periodchang.query('avg_ratio<0')['start_date'].iloc[-1]

    # 计算当前周期指标
    period_coef = coef_regress(
        stk_data.loc[periodchang['start_date'].iloc[-1]:periodchang['end_date'].iloc[-1], 'close'], mode='err')
    Result_Loc['Now_Period_StartDate'] = periodchang['start_date'].iloc[-1]
    Result_Loc['Now_Period_Lastdays'] = periodchang['last_days'].iloc[-1]
    Result_Loc['Now_Period_SumRatio'] = round(periodchang['sum_ratio'].iloc[-1], 3)
    Result_Loc['Now_Period_AvgRatio'] = round(periodchang['avg_ratio'].iloc[-1], 3)
    Result_Loc['Now_Period_R2'] = round(period_coef[0], 3)

    # 计算转折前下跌周期
    dropperiod_preturn = periodchang.query('start_date<@turn_date & avg_ratio<0')
    if len(dropperiod_preturn) > 1:
        # 寻找前期高点
        pre_num = -2
        while abs(pre_num) < len(dropperiod_preturn) \
                and stk_data.loc[dropperiod_preturn['start_date'].iloc[pre_num], 'close'] > \
                stk_data.loc[dropperiod_preturn['start_date'].iloc[pre_num + 1], 'close'] \
                and stk_data.loc[dropperiod_preturn['end_date'].iloc[pre_num], 'close'] > \
                (stk_data.loc[dropperiod_preturn['end_date'].iloc[-1], 'close'] +
                 stk_data.loc[dropperiod_preturn['start_date'].iloc[pre_num + 1], 'close']) / 2:
            pre_num -= 1
        PreTurn_PeakDate = dropperiod_preturn['start_date'].iloc[pre_num + 1]
    else:
        PreTurn_PeakDate = dropperiod_preturn['start_date'].iloc[-1] if len(dropperiod_preturn) > 0 else turn_date
    Result_Loc['TurnConcave_LastDays'] = len(stk_data.loc[PreTurn_PeakDate:]) - 1

    # 计算转折前周期指标
    if len(dropperiod_preturn) > 0:
        period_coef = coef_regress(
            stk_data.loc[dropperiod_preturn['start_date'].iloc[-1]:turn_date, 'close'], mode='err')
        Result_Loc['PreTurn_Period_Lastdays'] = dropperiod_preturn['last_days'].iloc[-1]
        Result_Loc['PreTurn_Period_SumRatio'] = round(dropperiod_preturn['sum_ratio'].iloc[-1], 3)
        Result_Loc['PreTurn_Period_AvgRatio'] = round(dropperiod_preturn['avg_ratio'].iloc[-1], 3)
        Result_Loc['PreTurn_Period_R2'] = round(period_coef[0], 3)
        Result_Loc['PreTurn_Period_Sum2Std'] = round(
            abs(stk_data.loc[turn_date, 'close'] / stk_data.loc[
                dropperiod_preturn['start_date'].iloc[-1], 'close'] - 1
                ) * 100 / period_coef[4], 3) if period_coef[4] != 0 else 0

        # 计算高点到转折点指标
        Result_Loc['Peak2Turn_LastDays'] = len(stk_data.loc[PreTurn_PeakDate:turn_date]) - 1
        Result_Loc['Peak2Turn_SumRatio'] = round(
            (stk_data.loc[turn_date, 'close'] / max(stk_data.loc[PreTurn_PeakDate, 'close'],
                                                    stk_data.loc[PreTurn_PeakDate, 'pre_close']) - 1) * 100, 3)
        Result_Loc['Peak2Turn_AvgRatio'] = round(
            (stk_data.loc[turn_date, 'close'] / stk_data.loc[PreTurn_PeakDate, 'close'] - 1) * 100 /
            len(stk_data.loc[PreTurn_PeakDate:turn_date]), 3)

        # 计算2年内高点到转折点天数
        Y2_Date = (pd.to_datetime(stk_data.index[-1], format='%Y-%m-%d') - relativedelta(years=2)).strftime('%Y-%m-%d')
        dropperiod_2y = dropperiod_preturn.query('end_date>@Y2_Date')['start_date'].values
        if len(dropperiod_2y) > 0:
            max_periodDate = stk_data.loc[dropperiod_2y, 'close'].idxmax()
            Result_Loc['PeakN2Y_2Turn_LastDays'] = len(stk_data.loc[max_periodDate:turn_date]) - 1

    # 计算趋势状态指标
    periodchang_drop = periodchang.query('sum_ratio<0')
    if len(periodchang_drop) > 0:
        # 寻找底部起始日期
        snum = -2
        while abs(snum) <= len(periodchang_drop) \
                and periodchang_drop['last_days'].iloc[snum + 1] < 40 \
                and stk_data.loc[periodchang_drop['end_date'].iloc[snum], 'close'] < stk_data.loc[
            periodchang_drop['end_date'].iloc[snum + 1], 'close']:
            snum -= 1
        bottom_startdate = stk_data.loc[periodchang_drop['start_date'].iloc[snum + 1]:, 'close'].idxmin()
        Result_Loc['Long_Trend'] = '上行' \
            if abs(snum) > 2 or len(stk_data.loc[bottom_startdate:]) > 25 else '下行'
    else:
        Result_Loc['Long_Trend'] = '上行'

    # 计算底部日期
    if len(trendstat.query('long_state=="下行"')) > 0:
        Bottom_Date = stk_data.loc[trendstat.query('long_state=="下行"')['ls_start_date'].iloc[-1]:, 'close'].idxmin()
    elif len(trendstat.query('long_state=="上行"')) > 0:
        Bottom_Date = stk_data.loc[trendstat.query('long_state=="上行"')['ls_start_date'].iloc[-1]:, 'close'].idxmin()
    else:
        Bottom_Date = stk_data['close'].idxmin()
    if Bottom_Date == stk_data.index[0]:
        Bottom_Date = stk_data.loc[stk_data.loc[:turn_date, 'close'].idxmax():, 'close'].idxmin()
    if Bottom_Date > turn_date:
        Bottom_Date = turn_date

    # 计算长期趋势指标
    Result_Loc['LongTrend_PeriodNum_Rise'] = len(periodchang.query('end_date>@Bottom_Date & sum_ratio>0'))
    Result_Loc['LongTrend_PeriodNum_Drop'] = len(periodchang.query('end_date>@Bottom_Date & sum_ratio<0'))
    PostTurn_MaxDate = stk_data.loc[turn_date:, 'close'].idxmax()
    Result_Loc['PostTurn_Period_RiseNum'] = len(periodchang.query('end_date>@turn_date & avg_ratio>0'))

    # 计算区段统计
    rise_p_startdate = periodchang.query('sum_ratio>0 & end_date<@Bottom_Date')['start_date'].iloc[-1] \
        if len(periodchang.query('sum_ratio>0 & end_date<@Bottom_Date')) > 0 \
        else Bottom_Date
    section_rise_origin, section_d, day_list = section_stat(stk_data=stk_data,
                                                            start_date=stk_data['trade_date'].iloc[0],
                                                            end_date=stk_data['trade_date'].iloc[-1],
                                                            index_data=index_data,
                                                            index_mode='Yes')
    drop_p_startdate = periodchang.query('sum_ratio<0 & start_date<@turn_date')['start_date'].iloc[-2] \
        if len(periodchang.query('sum_ratio<0 & start_date<@turn_date')) > 1 \
        else stk_data.loc[:turn_date, 'close'].idxmax()
    _, section_drop, day_drop = section_stat(stk_data=stk_data,
                                             start_date=drop_p_startdate,
                                             end_date=turn_date,
                                             index_data=index_data,
                                             index_mode='Yes')
    section_drop = section_drop.iloc[:-1] \
        if len(section_drop) > 0 and section_drop['lastdays'].iloc[-1] == 1 else section_drop

    # 如果没有区段数据则返回
    if len(section_rise_origin) == 0 or (len(section_drop) == 0 and len(section_d) == 0):
        return Result_Loc

    # 计算转折后下跌区段
    section_d_aft_turn = section_d.query('start_date>@turn_date').copy()

    # 计算上行突破覆盖横盘天数
    PostBottom_MaxClose = stk_data.loc[Bottom_Date:, 'close'].max()
    if len(stk_data.loc[:Bottom_Date].query('close>=@PostBottom_MaxClose')) > 0:
        Break_Peak_Date = stk_data.loc[:Bottom_Date].query('close>=@PostBottom_MaxClose').index[-1]
    else:
        Break_Peak_Date = stk_data.index[0]

    # 计算突破10%覆盖天数
    if len(stk_data.loc[:Bottom_Date].query('close>@PostBottom_MaxClose*1.1')) > 0:
        if len(stk_data.loc[:Bottom_Date].query('close>@PostBottom_MaxClose*1.1')) >= 2:
            list_date = stk_data.loc[:Bottom_Date].query('close>@PostBottom_MaxClose*1.1')['trade_date']
            num = -1
            while abs(num) < min(4, len(list_date)) and len(
                    stk_data.loc[list_date.iloc[num - 1]:list_date.iloc[num]]) <= 2:
                num -= 1
            if abs(num) < min(4, len(list_date)):
                Break_Peak_Date_10 = stk_data.loc[:Bottom_Date].query('close>@PostBottom_MaxClose*1.1').index[num - 1]
            else:
                Break_Peak_Date_10 = stk_data.loc[:Bottom_Date].query('close>@PostBottom_MaxClose*1.1').index[-1]
        else:
            Break_Peak_Date_10 = stk_data.loc[:Bottom_Date].query('close>@PostBottom_MaxClose*1.1').index[-1]
    else:
        Break_Peak_Date_10 = stk_data.index[0]

    # 计算突破20%覆盖天数
    if len(stk_data.loc[:Bottom_Date].query('close>@PostBottom_MaxClose*1.2')) > 0:
        if len(stk_data.loc[:Bottom_Date].query('close>@PostBottom_MaxClose*1.2')) >= 2:
            list_date = stk_data.loc[:Bottom_Date].query('close>@PostBottom_MaxClose*1.2')['trade_date']
            num = -1
            while abs(num) < min(4, len(list_date)) and len(
                    stk_data.loc[list_date.iloc[num - 1]:list_date.iloc[num]]) <= 2:
                num -= 1
            if abs(num) < min(4, len(list_date)):
                Break_Peak_Date_20 = stk_data.loc[:Bottom_Date].query('close>@PostBottom_MaxClose*1.2').index[num - 1]
            else:
                Break_Peak_Date_20 = stk_data.loc[:Bottom_Date].query('close>@PostBottom_MaxClose*1.2').index[-1]
        else:
            Break_Peak_Date_20 = stk_data.loc[:Bottom_Date].query('close>@PostBottom_MaxClose*1.2').index[-1]
    else:
        Break_Peak_Date_20 = stk_data.index[0]

    # 计算最高价和日期
    Max_Close = stk_data.loc[Bottom_Date:, 'close'].max()
    Max_Close_Date = stk_data.loc[Bottom_Date:, 'close'].idxmax()

    # 计算突破高点日期
    period_peak = periodchang.query('avg_ratio<0 & start_date>@Break_Peak_Date_20 & start_date<@Max_Close_Date'
                                    )['start_date'].values
    if len(period_peak) > 0:
        Break_Max_Date = stk_data.loc[period_peak, 'close'].idxmax()
    elif len(periodchang.query('end_date<@Break_Peak_Date & avg_ratio>0')) > 0:
        Break_Max_Date = periodchang.query('end_date<@Break_Peak_Date & avg_ratio>0')['end_date'].iloc[-1]
    else:
        Break_Max_Date = Break_Peak_Date_10

    # 更新突破相关指标
    Result_Loc['Long_PeakDate'] = Break_Max_Date
    Result_Loc['Concave_Break_Date'] = Break_Peak_Date
    Result_Loc['Concave_Break_Days2Now'] = len(stk_data.loc[Break_Peak_Date:])
    Result_Loc['CoverDaysDiff_10'] = len(stk_data.loc[Break_Peak_Date_10:]) - len(stk_data.loc[Break_Peak_Date:])
    Result_Loc['CoverRatioBand_10'] = round((stk_data.loc[Break_Peak_Date_10, 'close'] /
                                             stk_data.loc[Break_Peak_Date_10:, 'close'].min() - 1) * 100, 3)

    Result_Loc['CoverDaysDiff_20'] = len(stk_data.loc[Break_Peak_Date_20:]) - len(stk_data.loc[Break_Peak_Date_10:])
    Result_Loc['CoverRatioBand_20'] = round((stk_data.loc[Break_Peak_Date_20, 'close'] /
                                             stk_data.loc[Break_Peak_Date_20:, 'close'].min() - 1) * 100, 3)

    # 计算上行波峰压力位状态及目标价格
    PeriodDrop_Startdates = trendstat.apply(
        lambda fn: stk_data.loc[fn['start_date']:fn['end_date'], 'close'].idxmax(), axis=1).to_list() \
        if len(trendstat) > 0 else None

    if PeriodDrop_Startdates is not None:
        # 寻找合适的突破比率
        lnum = -1
        while abs(lnum) < len(PeriodDrop_Startdates) \
                and (stk_data.loc[PeriodDrop_Startdates[lnum], 'close'] / stk_data['close'].iloc[-1] - 1) * 100 < -15 \
                and (stk_data.loc[PeriodDrop_Startdates[lnum - 1], 'close'] > stk_data.loc[
            PeriodDrop_Startdates[lnum:], 'close'].max()
                     and len(stk_data.loc[PeriodDrop_Startdates[lnum - 1]:PeriodDrop_Startdates[lnum]]) < 30):
            lnum -= 1

        # 更新突破相关指标
        Result_Loc['Period_Break_Ratio'] = round(
            (stk_data.loc[PeriodDrop_Startdates[lnum], 'close'] / stk_data['close'].iloc[-1] - 1) * 100, 3)
        Result_Loc['Period_Break_Date'] = PeriodDrop_Startdates[lnum]


        # 计算转折后最高价前后覆盖天数
        Pre_StartDate = stk_data.loc[:turn_date][
            stk_data.loc[:turn_date, 'close'] > stk_data.loc[PostTurn_MaxDate, 'close']].index[-1] \
            if len(stk_data.loc[:turn_date][
                       stk_data.loc[:turn_date, 'close'] > stk_data.loc[
                           PostTurn_MaxDate, 'close']]) > 0 else PostTurn_MaxDate
        Result_Loc['CoverDays_Bf_PostturnMaxDate'] = len(stk_data.loc[Pre_StartDate:PostTurn_MaxDate])
        Result_Loc['CoverDays_Aft_PostturnMaxDate'] = len(stk_data.loc[PostTurn_MaxDate:])

    # 计算目标价格相对最高价比率
    Result_Loc['TargetRatio_High'] = round(
        (Result_Loc['Target_Price'] / stk_data.loc[turn_date:, 'high'].max() - 1) * 100, 3)

    # 计算转折日期前后状态
    PreTurn_ValleyDate_1 = periodchang.query('end_date<@turn_date & avg_ratio>0')['start_date'].iloc[-1] \
        if len(periodchang.query('end_date<@turn_date & avg_ratio>0')) > 0 else ''
    PreTurn_ValleyDate_2 = periodchang.query('end_date<@turn_date & avg_ratio>0')['start_date'].iloc[-2] \
        if len(periodchang.query('end_date<@turn_date & avg_ratio>0')) > 1 else ''

    # 计算转折相关比率
    if PreTurn_ValleyDate_1 != '':
        Result_Loc['Turn_Period_GapRatio'] = round((stk_data.loc[turn_date, 'close'] /
                                                    stk_data.loc[PreTurn_ValleyDate_1, 'close'] - 1) * 100, 3)
    elif PreTurn_ValleyDate_2 != '':
        Result_Loc['PreTurn_Period_GapRatio'] = round((stk_data.loc[PreTurn_ValleyDate_1, 'close'] /
                                                       stk_data.loc[PreTurn_ValleyDate_2, 'close'] - 1) * 100, 3)

    # 计算转折后上涨区段
    section_rise_aft_turn = section_rise_origin.query('end_date>@turn_date').copy()

    # 计算转折后下跌区段
    section_d_aft_postmax = section_d.query('start_date>@turn_date').copy()

    # 更新转折后区段指标
    if len(section_rise_aft_turn) > 0:
        if len(section_d_aft_postmax) > 0:
            Result_Loc['PostTurn_Sec_Min_SumRatio'] = section_d_aft_postmax['sumratio'].min()
            Result_Loc['PostTurn_Sec_Min_ExtreRatio'] = section_d_aft_postmax['extre_ratio'].min()
        Result_Loc['PostTurn_Sec_Max_SumRatio'] = round(section_rise_aft_turn['sumratio'].max(), 3)
        Result_Loc['PostTurn_Sec_Avg_LastDays'] = round(section_rise_aft_turn['lastdays'].mean(), 3)
        Result_Loc['PostTurn_Sec_Max_ExtreRatio'] = round(section_rise_aft_turn['extre_ratio'].max(), 3)

    # 计算下跌区段指标
    if len(section_drop) > 0:
        Result_Loc['PreTurn_LastSec_Lastdays'] = section_drop['lastdays'].iloc[-1]
        Result_Loc['PreTurn_LastSec_AvgRatio'] = section_drop['avgratio'].iloc[-1]
        Result_Loc['PreTurn_LastSec_SumRatio'] = section_drop['sumratio'].iloc[-1]
        Result_Loc['PreTurn_LastSec_ExtreRatio'] = section_drop['extre_ratio'].iloc[-1]
        Result_Loc['PreTurn_LastSec_AvgTurnover'] = section_drop['avg_turnover'].iloc[-1]
        Result_Loc['PreTurn_LastPeriod_SecNum'] = len(section_drop)

    # 计算转折后第一个上涨区段指标
    if len(section_rise_aft_turn) > 0:
        Result_Loc['PostTurn_1stSec_Lastdays'] = section_rise_aft_turn['lastdays'].iloc[0]
        Result_Loc['PostTurn_1stSec_AvgRatio'] = round(section_rise_aft_turn['avgratio'].iloc[0], 3)
        Result_Loc['PostTurn_1stSec_SumRatio'] = round(section_rise_aft_turn['sumratio'].iloc[0], 3)
        Result_Loc['PostTurn_1stSec_ExtreRatio'] = round(section_rise_aft_turn['extre_ratio'].iloc[0], 3)
        Result_Loc['PostTurn_1stSec_AvgTurnover'] = section_rise_aft_turn['avg_turnover'].iloc[0]
        Result_Loc['PostTurn_Sec_RiseNum'] = len(section_rise_aft_turn)
        Result_Loc['PostTurn_Sec_DropNum'] = len(section_d_aft_turn.query('lastdays>2'))

    # 计算转折点后的最大回撤
    PostTurn_MaxDrop, _ = retracement_from_high(stk_data.loc[turn_date:, 'close'], 0)

    # 计算底部后的最大回撤
    PostBottom_MaxDrop, _ = retracement_from_high(stk_data.loc[Bottom_Date:, 'close'], 0) \
        if Bottom_Date is not None else None
    Result_Loc['PostBottom_MaxDrop'] = round(PostBottom_MaxDrop, 3)  # 记录底部后的最大回撤

    # 统计转折点后涨幅超过7%的天数
    Result_Loc['PostTurn_Over7Num'] = sum(stk_ratio.loc[turn_date:] > 7)  # 记录转折点后日涨幅>7%的天数

    Period_Maxdate = periodchang.query('start_date>@turn_date & sum_ratio<0')['start_date'].iloc[-1] \
        if len(periodchang.query('start_date>@turn_date & sum_ratio<0')) > 0 \
        else '-'
    Pick_MaxDate = Period_Maxdate \
        if Period_Maxdate != '-' and Period_Maxdate < PostTurn_MaxDate \
           and stk_data.loc[:PostTurn_MaxDate, 'close'].iloc[-2] < stk_data.loc[Period_Maxdate, 'close'] \
        else PostTurn_MaxDate
    if len(periodchang.query('start_date>@turn_date & sum_ratio<0')) == 0 \
            and (len(section_d) == 0 or (section_d['avgratio'].mean() * section_d['lastdays'].max() > -25
                                         and len(section_d.query('end_date>@PostTurn_MaxDate')) <= 2
                                         and (len(section_d.query('end_date>@PostTurn_MaxDate')) == 0 or
                                              section_d.query('end_date>@PostTurn_MaxDate')['lastdays'].max() < 10))) \
            and PostTurn_MaxDrop > -15 \
            and PreTurn_ValleyDate_1 != '' \
            and ((stk_data.loc[turn_date, 'close'] / stk_data.loc[PreTurn_ValleyDate_1, 'close'] - 1) * 100 > -25
                 or Result_Loc['PostTurn_Over7Num'] >= 2):
        Result_Loc['Now_State'] = '上行'
    elif len(periodchang.query('start_date>@turn_date & sum_ratio<0')) > 0 \
            and len(section_rise_aft_turn[section_rise_aft_turn['start_date'] < Pick_MaxDate]) > 0 \
            and (section_rise_aft_turn[section_rise_aft_turn['start_date'] < Pick_MaxDate]['extre_ratio'].iloc[-1] > 9
                 or sum(
                stk_high_ratio.loc[section_rise_aft_turn[section_rise_aft_turn['start_date'] < Pick_MaxDate][
                    'start_date'].iloc[-1]:
                section_rise_aft_turn[section_rise_aft_turn['start_date'] < Pick_MaxDate]['end_date'].iloc[
                    -1]] > 9) >= 1) \
            and len(section_d) > 0 and len(section_d[section_d['end_date'] > Pick_MaxDate]) >= 1 \
            and PreTurn_ValleyDate_1 != '' \
            and ((stk_data.loc[turn_date, 'close'] / stk_data.loc[PreTurn_ValleyDate_1, 'close'] - 1) * 100 > -25
                 or Result_Loc['PostTurn_Over7Num'] >= 2):
        Result_Loc['Now_State'] = '回档'
    elif (stk_data.loc[PostTurn_MaxDate, 'close'
          ] / stk_data.loc[turn_date:stk_data.index[-1], 'close'].min() - 1) * 100 <= 25 \
            and len(section_d) > 0 and section_d['avgratio'].mean() > -1 \
            and section_d['lastdays'].max() >= 8 and section_rise_aft_turn['lastdays'].max() >= 8 \
            and stk_data.loc[turn_date:, 'close'].min() >= stk_data.loc[turn_date, 'close'] \
            and PreTurn_ValleyDate_1 != '' \
            and ((stk_data.loc[turn_date, 'close'] / stk_data.loc[PreTurn_ValleyDate_1, 'close'] - 1) * 100 > -25
                 or Result_Loc['PostTurn_Over7Num'] >= 2):
        Result_Loc['Now_State'] = '横盘'

    if (stk_high_ratio.iloc[-1] > 5 or stk_ratio.iloc[-1] < -4) \
            and ((-5 < (stk_data['close'].iloc[-1] / stk_data['close'].iloc[-4] - 1) * 100 < 5)
                 or (-5 < (stk_data['close'].iloc[-2] / stk_data['close'].iloc[-5] - 1) * 100 < 5)
                 or (-5 < (stk_data['close'].iloc[-3] / stk_data['close'].iloc[-6] - 1) * 100 < 5)):
        Result_Loc['Now_Vol_Trend'] = '扩大'
    elif (stk_data['close'].iloc[-3:].max() / stk_data['close'].iloc[-3:].min() - 1) * 100 <= 5 \
            and -3 <= (stk_data['close'].iloc[-1] / stk_data['close'].iloc[-4] - 1) * 100 <= 3:
        Result_Loc['Now_Vol_Trend'] = '收窄'

    # 测算section最高价日期
    if len(section_d) > 0 and len(section_rise_origin) > 0:
        snum = -2
        sec_lowdate = stk_data.loc[section_rise_origin['end_date'].iloc[snum]:, 'close'].idxmin() \
            if abs(snum) < len(section_rise_origin) else None
        while abs(snum) < len(section_rise_origin) \
                and min(stk_data.loc[section_rise_origin['start_date'].iloc[snum], 'close'],
                        stk_data.loc[section_rise_origin['start_date'].iloc[snum - 1], 'close']) < \
                stk_data.loc[section_rise_origin['start_date'].iloc[snum + 1], 'close'] \
                and len(section_rise_origin[section_rise_origin['start_date'] <
                                            section_rise_origin['end_date'].iloc[snum + 1]]) > 0 \
                and len(stk_data.loc[stk_data.loc[section_rise_origin['start_date'].iloc[snum]:sec_lowdate, 'close'
                                     ].idxmax():sec_lowdate]) < 20:
            # and section_d['sumratio'].iloc[snum] >= -15 \
            # and section_d['lastdays'].iloc[snum+1] <= section_rise_origin[section_rise_origin['start_date'] <
            #     section_d['start_date'].iloc[snum+1]]['lastdays'].iloc[-1]:
            snum -= 1
            sec_lowdate = stk_data.loc[section_rise_origin['end_date'].iloc[snum]:, 'close'].idxmin()
        snum = snum + 1 if abs(snum) > len(section_d) else snum
        Section_StartDate = stk_data.loc[section_d['end_date'].iloc[snum]:, 'close'].idxmin()
        # Section_StartDate = stk_data.loc[section_d['start_date'].iloc[-1]:, 'close'].idxmin()
    elif len(section_rise_origin) > 0:
        Section_StartDate = stk_data.loc[section_rise_origin['start_date'].iloc[-1]:, 'close'].idxmin()
    else:
        Section_StartDate = turn_date

    if Section_StartDate < turn_date:
        Section_StartDate = turn_date

    # 计算目标价格
    Result_Loc['Target_Price'], _ = cal_target_price(
        Section_StartDate, stk_data, periodchang)
    Result_Loc['Target_Ratio'] = round((Result_Loc['Target_Price'] / stk_data['close'].iloc[-1] - 1) * 100, 3)
    
    try:
        if len(section_d) > 1:
            min_index = stk_data.loc[section_d['start_date'].iloc[-1]:, 'close'].idxmin()
            Now_SecDate = min_index \
                if section_d['lastdays'].iloc[-1] >= 2 and \
                   stk_data.loc[min_index, 'close'] < stk_data.loc[min_index, 'stk_rollmin_shift'] \
                else stk_data.loc[section_d['start_date'].iloc[-2]:, 'close'].idxmin()
        elif len(section_d) > 0:
            Now_SecDate = stk_data.loc[section_d['start_date'].iloc[-1]:, 'close'].idxmin()
        else:
            Now_SecDate = Section_StartDate
    except Exception as e:
        print('Now_SecDate Error:', e, Result_Loc['ts_code'], section_d['start_date'].iloc[-1])

    Result_Loc['Now_SecDate'] = Now_SecDate

    Result_Loc['PostNowSec_PeakDate'] = stk_data.loc[Now_SecDate:, 'close'].idxmax()
    Result_Loc['PostNowSec_Peak2Now_LastDays'] = len(stk_data.loc[Result_Loc['PostNowSec_PeakDate']:]) - 1

    section_rise_pre = section_rise_origin.query('end_date<@Section_StartDate')
    period_maxdate = stk_data.loc[turn_date:Section_StartDate, 'close'].idxmax() \
        if len(stk_data.loc[turn_date:Section_StartDate, 'close']) > 0 and turn_date != Section_StartDate else None
    period_aftturn = periodchang.query('end_date>@period_maxdate & avg_ratio<0') \
        if period_maxdate is not None and len(periodchang.query('end_date>@period_maxdate & avg_ratio<0')) > 0 \
        else None

    Section_PeakDate = stk_data.loc[:Section_StartDate, 'close'].idxmax()

    # if period_aftturn is not None and period_aftturn['avg_ratio'].min() >= -1:
    #     Section_PeakDate = period_maxdate
    if len(section_rise_pre) > 0:
        lnum = -2
        while len(section_rise_pre) > 1 and abs(lnum) <= len(section_rise_pre) \
                and stk_data.loc[section_rise_pre['end_date'].iloc[lnum], 'close'
        ] > stk_data.loc[section_rise_pre['end_date'].iloc[lnum + 1], 'close']:
            lnum -= 1
        Section_PeakDate = section_rise_pre['end_date'].iloc[lnum + 1] \
            if len(section_rise_pre) > 1 else section_rise_pre['end_date'].iloc[-1]
    elif len(section_drop) > 0:
        secPeak_drop = section_drop.query('start_date<@Section_StartDate')
        if len(secPeak_drop) > 1:
            lnum = -2
            while len(secPeak_drop) > 1 and abs(lnum) + 1 < len(secPeak_drop) \
                    and stk_data.loc[secPeak_drop['start_date'].iloc[lnum], 'close'
            ] > stk_data.loc[secPeak_drop['start_date'].iloc[lnum + 1], 'close']:
                lnum -= 1
            Section_PeakDate = secPeak_drop['start_date'].iloc[lnum + 1] \
                if len(secPeak_drop) > 1 else secPeak_drop['start_date'].iloc[-1]

    Result_Loc['Section_PeakDate'] = Section_PeakDate
    Result_Loc['PostNowSec_Over7Num'] = sum(stk_ratio.loc[Now_SecDate:].iloc[1:] > 7) \
        if len(stk_ratio.loc[Now_SecDate:]) > 0 else 0
    Result_Loc['PostNowSec_HighOver7Num'] = sum(stk_high_ratio.loc[Now_SecDate:].iloc[1:] > 7) \
        if len(stk_high_ratio.loc[Now_SecDate:]) > 0 else 0
    # 计算Now_SecDate后连续涨幅超过7%的天数
    # 计算最新日期往前连续涨幅超过7%的天数
    continuous_over7_days = 0
    if len(stk_ratio.loc[Now_SecDate:]) > 0:
        for ratio in stk_ratio.loc[Now_SecDate:].iloc[::-1]:  # 从最新日期往前遍历
            if ratio > 7:
                continuous_over7_days += 1
            else:
                break  # 一旦遇到不超过7%的涨幅就中断
    Result_Loc['PostNowSec_ContiOver7Days'] = continuous_over7_days

    Result_Loc['Section_StartDate'] = Section_StartDate
    PostSecStart_PeakDate = stk_data.loc[Section_StartDate:, 'close'].idxmax()
    Result_Loc['PostSecStart_PeakDate'] = PostSecStart_PeakDate
    PreNow_SecDate = stk_data.loc[section_rise_origin.query('end_date<@Now_SecDate')
                                  ['start_date'].iloc[-1]:Now_SecDate, 'close'].idxmax() \
        if len(section_rise_origin.query('end_date<@Now_SecDate')) > 0 \
        else stk_data.loc[:Now_SecDate, 'close'].idxmax()
    Result_Loc['PreNow_SecDate'] = PreNow_SecDate
    # if Section_StartDate == Now_SecDate and turn_date < Section_StartDate:
    #     PreNow_PeakDate = stk_data.loc[turn_date:Section_StartDate, 'close'].idxmax()
    #     if stk_data.loc[PreNow_PeakDate:Now_SecDate, 'close'].min() < stk_data.loc[Now_SecDate, 'close']:
    #         min_btw_peaknow = stk_data.loc[PreNow_PeakDate:Now_SecDate, 'close'].idxmin()
    #         PreNow_PeakDate = stk_data.loc[min_btw_peaknow:Now_SecDate, 'close'].idxmax()
    if Section_StartDate < Now_SecDate:
        PreNow_PeakDate = stk_data.loc[Section_StartDate:Now_SecDate, 'close'].idxmax()
    else:
        PreNow_PeakDate = Section_PeakDate
    Result_Loc['PreNow_PeakDate'] = PreNow_PeakDate

    Result_Loc['Sec2Now_PeakDate'] = stk_data.loc[Section_StartDate:Now_SecDate, 'close'].idxmax() \
        if Section_StartDate < Now_SecDate else Now_SecDate
    if Result_Loc['PostSecStart_PeakDate'] < Now_SecDate:
        Result_Loc['PreNow_BottomDate'] = stk_data.loc[Result_Loc['PostSecStart_PeakDate']:Now_SecDate, 'close'].idxmin()
    elif PreNow_PeakDate < Now_SecDate:
        Result_Loc['PreNow_BottomDate'] = stk_data.loc[PreNow_PeakDate:Now_SecDate, 'close'].idxmin()
    else:
        Result_Loc['PreNow_BottomDate'] = Now_SecDate
    sectionrise_enddates = section_rise_origin.query('end_date>@Section_StartDate')['end_date'].tolist()
    base_close_price = stk_data.loc[Result_Loc['PostSecStart_PeakDate'], 'close'] * 0.98
    # Recent_PeakDate = \
    # stk_data.loc[(stk_data.index.isin(sectionrise_enddates)) & (stk_data['close'] > base_close_price)].index[-1] \
    #     if len(sectionrise_enddates) > 0 \
    #     and len(stk_data.loc[(stk_data.index.isin(sectionrise_enddates)) &
    #                          (stk_data['close'] > base_close_price)]) > 0 \
    #     else Result_Loc['PostSecStart_PeakDate']
    Recent_PeakDate = Result_Loc['PostSecStart_PeakDate']
    Result_Loc['PostSecPeak2Now_LastDays'] = len(stk_data.loc[Recent_PeakDate:]) - 1
    Result_Loc['PostSecPeak2Now_AvgTurnover'] = round(stk_data.loc[Recent_PeakDate:, 'turnover'].mean(), 3)
    Result_Loc['PostSecPeak2Now_SumRatio'] = round(
        (stk_data['close'].iloc[-1] / max(stk_data.loc[Recent_PeakDate, 'close'],
                                          stk_data.loc[Recent_PeakDate, 'pre_close']) - 1) * 100, 3)
    Result_Loc['PostSecPeak2Now_AvgRatio'] = round(
        Result_Loc['PostSecPeak2Now_SumRatio'] / Result_Loc['PostSecPeak2Now_LastDays'], 3) \
        if Result_Loc['PostSecPeak2Now_LastDays'] > 0 else 0

    Result_Loc['Now_MaxSum'] = round((stk_data.loc[Section_StartDate:, 'close'].max() /
                                      min(stk_data.loc[Section_StartDate, 'close'],
                                          stk_data.loc[Section_StartDate, 'pre_close']) - 1) * 100, 3)

    Result_Loc['Turn_Target_Price'] = round(stk_data.loc[PreTurn_PeakDate, 'close'] * 2 -
                                            stk_data.loc[PreTurn_PeakDate:, 'close'].min(), 3)
    Result_Loc['Turn_Target_Ratio'] = round((Result_Loc['Turn_Target_Price'] /
                                             stk_data.loc[Section_StartDate:, 'close'].max() - 1) * 100, 3)
    Result_Loc['Section_Target_Price'] = round(stk_data.loc[Section_PeakDate, 'close'] * 2 -
                                               stk_data.loc[Section_PeakDate:, 'close'].min(), 3)
    Result_Loc['Section_Target_Ratio'] = round((Result_Loc['Section_Target_Price'] /
                                                stk_data.loc[Section_StartDate:, 'close'].max() - 1) * 100, 3)

    Result_Loc['PostNowSec_SumRatio'] = round(
        (stk_data['close'].iloc[-1] / min(stk_data.loc[Now_SecDate, 'close'],
                                          stk_data.loc[Now_SecDate, 'pre_close']) - 1
         ) * 100, 3)
    Result_Loc['PostNowSec_LastDays'] = len(stk_data.loc[Now_SecDate:]) - 1
    Result_Loc['PostNowSec_AvgRatio'] = round(
        Result_Loc['PostNowSec_SumRatio'] / Result_Loc['PostNowSec_LastDays']
        if Result_Loc['PostNowSec_LastDays'] != 0 else 0, 3)
    Result_Loc['PostNowSec_ExtreRatio'] = round(stk_ratio.loc[Now_SecDate:].max(), 3)
    Result_Loc['PostNowSec_Adverse2Trend'] = round(
        len(stk_data.loc[Now_SecDate:].query('close<stk_rollmean_shift')) /
        len(stk_data.loc[Now_SecDate:]), 3) \
        if len(stk_data.loc[Now_SecDate:]) > 0 else -1
    Result_Loc['PostNowSec_AvgTurnover'] = round(stk_data.loc[Now_SecDate:, 'turnover'].mean(), 3)
    Result_Loc['PostNowSec_MaxTurnover'] = round(stk_data.loc[Now_SecDate:, 'turnover'].max(), 3)
    Result_Loc['PreNowSec_Sec_AvgTurnover'] = round(stk_data.loc[PreNow_SecDate:Now_SecDate, 'turnover'].mean(), 3)
    Result_Loc['PreNowSec_Sec_MaxTurnover'] = round(stk_data.loc[PreNow_SecDate:Now_SecDate, 'turnover'].max(), 3)
    Result_Loc['PreNowSec_SumRatio'] = round(
        (min(stk_data.loc[Now_SecDate, 'close'], stk_data.loc[Now_SecDate, 'pre_close']) /
         max(stk_data.loc[PreNow_SecDate, 'close'], stk_data.loc[PreNow_SecDate, 'pre_close']) - 1) * 100, 3)
    Result_Loc['PreNowSec_LastDays'] = len(stk_data.loc[PreNow_SecDate:Now_SecDate]) - 1
    Result_Loc['PreNowSec_AvgRatio'] = round(Result_Loc['PreNowSec_SumRatio'] / Result_Loc['PreNowSec_LastDays'], 3) \
        if Result_Loc['PreNowSec_LastDays'] != 0 else 0
    Result_Loc['PreNowSec_MaxClsOpenRatio'] = round(ClsOpen_Ratio.loc[PreNow_SecDate:Now_SecDate].max(), 3)
    Result_Loc['PreNowSec_ExtreRatio'] = round(stk_ratio.loc[PreNow_SecDate:].min(), 3)
    # Result_Loc['PreNowSec_Adverse2Trend'] = round(
    #     abs(stk_ratio.loc[PreNow_SecDate:Now_SecDate][stk_ratio.loc[PreNow_SecDate:Now_SecDate] > 0].iloc[1:].sum()) /
    #     abs(stk_ratio.loc[PreNow_SecDate:Now_SecDate][stk_ratio.loc[PreNow_SecDate:Now_SecDate] < 0].sum()), 3) \
    #     if len(stk_ratio.loc[PreNow_SecDate:Now_SecDate][stk_ratio.loc[PreNow_SecDate:Now_SecDate] < 0]) > 0 \
    #        and len(stk_ratio.loc[PreNow_SecDate:Now_SecDate][stk_ratio.loc[PreNow_SecDate:Now_SecDate] > 0]) > 1 else 0
    Result_Loc['PreNowSec_Adverse2Trend'] = round(
        len(stk_data.loc[PreNow_SecDate:Now_SecDate].query('close>stk_rollmax_shift')) /
        len(stk_data.loc[PreNow_SecDate:Now_SecDate]), 3) \
        if len(stk_data.loc[PreNow_SecDate:Now_SecDate]) > 0 else -1

    prenowsec_section = section_d.query('start_date<=@Now_SecDate') \
        if len(section_d.query('start_date<=@Now_SecDate')) > 0 \
        else section_drop.query('start_date<=@Now_SecDate')
    Result_Loc['PreNowSec_Und2ContiDays'] = prenowsec_section['und2_contidays'].iloc[-1]

    Result_Loc['PostTurn2Peak_LastDays'] = len(stk_data.loc[turn_date:stk_data.loc[turn_date:, 'close'].idxmax()]) - 1
    Result_Loc['PostTurn_RiseRatio'] = round(
        (stk_data.loc[turn_date:, 'close'].max() / stk_data.loc[turn_date, 'close'] - 1
         ) * 100, 3)
    # Result_Loc['PostTurn_RiseAvg'] = round(
    #     (stk_data.loc[turn_date:, 'close'].max() / stk_data.loc[turn_date, 'close'] - 1
    #      ) * 100 / Result_Loc['PostTurn_LastDays'], 3)
    Result_Loc['PostTurn_MaxDrop'] = round(PostTurn_MaxDrop, 3)

    Result_Loc['PostTurn_BfPeak_MaxDrop'], _ = retracement_from_high(
        stk_data.loc[turn_date:PostTurn_MaxDate, 'close'], 1) \
        if len(stk_data.loc[turn_date:PostTurn_MaxDate]) > 0 else 0

    Result_Loc['Bottom_Date'] = Bottom_Date
    Result_Loc['Bottom2Now_Ratio'] = round(
        (stk_data['close'].iloc[-1] / stk_data.loc[Bottom_Date, 'close'] - 1
         ) * 100, 3)
    Result_Loc['PostBottom_RiseRatio'] = round(
        (stk_data.loc[Bottom_Date:, 'close'].max() / stk_data.loc[Bottom_Date, 'close'] - 1
         ) * 100, 3)
    Result_Loc['PostBottom_Lastdays'] = len(stk_data.loc[Bottom_Date:]) - 1
    Result_Loc['PostBottom_PeriodNum'] = len(periodchang.query('end_date>@Bottom_Date'))
    turn_close = stk_data.loc[Result_Loc['Bottom_Date'], 'close']
    peak_close = stk_data.loc[Result_Loc['Long_PeakDate'], 'close']
    Result_Loc['SectionStart_Position'] = round((stk_data.loc[Section_StartDate, 'close'] - turn_close)
                                                / (peak_close - turn_close), 3) \
        if peak_close != turn_close else 0

    Result_Loc['PostSecStart_LastDays'] = len(stk_data.loc[Section_StartDate:]) - 1
    Result_Loc['PostSecStart_SumRatio'] = round((stk_data['close'].iloc[-1] /
                                                 min(stk_data.loc[Section_StartDate, 'close'],
                                                     stk_data.loc[Section_StartDate, 'pre_close']) - 1) * 100, 3)
    Result_Loc['PostSecStart_MeanClose'] = round(stk_data.loc[Section_StartDate:, 'close'].mean(), 2)
    Result_Loc['PostSecStart_AvgRatio'] = round((stk_data['close'].iloc[-1] /
                                                 min(stk_data.loc[Section_StartDate, 'close'],
                                                     stk_data.loc[Section_StartDate, 'pre_close']) - 1) * 100 /
                                                len(stk_data.loc[Section_StartDate:]), 3) \
        if len(stk_data.loc[Section_StartDate:]) > 0 else 0
    Result_Loc['PostSecStart_MaxDailyRatio'] = round(max(0, stk_ratio.loc[Section_StartDate:].max()), 3)
    Result_Loc['PostSecStart_MinDailyRatio'] = round(stk_ratio.loc[Section_StartDate:].iloc[1:].min(), 3) \
        if len(stk_ratio.loc[Section_StartDate:]) > 1 else -100
    Result_Loc['PostSecStart_MinQuntlRatio'] = round(
        min(-1.5, stk_ratio.loc[Section_StartDate:][stk_ratio.loc[Section_StartDate:] < 0].iloc[1:].quantile(0.2)), 3) \
        if sum(stk_ratio.loc[Section_StartDate:] < 0) > 1 else -100
    Result_Loc['PostSecStart_AvgTurnover'] = round(stk_data.loc[Section_StartDate:Now_SecDate, 'turnover'].mean(), 3)
    # Result_Loc['PostSecStart_Adverse2Trend'] = round(
    #     abs(stk_ratio.loc[Section_StartDate:][stk_ratio.loc[Section_StartDate:] < 0].iloc[1:].sum()) /
    #     abs(stk_ratio.loc[Section_StartDate:][stk_ratio.loc[Section_StartDate:] > 0].sum()), 3) \
    #     if len(stk_ratio.loc[Section_StartDate:][stk_ratio.loc[Section_StartDate:] > 0]) > 0 \
    #        and len(stk_ratio.loc[Section_StartDate:][stk_ratio.loc[Section_StartDate:] < 0]) > 1 else 0
    Result_Loc['PostSecStart_Adverse2Trend'] = round(
        len(stk_data.loc[Section_StartDate:].query('close<stk_rollmin_shift')) /
        len(stk_data.loc[Section_StartDate:]), 3) \
        if len(stk_data.loc[Section_StartDate:]) > 0 else -1

    if Result_Loc['PreNowSec_ExtreRatio'] <= Result_Loc['PostSecStart_MinQuntlRatio']:
        Result_Loc['PreNowSec_BelowMinDaily'] = 1
        if Result_Loc['PreNowSec_ExtreRatio'] <= stk_ratio.loc[Section_StartDate:PreNow_SecDate].min():
            Result_Loc['PreNowSec_BelowMinDaily'] = 2
    else:
        Result_Loc['PreNowSec_BelowMinDaily'] = 0

    if len(section_d_aft_turn) > 0 \
            and (len(section_rise_aft_turn) == 0
                 or section_rise_aft_turn['end_date'].iloc[-1] < section_d_aft_turn['end_date'].iloc[-1]):
        Result_Loc['Sec_IndexDiff_Signal'] = 'Drop'
        Result_Loc['Sec_IndexDiff_Ratio'] = round(section_d_aft_turn['index_diff'].iloc[-1], 3)
        Result_Loc['Sec_IndexDiff_StartDate'] = section_d_aft_turn['end_date'].iloc[-1]
    elif len(section_rise_aft_turn) > 0 \
            and (len(section_d_aft_turn) == 0 or
                 section_rise_aft_turn['end_date'].iloc[-1] > section_d_aft_turn['end_date'].iloc[-1]):
        Result_Loc['Sec_IndexDiff_Signal'] = 'Rise'
        Result_Loc['Sec_IndexDiff_Ratio'] = round(section_rise_aft_turn['index_diff'].iloc[-1], 3)
        Result_Loc['Sec_IndexDiff_StartDate'] = section_rise_aft_turn['end_date'].iloc[-1]

    # 区段日均涨跌幅
    Result_Loc['NowSec_Break_Ratio'] = round((stk_data.loc[Now_SecDate:, 'close'].max() /
                                               stk_data.loc[Result_Loc['Section_PeakDate'], 'close'] - 1) * 100, 3)

    if len(section_rise_origin.query('end_date<@Now_SecDate')) > 0:
        bma_startdate = section_rise_origin.query('end_date<@Now_SecDate')['start_date'].iloc[-1]
        rise_portion, _, drop_num, _ = cal_movingavg_stat(stk_data.loc[bma_startdate:PreNow_SecDate].copy())
        Result_Loc['PreNow_Rise_BMA_Prop'] = round(rise_portion, 3)
        Result_Loc['PreNow_Rise_BMA_Days'] = drop_num
    if Section_PeakDate is not None and Section_PeakDate < Section_StartDate:
        drop_portion, _, rise_num, _ = cal_movingavg_stat(stk_data.loc[Section_PeakDate:Section_StartDate].copy(),
                                                          mode='Drop')
        Result_Loc['PreSec_Sec_OMA_Prop'] = round(drop_portion, 3)
        Result_Loc['PreSec_Sec_OMA_Days'] = rise_num
    if len(stk_data.loc[Now_SecDate:]) > 1:
        rise_portion, _, drop_num, _ = cal_movingavg_stat(stk_data.loc[Now_SecDate:].copy())
        Result_Loc['PostNowSec_Rise_BMA_Prop'] = round(rise_portion, 3)
        Result_Loc['PostNowSec_Rise_BMA_Days'] = drop_num

    Result_Loc['Section_PostPeak_MaxDrop'], _ = retracement_from_high(stk_data.loc[Section_PeakDate:, 'close'], 1)
    Result_Loc['Recent4P_MaxLastDays'] = periodchang.query('start_date<=@Section_StartDate')['last_days'].iloc[
                                         -4:].max()

    # 曲线拟合
    if len(section_rise_pre) > 0 and turn_date < section_rise_pre['end_date'].iloc[-1]:
        coef1 = coef_regress(stk_data.loc[turn_date:section_rise_pre['end_date'].iloc[-1], 'close'], mode='err')
        coef2 = coef_regress(stk_data.loc[turn_date:section_rise_pre['end_date'].iloc[-2],
                             'close'], mode='err') \
            if len(section_rise_pre) > 1 and turn_date < section_rise_pre['end_date'].iloc[-2] \
            else [0, 0, 0, 0, 0]
        coef_r2 = max(coef1[0], coef2[0])
        if coef_r2 == coef2[0] and len(section_rise_pre) > 1:
            sec_enddate = section_rise_pre['end_date'].iloc[-2]
            coef_bias = (stk_data.loc[section_rise_pre['end_date'].iloc[-2], 'close'] /
                         stk_data.loc[turn_date, 'close'] - 1) * 100 / coef2[4] \
                if coef2[4] != 0 else 0
        else:
            sec_enddate = section_rise_pre['end_date'].iloc[-1]
            coef_bias = (stk_data.loc[section_rise_pre['end_date'].iloc[-1], 'close'] /
                         stk_data.loc[turn_date, 'close'] - 1) * 100 / coef1[4] \
                if coef1[4] != 0 else 0
        Result_Loc['PostTurn_Reg_R2'] = round(coef_r2, 3)
        Result_Loc['PostTurn_Reg_Sum2Std'] = round(coef_bias, 3)
        Result_Loc['PostTurn_Reg_Lastdays'] = len(stk_data.loc[turn_date:sec_enddate]) - 1
        Result_Loc['PostTurn_Reg_EndDate'] = sec_enddate

    elif turn_date < stk_data.index[-1] and len(stk_data.loc[turn_date:stk_data.index[-1]]) >= 3:
        coef2 = coef_regress(stk_data.loc[turn_date:stk_data.index[-1], 'close'], mode='err')
        coef_r2 = coef2[0]
        coef_bias = (stk_data['close'].iloc[-1] /
                     min(stk_data.loc[turn_date, 'close'], stk_data.loc[turn_date, 'pre_close']) - 1) * 100 / coef2[4] \
            if coef2[4] != 0 else 0
        Result_Loc['PostTurn_Reg_R2'] = round(coef_r2, 3)
        Result_Loc['PostTurn_Reg_Sum2Std'] = round(coef_bias, 3)
        Result_Loc['PostTurn_Reg_Lastdays'] = len(stk_data.loc[turn_date:stk_data.index[-2]]) - 1
        Result_Loc['PostTurn_Reg_EndDate'] = stk_data.index[-2]

    coef = coef_regress(stk_data.loc[Result_Loc['Section_StartDate']:, 'close'], mode='err')
    std_bias = round((stk_data['close'].iloc[-1] / stk_data.loc[
        Result_Loc['Section_StartDate'], 'close'] - 1) * 100 / coef[4], 3) \
        if coef[4] != 0 else 0

    Result_Loc['PostSecStart_Reg_R2'] = round(coef[0], 3)
    Result_Loc['PostSecStart_Reg_Sum2Std'] = round(std_bias, 3)
    Result_Loc['PostSecStart_RiseRatio'] = round(
        (stk_data.loc[Section_StartDate:, 'close'].max() / stk_data.loc[Section_StartDate, 'close'] - 1) * 100, 3)
    Result_Loc['PostSecStart_MaxDrop'], Result_Loc['PostSecStart_MaxDrop_LastDays'] = \
        retracement_from_high(stk_data.loc[Section_StartDate:, 'close'], mode=1)

    # 突破100天高点价格
    D100_Date = stk_data.index[-min(100, len(stk_data))]
    period_startdate = D100_Date
    D100_MaxClose = stk_data.loc[period_startdate:Now_SecDate, 'close'].max()
    Result_Loc['Break_D100_Ratio'] = round((stk_data.loc[Now_SecDate:, 'close'].max() / D100_MaxClose - 1) * 100, 3)
    
    Result_Loc['NowSec_BreakSecPeak_Ratio'] = round(
        (stk_data.loc[Now_SecDate:, 'close'].max() / stk_data.loc[Section_PeakDate, 'close'] - 1) * 100, 3)

    # 日涨跌幅状态
    stk_ratio_turn = stk_ratio.loc[turn_date:]
    if sum(stk_ratio_turn < -3.5) > 0:
        DropDate = stk_ratio_turn[stk_ratio_turn < -3.5].index[-1]
        DropDate_Lag = stk_ratio_turn.loc[DropDate:].index[1] if len(stk_ratio_turn.loc[DropDate:]) > 1 else None
        Restore_Date = stk_data.loc[DropDate_Lag:][stk_data.loc[DropDate_Lag:, 'close'] >=
                                                   stk_data.loc[DropDate, 'open']].index[0] \
            if DropDate_Lag is not None and np.any(stk_data.loc[DropDate_Lag:, 'close'] >=
                                                   stk_data.loc[DropDate, 'open']) \
            else None
        Days_lap = len(stk_data.loc[DropDate:Restore_Date]) - 1 \
            if Restore_Date is not None else -1
        Result_Loc['PostTurn_Recent_Neg4_DropDate'] = DropDate
        Result_Loc['PostTurn_Recent_Neg4_Threshold'] = min(stk_data.loc[DropDate, 'pre_close'],
                                                           stk_data.loc[DropDate, 'open'])
        # Result_Loc['PostTurn_Recent_Neg4_DropDate_Close'] = stk_data.loc[DropDate, 'close']
        Result_Loc['PostTurn_Recent_Neg4_RecovDays'] = Days_lap
        Result_Loc['PostTurn_Recent_Neg4_DropRatio'] = round(stk_ratio_turn[stk_ratio_turn < -3.5].iloc[-1], 3)
        Result_Loc['PostTurn_Recent_Neg4_RecovDate'] = Restore_Date
        Result_Loc['PostTurn_Recent_Neg4_Recov2Now_LastDays'] = len(stk_data.loc[Restore_Date:]) - 1

    stk_ratio_peak = stk_ratio.loc[Section_PeakDate:]
    if sum(stk_ratio_peak < -3.5) > 0:
        DropDate = stk_ratio_peak[stk_ratio_peak < -3.5].index[-1]
        DropDate_Lag = stk_ratio_peak.loc[DropDate:].index[1] if len(stk_ratio_peak.loc[DropDate:]) > 1 else None
        Restore_Date = stk_data.loc[DropDate_Lag:][
            stk_data.loc[DropDate_Lag:, 'close'] >= stk_data.loc[DropDate, 'open']].index[0] \
            if DropDate_Lag is not None and np.any(stk_data.loc[DropDate_Lag:, 'close'] >=
                                                   stk_data.loc[DropDate, 'open']) \
            else None
        Days_lap = len(stk_data.loc[DropDate:Restore_Date]) - 1 \
            if Restore_Date is not None and Restore_Date > DropDate else -1

        Result_Loc['PostPeak_Recent_Neg4_DropDate'] = DropDate
        Result_Loc['PostPeak_Recent_Neg4_Threshold'] = min(stk_data.loc[DropDate, 'pre_close'],
                                                           stk_data.loc[DropDate, 'open'])
        Result_Loc['PostPeak_Recent_Neg4_DropDate_Close'] = stk_data.loc[DropDate, 'close']
        Result_Loc['PostPeak_Recent_Neg4_RecovDays'] = Days_lap
        Result_Loc['PostPeak_Recent_Neg4_DropRatio'] = round(stk_ratio_peak[stk_ratio_peak < -3.5].iloc[-1], 3)
        Result_Loc['PostPeak_Recent_Neg4_RecovDate'] = Restore_Date
        Result_Loc['PostPeak_Recent_Neg4_Recov2Now_LastDays'] = len(stk_data.loc[Restore_Date:]) - 1
    elif stk_ratio_peak.loc[PreNow_SecDate:Now_SecDate].min() < -2:
        DropDate = stk_ratio_peak.loc[PreNow_SecDate:Now_SecDate].idxmin()
        DropDate_Lag = stk_ratio_peak.loc[DropDate:].index[1] if len(stk_ratio_peak.loc[DropDate:]) > 1 else None
        Restore_Date = stk_data.loc[DropDate_Lag:][
            stk_data.loc[DropDate_Lag:, 'close'] >= stk_data.loc[DropDate, 'open']].index[0] \
            if DropDate_Lag is not None \
               and np.any(stk_data.loc[DropDate_Lag:, 'close'] >= stk_data.loc[DropDate, 'open']) \
            else None
        Days_lap = len(stk_data.loc[DropDate:Restore_Date]) - 1 \
            if Restore_Date is not None else -1

        Result_Loc['PostPeak_Recent_Neg4_DropDate'] = DropDate
        Result_Loc['PostPeak_Recent_Neg4_Threshold'] = min(stk_data.loc[DropDate, 'pre_close'],
                                                           stk_data.loc[DropDate, 'open'])
        Result_Loc['PostPeak_Recent_Neg4_DropDate_Close'] = stk_data.loc[DropDate, 'close']
        Result_Loc['PostPeak_Recent_Neg4_RecovDays'] = Days_lap
        Result_Loc['PostPeak_Recent_Neg4_DropRatio'] = round(stk_ratio_peak.min(), 3)
        Result_Loc['PostPeak_Recent_Neg4_RecovDate'] = Restore_Date
        Result_Loc['PostPeak_Recent_Neg4_Recov2Now_LastDays'] = len(stk_data.loc[Restore_Date:]) - 1

    Result_Loc['PostSecStart_Over7Num'] = sum(stk_ratio.loc[Section_StartDate:] >= 7)
    Result_Loc['PostSecStart_HighOver7Num'] = sum(stk_high_ratio.loc[Section_StartDate:] >= 7)

    # 回档状态
    # PreSec_PeakDate = max(
    #     stk_data.loc[section_rise_pre['start_date'].iloc[-1]:Section_StartDate, 'high'].idxmax(),
    #     stk_data.loc[section_rise_pre['start_date'].iloc[-1]:Section_StartDate, 'close'].idxmax()
    #                       ) \
    #     if len(section_rise_pre) > 0 \
    #     else stk_data.loc[:Section_StartDate, 'close'].idxmax()
    if len(section_d.query('start_date<@Now_SecDate')) > 0:
        PreSec_PeakDate = section_d.query('start_date<@Now_SecDate')['start_date'].iloc[-1]
    elif len(stk_data.loc[turn_date:Now_SecDate]) > 0:
        PreSec_PeakDate = stk_data.loc[turn_date:Now_SecDate, 'close'].idxmax()
    else:
        PreSec_PeakDate = Section_PeakDate
    if len(section_rise_pre.query('start_date<@PreSec_PeakDate & lastdays>4')) > 0:
        drop_startdate = section_rise_pre.query('start_date<@PreSec_PeakDate & lastdays>4')['start_date'].iloc[-1]
    elif len(periodchang.query('start_date<@PreSec_PeakDate & avg_ratio>0')) > 0:
        drop_startdate = periodchang.query('start_date<@PreSec_PeakDate & avg_ratio>0')['start_date'].iloc[-1]
    else:
        drop_startdate = turn_date
    ratio_date = stk_ratio.loc[drop_startdate:PreSec_PeakDate][stk_ratio.loc[drop_startdate:PreSec_PeakDate] > 4].index[
        -1] \
        if sum(stk_ratio.loc[drop_startdate:PreSec_PeakDate] > 4) > 0 else None
    high_date = \
    stk_high_ratio.loc[drop_startdate:PreSec_PeakDate][stk_high_ratio.loc[drop_startdate:PreSec_PeakDate] > 4].index[-1] \
        if sum(stk_high_ratio.loc[drop_startdate:PreSec_PeakDate] > 4) > 0 else None
    ratio_date_peak = \
    stk_ratio.loc[drop_startdate:Section_PeakDate][stk_ratio.loc[drop_startdate:Section_PeakDate] > 4].index[-1] \
        if sum(stk_ratio.loc[drop_startdate:Section_PeakDate] > 4) > 0 else None
    high_date_peak = \
    stk_high_ratio.loc[drop_startdate:Section_PeakDate][stk_high_ratio.loc[drop_startdate:Section_PeakDate] > 4].index[
        -1] \
        if sum(stk_high_ratio.loc[drop_startdate:Section_PeakDate] > 4) > 0 else None
    if ratio_date is not None and ((high_date is not None and ratio_date >= high_date) or high_date is None):
        over4_date = ratio_date
    elif high_date is not None and ((ratio_date is not None and high_date >= ratio_date) or ratio_date is None):
        over4_date = high_date
    elif ratio_date_peak is not None and ((high_date_peak is not None and ratio_date_peak >= high_date_peak)
                                          or high_date_peak is None):
        over4_date = ratio_date_peak
        PreSec_PeakDate = Section_PeakDate
    elif high_date_peak is not None and ((ratio_date_peak is not None and high_date_peak >= ratio_date_peak)
                                         or ratio_date_peak is None):
        over4_date = high_date_peak
        PreSec_PeakDate = Section_PeakDate
    else:
        over4_date = None

    postmax_min_date = stk_data.loc[PreSec_PeakDate:, 'low'].idxmin()
    lim_price = min(min(stk_data.loc[over4_date, 'open'], stk_data.loc[over4_date, 'close']),
                    stk_data.loc[over4_date, 'pre_close']) \
        if over4_date is not None else None
    if over4_date is not None and len(stk_data.query('low<=@lim_price & trade_date>@over4_date')) > 0:
        drop_date = stk_data.query('low<=@lim_price & trade_date>@over4_date')['trade_date'].iloc[0]
    elif stk_data.loc[Section_StartDate:, 'high'].max() > stk_data.loc[PreSec_PeakDate, 'close']:
        drop_date = stk_data.loc[PreSec_PeakDate:, 'close'].idxmin()
    else:
        drop_date = None

    if over4_date is not None and drop_date is not None:
        Result_Loc['PostPeak_GapCovDays'] = len(stk_data.loc[PreSec_PeakDate:drop_date]) - 1
        Result_Loc['PrePeak_Over4_Date'] = over4_date
        Result_Loc['Convex_Break_DropDate'] = drop_date
        Sec_PeakDate = stk_data.loc[:PreSec_PeakDate].query('close>open').index[-1] \
            if len(stk_data.loc[:PreSec_PeakDate].query('close>open')) > 0 else PreSec_PeakDate
        Result_Loc['GapRatio2Sec'] = round(
            (stk_data.loc[over4_date:, 'low'].min() / stk_data.loc[Sec_PeakDate, 'open'] - 1) * 100, 3)
        Result_Loc['PostPeak_GapLastDays'] = len(stk_data.loc[PreSec_PeakDate:postmax_min_date]) - 1
        Result_Loc['PostPeak_Gap2NowDays'] = len(stk_data.loc[postmax_min_date:])
    Result_Loc['PostPeak_Gap2Peak_Ratio'] = round((stk_data.loc[Section_PeakDate:, 'low'].min() / stk_data.loc[
        Section_PeakDate, 'open'] - 1) * 100, 3)

    # 止损价格及风险收益比
    Result_Loc['Stop_Lose_Price'] = round(stk_data.loc[Section_StartDate, 'low'], 3)
    Result_Loc['Return_Risk_Ratio'] = round(Result_Loc['Target_Ratio'] / (
            abs(Result_Loc['Stop_Lose_Price'] / stk_data['close'].iloc[-1] - 1) * 100), 3) \
        if abs(Result_Loc['Stop_Lose_Price'] / stk_data['close'].iloc[-1] - 1) != 0 \
        else np.nan

    # 计算Section_StartDate前的区段状态及Section_PeakDate至Secttion_StartDate的状态

    section_bfpeak = section_rise_origin.query('end_date<@Section_StartDate') \
        if len(section_rise_origin.query('end_date<@Section_StartDate')) > 0 \
        else None
    # if section_bfpeak is not None:
    #     Result_Loc['Bf_PreSec_LastDays'] = section_bfpeak['lastdays'].iloc[-1]
    #     Result_Loc['Bf_PreSec_SumRatio'] = round(section_bfpeak['sumratio'].iloc[-1], 3)
    #     Result_Loc['Bf_PreSec_AvgRatio'] = round(section_bfpeak['avgratio'].iloc[-1], 3)
    #     Result_Loc['Bf_PreSec_ExtreRatio'] = round(section_bfpeak['extre_ratio'].iloc[-1], 3)
    #     Result_Loc['Bf_PreSec_AvgTurnover'] = section_bfpeak['avg_turnover'].iloc[-1]
    #     Result_Loc['Bf_PreSec_MaxTurnover'] = section_bfpeak['max_turnover'].iloc[-1]

    section_d_postpeak = section_d.query('start_date<=@Section_StartDate') \
        if len(section_d.query('start_date<=@Section_StartDate')) > 0 \
        else section_drop.query('start_date<=@Section_StartDate')

    if len(section_d_postpeak) > 0 \
            and (section_bfpeak is None
                 or section_d_postpeak['end_date'].iloc[-1] > section_bfpeak['end_date'].iloc[-1]):
        Result_Loc['PreSec_LastDays'] = len(stk_data.loc[section_d_postpeak['start_date'].iloc[-1]:Section_StartDate]
                                            ) - 1
        Result_Loc['PreSec_SumRatio'] = round((stk_data.loc[Section_StartDate, 'close'] /
                                               stk_data.loc[
                                                   section_d_postpeak['start_date'].iloc[-1], 'close'] - 1) * 100, 3)
        Result_Loc['PreSec_AvgRatio'] = round(Result_Loc['PreSec_SumRatio'] / Result_Loc['PreSec_LastDays'], 3)
        Result_Loc['PreSec_ExtreRatio'] = section_d_postpeak['extre_ratio'].iloc[-1]
        Result_Loc['PreSec_AvgTurnover'] = section_d_postpeak['avg_turnover'].iloc[-1]
        Result_Loc['PreSec_Adverse2Trend'] = section_d_postpeak['adverse2trend_sum'].iloc[-1]
        Result_Loc['PreSec_Und2ContiDays'] = section_d_postpeak['und2_contidays'].iloc[-1]

    if len(section_rise_origin.query('end_date<@Now_SecDate')) > 0:
        section_bfpeak = section_rise_origin.query('end_date<@Now_SecDate')
    else:
        section_bfpeak = pd.DataFrame()

    if len(section_bfpeak) > 0:
        Result_Loc['Pre_PreNow_Sec_LastDays'] = section_bfpeak['lastdays'].iloc[-1]
        Result_Loc['Pre_PreNow_Sec_SumRatio'] = round(section_bfpeak['sumratio'].iloc[-1], 3)
        Result_Loc['Pre_PreNow_Sec_AvgRatio'] = round(section_bfpeak['avgratio'].iloc[-1], 3)
        Result_Loc['Pre_PreNow_Sec_ExtreRatio'] = round(section_bfpeak['extre_ratio'].iloc[-1], 3)
        pre_prenow_sec_startdate = section_bfpeak['start_date'].iloc[-1]
        Result_Loc['Pre_PreNow_Sec_Over7Num'] = sum(stk_ratio.loc[pre_prenow_sec_startdate:PreNow_SecDate] >= 7)

    # aftpeak_days = len(stk_data.loc[Section_PeakDate:Section_StartDate]) - 1
    # aftpeak_sum = round((min(stk_data.loc[Section_StartDate, 'pre_close'], stk_data.loc[Section_StartDate, 'close'])
    #                      / stk_data.loc[Section_PeakDate, 'close'] - 1) * 100, 3)
    # aftpeak_avg = round(aftpeak_sum / aftpeak_days, 3) if aftpeak_days != 0 else 0
    # aftpeak_extre = stk_ratio.loc[Section_PeakDate:Section_StartDate].min()
    #
    # Result_Loc['Aft_Peak_Sec_Lastdays'] = aftpeak_days
    # Result_Loc['Aft_Peak_Sec_SumRatio'] = aftpeak_sum
    # Result_Loc['Aft_Peak_Sec_AvgRatio'] = aftpeak_avg
    # Result_Loc['Aft_Peak_Sec_ExtreRatio'] = round(aftpeak_extre, 3)
    # Result_Loc['Aft_Peak_Sec_AvgTurnover'] = round(stk_data.loc[Section_PeakDate:Section_StartDate, 'turnover'].mean(), 3)
    # Result_Loc['Aft_Peak_Sec_MaxTurnover'] = round(stk_data.loc[Section_PeakDate:Section_StartDate, 'turnover'].max(),
    #                                                3)
    stk_ratio_adj = stk_ratio.loc[:Section_StartDate]
    Result_Loc['Bf_SecStart_5D_MinRatio'] = round(stk_ratio_adj.loc[
                                                  max(PreSec_PeakDate,
                                                      stk_ratio_adj.index[-min(5, len(stk_ratio_adj))]):].min(), 3)

    calnum = 3
    mean_ratio, max_ratio, cls_open_ratio, sec_ratio = cal_recent_stat(stk_data, num=calnum)

    stk_highlow_ratio = (stk_data['high'] /
                         stk_data.apply(lambda fn: min(fn['low'], fn['pre_close']), axis=1) - 1) * 100
    Result_Loc['Recent_MeanRatio'] = mean_ratio
    Result_Loc['Recent_MaxRatio'] = max_ratio
    Result_Loc['Recent_Mean_ClsOpenRatio'] = cls_open_ratio
    aftsec_num = len(stk_data.loc[Section_StartDate:])
    section_d_bf_sec = section_d.query('start_date<@Section_StartDate')

    if sum(stk_highlow_ratio.loc[Section_StartDate:] >= 4) > 0:
        Last_Over4Date = stk_highlow_ratio[stk_highlow_ratio >= 4].index[-1] \
            if sum(stk_highlow_ratio.loc[Section_StartDate:] >= 4) > 0 else stk_highlow_ratio.index[-1]
        Result_Loc['Recent_HLR_Date'] = Last_Over4Date \
            if sum(stk_highlow_ratio.loc[Section_StartDate:] >= 4) > 0 else None
        Result_Loc['PreNow_MeanHLRatio'] = round(
            stk_highlow_ratio.loc[Section_StartDate:Last_Over4Date].iloc[:-1].mean(), 3) \
            if len(stk_highlow_ratio.loc[Section_StartDate:]) > 1 else None
        Result_Loc['PreNow_MHLR_Over4Num'] = sum(stk_highlow_ratio.loc[Section_StartDate:] >= 4)
        Result_Loc['Recent_HLRatio_Chg'] = round(
            stk_highlow_ratio.loc[Last_Over4Date] / Result_Loc['PreNow_MeanHLRatio'], 3) \
            if Result_Loc['PreNow_MeanHLRatio'] is not None and Result_Loc['PreNow_MeanHLRatio'] != 0 else 0

    # 20221214新增指标
    Result_Loc['TurnDiff_Period_AvgRatio_Div'] = round(abs(
        periodchang_up['avg_ratio'].iloc[0] / dropperiod_preturn['avg_ratio'].iloc[-1]), 3) \
        if len(periodchang_up) > 0 and len(dropperiod_preturn) > 0 \
        else 0
    Result_Loc['TurnDiff_Period_LastDays_Div'] = round(
        periodchang_up['last_days'].iloc[0] / dropperiod_preturn['last_days'].iloc[-1], 3) \
        if len(periodchang_up) > 0 and len(dropperiod_preturn) > 0 \
        else 0
    Result_Loc['TurnDiff_Period_AvgTurnover_Div'] = round(
        periodchang_up['avg_turnover'].iloc[0] / dropperiod_preturn['avg_turnover'].iloc[-1], 3) \
        if len(periodchang_up) > 0 and len(dropperiod_preturn) > 0 \
        else 0
    if len(section_drop) > 0 and len(section_rise_aft_turn) > 0 \
            and max(stk_data.loc[section_drop['start_date'].iloc[-1], 'close'],
                    stk_data.loc[section_drop['start_date'].iloc[-1], 'pre_close']) != stk_data.loc[turn_date, 'close']:
        Result_Loc['TurnDiff_Sec_Recov_Position'] = round(
            (stk_data.loc[section_rise_aft_turn['end_date'].iloc[0], 'close'] - stk_data.loc[turn_date, 'close']) /
            (max(stk_data.loc[section_drop['start_date'].iloc[-1], 'close'],
                 stk_data.loc[section_drop['start_date'].iloc[-1], 'pre_close']) - stk_data.loc[turn_date, 'close']), 3)
    elif len(section_drop) > 0 and max(stk_data.loc[section_drop['start_date'].iloc[-1], 'close'],
                                       stk_data.loc[section_drop['start_date'].iloc[-1], 'pre_close']) != stk_data.loc[
        turn_date, 'close']:
        Result_Loc['TurnDiff_Sec_Recov_Position'] = round(
            (stk_data.loc[Section_StartDate:, 'close'].max() - stk_data.loc[turn_date, 'close']) /
            (max(stk_data.loc[section_drop['start_date'].iloc[-1], 'close'],
                 stk_data.loc[section_drop['start_date'].iloc[-1], 'pre_close']) - stk_data.loc[turn_date, 'close']), 3)
    else:
        Result_Loc['TurnDiff_Sec_Recov_Position'] = 0
    Result_Loc['TurnDiff_Sec_LastDays_Div'] = round(
        abs(section_rise_aft_turn['lastdays'].iloc[0] / section_drop['lastdays'].iloc[-1]), 3) \
        if len(section_rise_aft_turn) > 0 and len(section_drop) > 0 else 0
    preturn_secstartclose = stk_data.loc[section_drop['start_date'].iloc[-1], 'close'] \
        if len(section_drop) > 0 else None
    preturn_breakdate = stk_data.loc[turn_date:][stk_data.loc[turn_date:, 'close'] > preturn_secstartclose].index[0] \
        if preturn_secstartclose is not None and len(stk_data.loc[turn_date:]) > 0 \
           and len(stk_data.loc[turn_date:][stk_data.loc[turn_date:, 'close'] > preturn_secstartclose]) > 0 \
        else None
    Result_Loc['TurnDiff_Recoved_Sec_Num'] = len(section_rise_aft_turn.query('end_date<=@preturn_breakdate')) + \
                                             len(section_d_aft_turn.query('end_date<=@preturn_breakdate')) \
        if preturn_breakdate is not None else 0
    Result_Loc['TurnDiff_Recoved_Sec_LastDays_Div'] = round(
        len(stk_data.loc[turn_date:preturn_breakdate]) / section_drop['lastdays'].iloc[-1], 3) \
        if preturn_breakdate is not None and len(section_drop) > 0 else 0

    BMA_ratio, _, _, _ = cal_movingavg_stat(stk_data.loc[turn_date:PostTurn_MaxDate])
    Result_Loc['PostTurn_BMA_Proportion'] = BMA_ratio

    if len(section_d.query('end_date<@Now_SecDate')) > 0:
        presec_start = section_d.query('end_date<@Now_SecDate')['end_date'].iloc[-1]
    elif len(section_drop.query('end_date<@Now_SecDate')) > 0:
        presec_start = section_drop.query('end_date<@Now_SecDate')['end_date'].iloc[-1]
    else:
        presec_start = None
    Result_Loc['SecValley_GapRatio'] = round(
        (stk_data.loc[Now_SecDate, 'close'] / stk_data.loc[presec_start, 'close'] - 1) * 100, 3) \
        if presec_start is not None else 0

    if len(section_d.query('start_date<@Section_StartDate')) > 0:
        if len(section_rise_origin.query('end_date<@Section_StartDate')) > 0:
            PreSec_StartDate = stk_data.loc[
                               section_rise_origin.query('end_date<@Section_StartDate')['start_date'].iloc[-1]:
                               Section_StartDate, 'close'].idxmax()
        else:
            PreSec_StartDate = section_d.query('start_date<@Section_StartDate')['start_date'].iloc[-1]
        PreSec_LastDays = section_d.query('start_date<@Section_StartDate')['lastdays'].iloc[-1]
        PreSec_AvgRatio = section_d.query('start_date<@Section_StartDate')['avgratio'].iloc[-1]
        PreSec_AvgTurnover = section_d.query('start_date<@Section_StartDate')['avg_turnover'].iloc[-1]
    elif len(section_drop.query('start_date<@Section_StartDate')) > 0:
        PreSec_StartDate = section_drop.query('start_date<@Section_StartDate')['start_date'].iloc[-1]
        PreSec_LastDays = section_drop.query('start_date<@Section_StartDate')['lastdays'].iloc[-1]
        PreSec_AvgRatio = section_drop.query('start_date<@Section_StartDate')['avgratio'].iloc[-1]
        PreSec_AvgTurnover = section_drop.query('start_date<@Section_StartDate')['avg_turnover'].iloc[-1]
    else:
        PreSec_StartDate = None
        PreSec_LastDays = None
        PreSec_AvgRatio = None
        PreSec_AvgTurnover = None

    # postbreak_maxdate = PeriodDrop_Startdates[lnum]
    postbreak_maxdate = PostSecStart_PeakDate
    postbreak_maxclose = stk_data.loc[postbreak_maxdate, 'close']
    Result_Loc['PreSec_StartDate'] = PreSec_StartDate
    Result_Loc['SecDiff_Sec_Recov_Position'] = round(
        (stk_data.loc[Section_StartDate:, 'close'].max() - stk_data.loc[Section_StartDate, 'close'])
        / (max(stk_data.loc[PreSec_StartDate, 'close'], stk_data.loc[PreSec_StartDate, 'pre_close'])
           - stk_data.loc[Section_StartDate, 'close']), 3) \
        if max(stk_data.loc[PreSec_StartDate, 'close'], stk_data.loc[PreSec_StartDate, 'pre_close']) \
           != stk_data.loc[Section_StartDate, 'close'] \
        else 0
    Result_Loc['SecDiff_Sec_LastDays_Div'] = round(
        section_rise_origin.query('end_date>@Section_StartDate')['lastdays'].iloc[0] / PreSec_LastDays, 3) \
        if len(section_rise_origin.query('end_date>@Section_StartDate')) > 0 and PreSec_LastDays is not None \
        else 0
    Result_Loc['SecDiff_Sec_AvgRatio_Div'] = round(abs(
        section_rise_origin.query('end_date>@Section_StartDate')['avgratio'].iloc[0] / PreSec_AvgRatio), 3) \
        if len(section_rise_origin.query('end_date>@Section_StartDate')) > 0 and PreSec_AvgRatio is not None \
        else 0
    Result_Loc['SecDiff_Sec_AvgTurnover_Div'] = round(
        section_rise_origin.query('end_date>@Section_StartDate')['avg_turnover'].iloc[0] / PreSec_AvgTurnover, 3) \
        if len(section_rise_origin.query('end_date>@Section_StartDate')) > 0 and PreSec_AvgTurnover is not None \
        else 0
    if postbreak_maxdate is not None:
        PreBreak_Date = stk_data.loc[:Section_StartDate].query('close>@postbreak_maxclose').index[-1] \
            if len(stk_data.loc[:Section_StartDate].query('close>@postbreak_maxclose')) > 0 \
            else stk_data.index[0]
        Result_Loc['CoverDays_BreakDate_RatioBand'] = round(
            (postbreak_maxclose / stk_data.loc[PreBreak_Date:, 'close'].min() - 1) * 100, 3)
    else:
        Result_Loc['CoverDays_BreakDate_RatioBand'] = 0

    PostSec_PeakClose = stk_data.loc[Section_StartDate:, 'close'].max()
    Up_bf_LS = periodchang[(periodchang['end_date'] < PreSec_StartDate) & (periodchang['avg_ratio'] > 0)]
    Peak_Presure_Dates = stk_data.loc[Up_bf_LS['end_date']][
        (stk_data.loc[Up_bf_LS['end_date'], 'close'] >= PostSec_PeakClose * 0.97)
        & (stk_data.loc[Up_bf_LS['end_date'], 'close'] <= PostSec_PeakClose * 1.1)].index.tolist() \
        if len(Up_bf_LS) > 0 else []
    if len(Peak_Presure_Dates) > 2:
        pnum = -2
        period_middle = Up_bf_LS[(Up_bf_LS['end_date'] > Peak_Presure_Dates[pnum])
                                 & (Up_bf_LS['end_date'] < Peak_Presure_Dates[pnum + 1])]
        while len(Peak_Presure_Dates) > 2 \
                and abs(pnum) < len(Peak_Presure_Dates) \
                and (len(period_middle) == 0
                     or sum(stk_data.loc[period_middle['end_date'], 'close'] > PostSec_PeakClose * 1.1) == 0):
            pnum -= 1
            period_middle = Up_bf_LS[(Up_bf_LS['end_date'] > Peak_Presure_Dates[pnum])
                                     & (Up_bf_LS['end_date'] < Peak_Presure_Dates[pnum + 1])]
        Peak_Presure_Dates = Peak_Presure_Dates[pnum + 1:]
    Valley_Presure_Dates = stk_data.loc[Up_bf_LS['start_date']][
        (stk_data.loc[Up_bf_LS['start_date'], 'close'] >= PostSec_PeakClose * 0.97)
        & (stk_data.loc[Up_bf_LS['start_date'], 'close'] <= PostSec_PeakClose * 1.1)].index.tolist() \
        if len(Up_bf_LS) > 0 else []
    Peak_Last = len(stk_data.loc[Peak_Presure_Dates[0]:]) \
        if len(Peak_Presure_Dates) > 0 else 0
    Valley_Last = len(stk_data.loc[Valley_Presure_Dates[0]:]) \
        if len(Valley_Presure_Dates) > 0 else 0
    Result_Loc['Peak_Pres_Num'] = len(Peak_Presure_Dates)
    Result_Loc['Peak_Pres_Lastdays'] = Peak_Last
    Result_Loc['Valley_Pres_Num'] = len(Valley_Presure_Dates)
    Result_Loc['Valley_Pres_Lastdays'] = Valley_Last
    Peak_Pres_MaxPrice = stk_data.loc[Peak_Presure_Dates[0]:, 'close'].max() if len(Peak_Presure_Dates) > 0 else 0
    Peak_Pres_MinPrice = stk_data.loc[Peak_Presure_Dates[0]:, 'close'].min() if len(Peak_Presure_Dates) > 0 else 0
    Peak_Pres_MinPriceDate = stk_data.loc[Peak_Presure_Dates[0]:, 'close'].idxmin() \
        if len(Peak_Presure_Dates) > 0 else '-'
    Valley_Pres_MaxPrice = stk_data.loc[Valley_Presure_Dates, 'close'].max() if len(Valley_Presure_Dates) > 0 else 0
    Result_Loc['Peak_Pres_MaxPrice'] = round(Peak_Pres_MaxPrice, 3)
    Result_Loc['Valley_Pres_MaxPrice'] = round(Valley_Pres_MaxPrice, 3)
    Result_Loc['Peak_Pres_MaxPrice_Ratio'] = round(
        (Peak_Pres_MaxPrice / PostSec_PeakClose - 1) * 100, 3) if PostSec_PeakClose > 0 else 0
    Result_Loc['Peak_Pres_RatioBand'] = round(
        (Peak_Pres_MaxPrice / Peak_Pres_MinPrice - 1) * 100, 3) if Peak_Pres_MinPrice > 0 else 0
    Result_Loc['Valley_Pres_MaxPrice_Ratio'] = round(
        (Valley_Pres_MaxPrice / PostSec_PeakClose - 1) * 100, 3) if PostSec_PeakClose > 0 else 0
    if Peak_Pres_MinPriceDate != '-':
        Result_Loc['Peak_Pres_AftBottom_Lastdays'] = len(stk_data.loc[Peak_Pres_MinPriceDate:]) - 1
        Result_Loc['Peak_Pres_AftBottom_DaysProp'] = round(
            Result_Loc['Peak_Pres_AftBottom_Lastdays'] / Result_Loc['Peak_Pres_Lastdays'], 3) if Result_Loc['Peak_Pres_Lastdays'] > 0 else 0
    else:
        Result_Loc['Peak_Pres_AftBottom_Lastdays'] = 0
        Result_Loc['Peak_Pres_AftBottom_DaysProp'] = 0
    
    

    def se_ratio(data):
        se_threshold = 3
        return 1 if abs((data.iloc[-1] / data.iloc[0] - 1) * 100) < se_threshold else 0

    def maxmin_ratio(data):
        maxmin_threshold = 5
        return 1 if abs((data.max() / data.min() - 1) * 100) < maxmin_threshold else 0

    rolling_window = 4
    roll_se = stk_data.loc[PreSec_StartDate:, 'close'].rolling(rolling_window).apply(se_ratio)
    roll_maxmin = stk_data.loc[PreSec_StartDate:, 'close'].rolling(rolling_window).apply(maxmin_ratio)
    roll_data = pd.concat([roll_se, roll_maxmin], axis=1)
    roll_data.columns = ['se', 'maxmin']
    roll_data['judge'] = roll_data.apply(lambda fn: 1 if fn['se'] == 1 and fn['maxmin'] == 1 else 0, axis=1)
    roll_data['consecutive'] = roll_data.judge.groupby(
        (roll_data.judge != roll_data.judge.shift()).cumsum()).transform('size') * roll_data.judge
    if len(roll_data.query('consecutive>0')) > 0:
        Result_Loc['Narrow_LastDays'] = roll_data.query('consecutive>0')['consecutive'].iloc[-1] + (rolling_window - 2)
        consecutive_enddate = roll_data.query('consecutive>0').index[-1]
        Result_Loc['Narrow_E2N_Days'] = len(roll_data.loc[consecutive_enddate:]) - 1
        Result_Loc['Narrow_E2N_Return'] = round(
            (stk_data['close'].iloc[-1] / stk_data.loc[consecutive_enddate, 'close'] - 1) * 100, 3)
        Result_Loc['Narrow_E2N_MaxReturn'] = round(
            (stk_data.loc[consecutive_enddate:, 'close'].max() / stk_data.loc[consecutive_enddate, 'close'] - 1) * 100,
            3)
        Result_Loc['Narrow_E2N_MinReturn'] = round(
            (stk_data.loc[consecutive_enddate:, 'close'].min() / stk_data.loc[consecutive_enddate, 'close'] - 1) * 100,
            3)
    else:
        Result_Loc['Narrow_LastDays'], Result_Loc['Narrow_E2N_Days'], Result_Loc['Narrow_E2N_Return'], \
            Result_Loc['Narrow_E2N_MaxReturn'], Result_Loc['Narrow_E2N_MinReturn'] = 0, 0, 0, 0, 0

    if Section_StartDate < PreNow_PeakDate:
        mean_ratio_bf, _, cls_open_ratio_bf, _ = cal_recent_stat(
            stk_data.loc[Section_StartDate:PreNow_PeakDate], num=calnum)
    elif turn_date < PreNow_PeakDate:
        mean_ratio_bf, _, cls_open_ratio_bf, _ = cal_recent_stat(
            stk_data.loc[turn_date:PreNow_PeakDate], num=calnum)
    else:
        mean_ratio_bf, _, cls_open_ratio_bf, _ = cal_recent_stat(
            stk_data.loc[:PreNow_PeakDate], num=calnum)
    prenowpeak_startdate = section_rise_origin.query('start_date<@PreNow_PeakDate')['start_date'].iloc[-1] \
        if len(section_rise_origin.query('start_date<@PreNow_PeakDate')) > 0 else None
    prenowpeak_movavg = stk_movavg.loc[prenowpeak_startdate:PreNow_PeakDate]
    Result_Loc['PreNow_Rise_Recent_MeanRatio'] = mean_ratio_bf

    Result_Loc['PreNow_Rise_Recent_MaxRatio'] = round(prenowpeak_movavg.max(), 2)
    Result_Loc['PreNow_Rise_Recent_ClsOpenRatio'] = cls_open_ratio_bf
    Result_Loc['PreNowPeak2Now_AvgTurnover'] = round(stk_data.loc[PreNow_PeakDate:Now_SecDate, 'turnover'].mean(), 3)
    Result_Loc['PreNowPeak2Now_MaxTurnover'] = round(stk_data.loc[PreNow_PeakDate:Now_SecDate, 'turnover'].max(), 3)

    Result_Loc['PreNowPeak2Now_LastDays'] = len(stk_data.loc[PreNow_PeakDate:]) - 1
    
    Result_Loc['PreNowPeak2NowSec_LastDays'] = len(stk_data.loc[PreNow_PeakDate:Now_SecDate]) - 1
    
    Result_Loc['PreNowPeak2Now_SumRatio'] = round(
        (stk_data.loc[Now_SecDate, 'close'] /
         max(stk_data.loc[PreNow_PeakDate, 'close'], stk_data.loc[PreNow_PeakDate, 'pre_close']) - 1) * 100, 3)
    Result_Loc['PreNowPeak2Now_AvgRatio'] = round(
        Result_Loc['PreNowPeak2Now_SumRatio'] / Result_Loc['PreNowPeak2NowSec_LastDays'], 3) \
        if Result_Loc['PreNowPeak2NowSec_LastDays'] != 0 else 0
    postpeak_mindaily_date = stk_ratio.loc[Section_PeakDate:].idxmin()
    Result_Loc['PostSecPeak_MinDaily2Now_LastDays'] = len(stk_ratio.loc[postpeak_mindaily_date:]) - 1

    if len(section_rise_origin.query('start_date<@PreNow_PeakDate')) > 0:
        day_list_bf_PreSec = day_list.loc[
                             section_rise_origin.query('start_date<@PreNow_PeakDate')
                             ['start_date'].iloc[-1]:PreNow_PeakDate]
        Result_Loc['PreNow_Rise_MaxContiSum'], Result_Loc['PreNow_Rise_MaxContiAvg'], \
            Result_Loc['PreNow_Rise_MaxContiDays'], _ = cal_maxcontisum(stk_data, day_list_bf_PreSec)
        # Result_Loc['PreNowSec_LastDays'] = section_rise_origin.query('start_date<@PreNow_SecDate')['lastdays'].iloc[-1]
        # Result_Loc['PreNowSec_MaxClsOpenRatioRatio'] = round(section_rise_origin.query(
        #     'start_date<@PreNow_SecDate')['extre_ratio'].iloc[-1], 3)
    Result_Loc['Pre_PreNowSec_Und2ContiDays'] = section_rise_origin.query(
        'start_date<@PreNow_SecDate')['und2_contidays'].iloc[-1] \
        if len(section_rise_origin.query('start_date<@PreNow_SecDate')) > 0 \
        else -1
    Result_Loc['Pre_PreNowSec_Adverse2Trend'] = section_rise_origin.query(
        'start_date<@PreNow_SecDate')['adverse2trend_sum'].iloc[-1] \
        if len(section_rise_origin.query('start_date<@PreNow_SecDate')) > 0 else -1

    # 20221220新增指标
    if turn_date == Section_StartDate:
        day_list_aftturn = day_list.loc[turn_date:]
    else:
        day_list_aftturn = day_list.loc[turn_date:Section_StartDate]
    if len(day_list_aftturn) > 0 and len(day_list_aftturn.query('day_state=="涨"')) > 0:
        Result_Loc['PostTurn_MaxContiSum'], Result_Loc['PostTurn_MaxContiAvg'], \
            Result_Loc['PostTurn_MaxContiDays'], _ = cal_maxcontisum(stk_data, day_list_aftturn)
    else:
        Result_Loc['PostTurn_MaxContiSum'], Result_Loc['PostTurn_MaxContiAvg'], \
            Result_Loc['PostTurn_MaxContiDays'] = 0, 0, 0

    # 20221229新增指标
    if PreSec_StartDate is not None:
        coef_presec = coef_regress(stk_data.loc[PreSec_StartDate:Section_StartDate, 'close'], mode='err')
        std_bias_presec = round(abs(stk_data.loc[Section_StartDate, 'close'] / max(stk_data.loc[
                                                                                       PreSec_StartDate, 'close'],
                                                                                   stk_data.loc[
                                                                                       PreSec_StartDate, 'pre_close']) - 1) * 100 /
                                coef_presec[4], 3) \
            if coef_presec[4] != 0 else 0
        Result_Loc['PreSec_Reg_R2'] = round(coef_presec[0], 3)
        Result_Loc['PreSec_Reg_Sum2Std'] = round(std_bias_presec, 3)

    # 20230104新增指标
    Result_Loc['PostTurn_MaxTurnover'] = round(stk_data.loc[turn_date:, 'turnover'].max(), 3)
    Result_Loc['PostTurn_AvgTurnover'] = round(stk_data.loc[turn_date:, 'turnover'].mean(), 3)
    Result_Loc['PreTurn_MaxTurnover'] = round(
        stk_data.loc[dropperiod_preturn['start_date'].iloc[-1]:turn_date, 'turnover'].max(), 3) \
        if len(dropperiod_preturn) > 0 else None
    Result_Loc['PostBottom_MaxTurnover'] = round(stk_data.loc[Bottom_Date:, 'turnover'].max(), 3)
    Result_Loc['PostSecStart_MaxTurnover'] = round(stk_data.loc[Section_StartDate:, 'turnover'].max(), 3)
    Result_Loc['PostSecStart_MaxTurnover_Date'] = stk_data.loc[Section_StartDate:, 'turnover'].idxmax()
    Result_Loc['PreSec_MaxTurnover'] = round(
        stk_data.loc[section_d_postpeak['start_date'].iloc[-1]:Section_StartDate, 'turnover'].max(), 3) \
        if len(section_d_postpeak) > 0 else None
    # postsec2now_idxmax = stk_data.loc[Section_StartDate:Now_SecDate, 'close'].idxmax() \
    #     if Section_StartDate < Now_SecDate else stk_data.index[-1]
    Result_Loc['PostSecStart_RiseSecNum'] = len(section_rise_origin.query('end_date>@Section_StartDate'))

    # 20230110新增指标
    to_threshold = 3
    threshold = 9
    # Result_Loc['PostTurn_TO_Over10Num'] = len(stk_data.loc[turn_date:].query('turnover>@to_threshold'))
    # turn_threshold = min(stk_data.loc[turn_date:].query('turnover<@threshold')['turnover'].mean() * to_threshold, threshold)
    # postturn_avgturnover = min(
    #     stk_data.loc[turn_date:].query('turnover<@turn_threshold')['turnover'].mean() * to_threshold, threshold)
    turn_threshold = stk_data.loc[turn_date:, 'turnover'].quantile(0.5)
    postturn_avgturnover = min(turn_threshold * to_threshold, threshold)
    Result_Loc['PostTurn_TO_Over10Num'] = len(stk_data.loc[turn_date:].query('turnover>@postturn_avgturnover'))
    # Result_Loc['PostSecStart_TO_Over10Num'] = len(stk_data.loc[Section_StartDate:].query('turnover>@to_threshold'))
    # sec_threshold = min(
    #     stk_data.loc[Section_StartDate:].query('turnover<@threshold')['turnover'].mean() * to_threshold, threshold)
    # postsec_avgturnover = min(
    #     stk_data.loc[Section_StartDate:].query('turnover<@sec_threshold')['turnover'].mean() * to_threshold, threshold)
    sec_threshold = stk_data.loc[Section_StartDate:, 'turnover'].quantile(0.5)
    postsec_avgturnover = min(sec_threshold * to_threshold, threshold)

    Result_Loc['PostSecStart_TO_Over10Num'] = len(
        stk_data.loc[Section_StartDate:].query('turnover>@postsec_avgturnover'))
    Result_Loc['PostSecStart_Avg_LastDays'] = round(
        section_rise_origin.query('end_date>@Section_StartDate')['lastdays'].mean(), 3)
    # bottom_threshold = min(
    #     stk_data.loc[Bottom_Date:].query('turnover<@threshold')['turnover'].mean() * to_threshold, threshold)
    # postbottom_avgturnover = min(
    #     stk_data.loc[Bottom_Date:].query('turnover<@bottom_threshold')['turnover'].mean() * to_threshold, threshold)
    bottom_threshold = stk_data.loc[Bottom_Date:, 'turnover'].quantile(0.5)
    postbottom_avgturnover = min(bottom_threshold * to_threshold, threshold)
    Result_Loc['PostBottom_TO_Over10Num'] = len(stk_data.loc[Bottom_Date:].query('turnover>@postbottom_avgturnover'))

    # 20230103新增指标
    Result_Loc['Now_HighLow_Ratio'] = round(stk_highlow_ratio.iloc[-1], 3)
    Result_Loc['Now_Turnover'] = round(stk_data['turnover'].iloc[-1], 3)
    if Section_StartDate != Now_SecDate:
        cal_dates = stk_ratio[Section_StartDate:][stk_ratio[Section_StartDate:] <= 9.5].index
        bfcal_start = Section_StartDate
        if Result_Loc['PostTurn_RiseRatio'] > 20 \
                and Result_Loc['PostTurn_MaxTurnover'] / Result_Loc['PostTurn_AvgTurnover'] > 3 \
                and len(stk_data.loc[Section_StartDate:].query('turnover>@postsec_avgturnover')) > 5:
            PostSec_FirstDate = stk_data.loc[Section_StartDate:].query('turnover>@postsec_avgturnover').index[0]
            cal_dates = stk_ratio[PostSec_FirstDate:][stk_ratio[PostSec_FirstDate:] <= 9.5].index
    elif Section_StartDate == Now_SecDate and turn_date != Section_StartDate:
        cal_dates = stk_ratio[turn_date:][stk_ratio[turn_date:] <= 9.5].index
        bfcal_start = turn_date
        if Result_Loc['PostTurn_RiseRatio'] > 20 \
                and Result_Loc['PostTurn_MaxTurnover'] / Result_Loc['PostTurn_AvgTurnover'] > 3 \
                and len(stk_data.loc[turn_date:].query('turnover>@postturn_avgturnover')) > 5:
            PostTurn_FirstDate = stk_data.loc[turn_date:].query('turnover>@postturn_avgturnover').index[0]
            cal_dates = stk_ratio[PostTurn_FirstDate:][stk_ratio[PostTurn_FirstDate:] <= 9.5].index
    else:
        cal_dates = stk_ratio[Bottom_Date:][stk_ratio[Bottom_Date:] <= 9.5].index
        bfcal_start = Bottom_Date
        if Result_Loc['PostBottom_RiseRatio'] > 20 \
                and len(stk_data.loc[turn_date:].query('turnover>@postbottom_avgturnover')) > 5:
            PostBottom_FirstDate = stk_data.loc[turn_date:].query('turnover>@postbottom_avgturnover').index[0]
            cal_dates = stk_ratio[PostBottom_FirstDate:][stk_ratio[PostBottom_FirstDate:] <= 9.5].index
    # elif Section_StartDate == Now_SecDate and turn_date == Section_StartDate and Bottom_Date != turn_date:
    #     cal_dates = stk_ratio[Bottom_Date:][stk_ratio[Bottom_Date:] <= 9.5].index
    # else:
    #     cal_dates = stk_ratio[PreTurn_PeakDate:][stk_ratio[PreTurn_PeakDate:] <= 9.5].index
    if len(cal_dates) > 1:
        bf_cal_date = stk_data.loc[:cal_dates[0]].index[-1]
        bf_cal_avgturnover = stk_data.loc[bfcal_start:bf_cal_date, 'turnover'].mean()
        Result_Loc['BfRise_Turnover_Threshold'] = round(bf_cal_avgturnover, 3)
        turnover_quntl = stk_data.loc[cal_dates, 'turnover'].quantile(0.1)
        turnover_rank = stk_data.loc[cal_dates, 'turnover'].rank()
        Result_Loc['Now_Turnover_Signal'] = round(stk_data['turnover'].iloc[-2:].min() / turnover_quntl, 3)
        Result_Loc['Now_Turnover_Quntl'] = round(turnover_rank.iloc[-2:].min() / len(turnover_rank), 3) \
            if len(turnover_rank) > 1 else None
        Result_Loc['Now_Turnover_Rank'] = turnover_rank.iloc[-2:].min() \
            if len(turnover_rank) > 1 else None
        Result_Loc['Now_Turnover_MinGap'] = stk_data['turnover'].iloc[-2:].min() - math.ceil(bf_cal_avgturnover) \
            if bf_cal_avgturnover is not None else None
    Result_Loc['Pre_SecStart_MaxHLRatio'] = round(stk_highlow_ratio.loc[PreSec_StartDate:Section_StartDate].max(), 3)
    Result_Loc['Post_SecStart_MaxHLRatio'] = round(
        stk_highlow_ratio.loc[Section_StartDate:stk_highlow_ratio.index[-1]].max(), 3)

    # 20230118新增指标
    postturn_to_over10_date = stk_data.loc[turn_date:].query('turnover>@postturn_avgturnover').index[0] \
        if len(stk_data.loc[turn_date:].query('turnover>@postturn_avgturnover')) > 0 else None
    postsecstart_1stto_date = stk_data.loc[Section_StartDate:].query('turnover>@postsec_avgturnover').index[0] \
        if len(stk_data.loc[Section_StartDate:].query('turnover>@postsec_avgturnover')) > 0 else None
    Result_Loc['Turn_1stTODate_Ratio'] = round(
        (stk_data.loc[postturn_to_over10_date, 'close'] / min(stk_data.loc[turn_date, 'close'],
                                                              stk_data.loc[turn_date, 'pre_close']) - 1) * 100, 3) \
        if postturn_to_over10_date is not None else None
    Result_Loc['Turn_1stTODate_LastDays'] = len(stk_data.loc[turn_date:postturn_to_over10_date]) - 1 \
        if postturn_to_over10_date is not None else None
    Result_Loc['PostTurn_1stTODate2Top_Ratio'] = round(
        (stk_data.loc[postturn_to_over10_date:, 'close'].max() /
         stk_data.loc[postturn_to_over10_date, 'close'] - 1) * 100, 3) \
        if postturn_to_over10_date is not None else None
    Result_Loc['SecStart_1stTODate_Ratio'] = round(
        (stk_data.loc[postsecstart_1stto_date, 'close'] / min(stk_data.loc[Section_StartDate, 'close'],
                                                              stk_data.loc[Section_StartDate, 'pre_close']) - 1) * 100,
        3) \
        if postsecstart_1stto_date is not None else None
    Result_Loc['SecStart_1stTODate2Now_LastDays'] = len(stk_data.loc[postsecstart_1stto_date:]) - 1 \
        if postsecstart_1stto_date is not None else None

    # 20230201新增指标
    Result_Loc['PostTurn_LastDays'] = len(stk_data.loc[turn_date:]) - 1
    # cal_endclose = stk_data.loc[PreSec_PeakDate, 'close'] \
    #     if PreSec_PeakDate > turn_date else stk_data.loc[turn_date:Now_SecDate, 'close'].max()
    Result_Loc['PostTurn_RiseAvg'] = round(
        (stk_data['close'].iloc[-1] / stk_data.loc[turn_date, 'close'] - 1) * 100 / Result_Loc['PostTurn_LastDays'], 3) \
        if len(stk_data.loc[turn_date:]) > 1 else None

    # 20230724 LastDrop_COR_Ratio调整为Section_StartDate之前的最后下跌蜡烛体高度
    LastDropSec_ClsOpenRatio_Avg = section_d_bf_sec['clsopen_ratio'].iloc[-1] \
        if len(section_d_bf_sec) > 0 else None
    day_list_bfsec = day_list.loc[:Section_StartDate].copy().reset_index(drop=False)
    day_statis = day_list_bfsec.query('day_state=="跌"').groupby(
        (day_list_bfsec.day_state.shift() != day_list_bfsec.day_state).cumsum()).trade_date.agg(['count', 'min', 'max']) \
        if len(day_list_bfsec) > 0 else None
    ContiDrop_COR_Avg = np.mean(
        abs(stk_data.loc[day_statis['min'].iloc[-1]:day_statis['max'].iloc[-1], 'close'] /
            stk_data.loc[day_statis['min'].iloc[-1]:day_statis['max'].iloc[-1], 'open'] - 1) * 100) \
        if day_statis is not None else 0
    Bf_ContiDrop_COR_Avg = np.mean(
        abs(stk_data.loc[section_d_bf_sec['start_date'].iloc[-1]:day_statis['min'].iloc[-1], 'close'] /
            stk_data.loc[section_d_bf_sec['start_date'].iloc[-1]:day_statis['min'].iloc[-1], 'open'] - 1) * 100) \
        if len(section_d_bf_sec) > 0 and section_d_bf_sec['start_date'].iloc[-1] <= day_statis['min'].iloc[-1] \
        else 10
    if len(section_d_bf_sec) > 1 \
            and min(Bf_ContiDrop_COR_Avg, section_d_bf_sec['clsopen_ratio'].iloc[-2]) != 0:
        Result_Loc['LastDrop_COR_Ratio'] = round(
            ContiDrop_COR_Avg / min(Bf_ContiDrop_COR_Avg, section_d_bf_sec['clsopen_ratio'].iloc[-2]), 3)
    elif Bf_ContiDrop_COR_Avg > 0:
        Result_Loc['LastDrop_COR_Ratio'] = ContiDrop_COR_Avg / Bf_ContiDrop_COR_Avg
    else:
        Result_Loc['LastDrop_COR_Ratio'] = 0
    Result_Loc['LastDrop_ClsOpenRatio_Avg'] = LastDropSec_ClsOpenRatio_Avg

    # 20230216 新增指标
    day_list_aftsec = day_list.loc[Section_StartDate:].iloc[1:] if len(day_list.loc[Section_StartDate:]) > 1 else pd.DataFrame()
    if len(day_list_aftsec) > 0 and len(day_list_aftsec.query('day_state=="涨"')) > 0:
        Result_Loc['PostSecStart_MaxContiSum'], Result_Loc['PostSecStart_MaxContiAvg'], \
            Result_Loc['PostSecStart_MaxContiDays'], _ = cal_maxcontisum(stk_data, day_list_aftsec)
    else:
        Result_Loc['PostSecStart_MaxContiSum'], Result_Loc['PostSecStart_MaxContiAvg'], \
            Result_Loc['PostSecStart_MaxContiDays'] = 0, 0, 0

    if len(day_list_aftsec) > 0 and len(day_list_aftsec.query('day_state=="跌"')) > 0:
        Result_Loc['PostSecStart_MaxContiDrop'], _, Result_Loc['PostSecStart_MaxContiDropDays'], _ = \
            cal_maxcontisum(stk_data, day_list_aftsec, mode='跌')
    else:
        Result_Loc['PostSecStart_MaxContiDrop'], Result_Loc['PostSecStart_MaxContiDropDays'] = 0, 0

    Result_Loc['PostSecStart_DropDayRatio_Quntl'] = round(stk_ratio.loc[Section_StartDate:][
                                                              stk_ratio.loc[Section_StartDate:] < 0].iloc[:-1].quantile(
        0.33), 3)
    Result_Loc['PostSecStart_DropDayRatio_Min'] = round(stk_ratio.loc[Section_StartDate:].iloc[:-1].min(), 3)
    Result_Loc['PreSec_DDR_Quntl'] = round(stk_ratio.loc[PreSec_StartDate:][
                                               stk_ratio.loc[PreSec_StartDate:] < 0].iloc[:-1].quantile(0.33), 3)
    Result_Loc['PreSec_DDR_Min'] = round(stk_ratio.loc[PreSec_StartDate:].iloc[:-1].min(), 3)
    Result_Loc['Latst2Now_DDRQ_Days'] = len(
        stk_data.loc[stk_ratio.loc[PreSec_StartDate:][stk_ratio.loc[PreSec_StartDate:] <=
                                                      Result_Loc['PreSec_DDR_Quntl']].index[-1]:]) \
        if any(stk_ratio.loc[PreSec_StartDate:] <= Result_Loc['PreSec_DDR_Quntl']) \
        else None

    periodchang_rise = periodchang.query('avg_ratio>0 & last_days>2')
    if len(periodchang_rise) > 1:
        Result_Loc['Period_Valley_GapRatio'] = round((stk_data.loc[turn_date, 'close'] /
                                                      stk_data.loc[Bottom_Date, 'close'] - 1) * 100, 3) \
            if turn_date != Bottom_Date \
            else round((stk_data.loc[Section_StartDate, 'close'] / stk_data.loc[Bottom_Date, 'close'] - 1) * 100, 3)

    # 20230312新增指标
    # if turn_date < Section_StartDate:
    #     Result_Loc['PostTurn_Peak2Now_Lastdays'] = len(
    #         stk_data.loc[stk_data.loc[turn_date:Section_StartDate, 'close'].idxmax():])
    #     Result_Loc['PostTurn_Peak2Now_SumRatio'] = round(
    #         (stk_data['close'].iloc[-1] /
    #          stk_data.loc[turn_date:Section_StartDate, 'close'].max() - 1) * 100, 3)
    #     Result_Loc['PostTurn_Peak2Now_AvgRatio'] = round(
    #         Result_Loc['PostTurn_Peak2Now_SumRatio'] / Result_Loc['PostTurn_Peak2Now_Lastdays'], 3) \
    #         if Result_Loc['PostTurn_Peak2Now_Lastdays'] != 0 else 0
    # else:
    Result_Loc['PostTurn_Peak2Now_Lastdays'] = len(stk_data.loc[stk_data.loc[turn_date:, 'close'].idxmax():]) - 1
    Result_Loc['PostTurn_Peak2Now_SumRatio'] = round(
        (stk_data['close'].iloc[-1] /
         stk_data.loc[turn_date:, 'close'].max() - 1) * 100, 3)
    Result_Loc['PostTurn_Peak2Now_AvgRatio'] = round(
        Result_Loc['PostTurn_Peak2Now_SumRatio'] / Result_Loc['PostTurn_Peak2Now_Lastdays'], 3) \
        if Result_Loc['PostTurn_Peak2Now_Lastdays'] != 0 else 0

    # 20230322新增指标
    sec_trend_rise = section_rise_origin.query('end_date>@turn_date & avgratio!=0')
    sec_trend_drop = section_d.query('start_date>@turn_date & avgratio!=0')
    trend_rise_ratio = max(abs(sec_trend_rise['extre_ratio'] / sec_trend_rise['avgratio'])) if len(
        sec_trend_rise) > 0 else 0
    trend_drop_ratio = max(abs(sec_trend_drop['extre_ratio'] / sec_trend_drop['avgratio'])) if len(
        sec_trend_drop) > 0 else 0
    Result_Loc['PostTurn_Sec_TrendRatio_Diff'] = round(trend_rise_ratio - trend_drop_ratio, 3)

    # 20230505新增指标
    if len(section_rise_origin.query('start_date<@Section_PeakDate')) > 0:
        shock_startdate = section_rise_origin.query('start_date<@Section_PeakDate')['start_date'].iloc[-1]
    elif len(section_rise_origin.query('start_date<@PreSec_StartDate')) > 0:
        shock_startdate = section_rise_origin.query('start_date<@PreSec_StartDate')['start_date'].iloc[-1]
    else:
        shock_startdate = None
    Result_Loc['Spring_Date'], Result_Loc['SOS_Date'], Result_Loc['Shrink_Date'], \
        Result_Loc['Break_PreSec'], Result_Loc['Shock_Date'], Result_Loc['Shock_Date_Last'], \
        Result_Loc['BreakShock_Date'], Result_Loc['BreakShockLast_Date'] \
        = '-', '-', '-', '-', '-', '-', '-', '-'
    if shock_startdate is not None:
        Result_Loc['Shock_Date'], Result_Loc['Shock_Date_Last'] = \
            identify_ShockDate(stk_data.loc[shock_startdate:Section_StartDate])
    if shock_startdate is not None and PreSec_StartDate is not None:
        Result_Loc['Spring_Date'] = identify_SpringDate(stk_data.loc[PreSec_StartDate:Section_StartDate])
        Result_Loc['SOS_Date'], Result_Loc['Shrink_Date'] = identify_SOSDate(stk_data.loc[Section_StartDate:])
        # Result_Loc['SOS_Date'], Result_Loc['StepBack_Date'], _, _ = \
        #     identify_StepBackDate(stk_data=stk_data,
        #                           section_rise=section_rise_origin,
        #                           turn_date=Section_StartDate,
        #                           shock_date=Result_Loc['Shock_Date'])

        if (stk_data.loc[Section_StartDate:, 'high'].max() >=
                max(stk_data.loc[PreSec_StartDate, 'close'], stk_data.loc[PreSec_StartDate, 'open'])):
            Result_Loc['Break_PreSec'] = 'True'

    if Result_Loc['Shock_Date'] != '-' and len(stk_data.loc[Result_Loc['Shock_Date']:Now_SecDate]) > 0:
        postshock_mindate = stk_data.loc[Result_Loc['Shock_Date']:Now_SecDate, 'close'].idxmin()
        if sum(stk_data.loc[postshock_mindate:, 'close'] >
               max(stk_data.loc[Result_Loc['Shock_Date'], 'close'],
                   stk_data.loc[Result_Loc['Shock_Date'], 'open'])) > 0:
            Result_Loc['BreakShock_Date'] = stk_data.loc[postshock_mindate:][stk_data.loc[postshock_mindate:, 'close'] > \
                                                                             max(stk_data.loc[
                                                                                     Result_Loc['Shock_Date'], 'close'],
                                                                                 stk_data.loc[Result_Loc[
                                                                                     'Shock_Date'], 'open'])].index[0]
    if Result_Loc['Shock_Date_Last'] != '-' and len(stk_data.loc[Result_Loc['Shock_Date_Last']:Now_SecDate]) > 0:
        postshocklast_mindate = stk_data.loc[Result_Loc['Shock_Date_Last']:Now_SecDate, 'close'].idxmin()
        if sum(stk_data.loc[postshocklast_mindate:, 'close'] >
               max(stk_data.loc[Result_Loc['Shock_Date_Last'], 'close'],
                   stk_data.loc[Result_Loc['Shock_Date_Last'], 'open'])) > 0:
            Result_Loc['BreakShockLast_Date'] = stk_data.loc[postshocklast_mindate:][
                stk_data.loc[postshocklast_mindate:, 'close'] >
                max(stk_data.loc[Result_Loc['Shock_Date_Last'], 'close'],
                    stk_data.loc[Result_Loc['Shock_Date_Last'], 'open'])].index[0]

    # 20230730新增指标；增加PreNow_SecDate之后的ShockDate和SpringDate
    _, Result_Loc['Now_Shock_Date'] = identify_ShockDate(stk_data.loc[PreNow_SecDate:], mode='Now')
    Result_Loc['Now_Spring_Date'] = identify_SpringDate(stk_data.loc[PreNow_SecDate:])

    # 20230506新增指标
    if len(stk_data.loc[Bottom_Date:]) > 5:
        _, _, day_list_bottom = section_stat(stk_data=stk_data,
                                             start_date=Bottom_Date,
                                             end_date=stk_data['trade_date'].iloc[-1])
        Result_Loc['PostBottom_MaxContiSum'], _, _, _ = cal_maxcontisum(stk_data, day_list_bottom)
    else:
        Result_Loc['PostBottom_MaxContiSum'] = 0

    # 20230508新增指标
    Result_Loc['PostTurn_COR_Mean'] = round(ClsOpen_Ratio.loc[turn_date:].mean(), 3)
    Result_Loc['PostTurn_COR_Std'] = round(ClsOpen_Ratio.loc[turn_date:].std(), 3)
    Result_Loc['PostTurn_COR_Und2Poxn'] = round(
        sum(ClsOpen_Ratio.loc[turn_date:] < 2) / sum(abs(stk_ratio.loc[turn_date:]) < 9), 3) \
        if sum(abs(stk_ratio.loc[turn_date:]) < 9) > 0 else 0
    Result_Loc['PostSecStart_COR_Mean'] = round(
        ClsOpen_Ratio.loc[Section_StartDate:PreNow_SecDate][
            stk_ratio.loc[Section_StartDate:PreNow_SecDate] > 0].mean(), 3) \
        if Section_StartDate < PreNow_SecDate \
        else round(
        ClsOpen_Ratio.loc[Section_StartDate:][stk_ratio.loc[Section_StartDate:] > 0].mean(), 3)
    Result_Loc['PostSecStart_COR_Std'] = round(
        ClsOpen_Ratio.loc[Section_StartDate:PreNow_SecDate][
            stk_ratio.loc[Section_StartDate:PreNow_SecDate] > 0].std(), 3) \
        if Section_StartDate < PreNow_SecDate \
        else round(
        ClsOpen_Ratio.loc[Section_StartDate:][stk_ratio.loc[Section_StartDate:] > 0].std(), 3)
    Result_Loc['PostSecStart_COR_Und2Poxn'] = round(
        sum(ClsOpen_Ratio.loc[Section_StartDate:][stk_ratio.loc[Section_StartDate:] > 0] < 2) / sum(
            abs(stk_ratio.loc[Section_StartDate:][stk_ratio.loc[Section_StartDate:] > 0]) < 9), 3) \
        if sum(abs(stk_ratio.loc[Section_StartDate:][stk_ratio.loc[Section_StartDate:] > 0]) < 9) > 0 else 0
    Result_Loc['PreNowSec_COR_Mean'] = round(
        ClsOpen_Ratio.loc[PreSec_PeakDate:Now_SecDate][stk_ratio.loc[PreSec_PeakDate:Now_SecDate] > 0].mean(), 3)
    Result_Loc['PreNowSec_COR_Std'] = round(
        ClsOpen_Ratio.loc[PreSec_PeakDate:Now_SecDate][stk_ratio.loc[PreSec_PeakDate:Now_SecDate] > 0].std(), 3)
    Result_Loc['PreNowSec_COR_Und2Poxn'] = round(
        sum(ClsOpen_Ratio.loc[PreSec_PeakDate:Now_SecDate][stk_ratio.loc[PreSec_PeakDate:Now_SecDate] > 0] < 2) / sum(
            abs(stk_ratio.loc[PreSec_PeakDate:Now_SecDate][stk_ratio.loc[PreSec_PeakDate:Now_SecDate] > 0]) < 9), 3) \
        if sum(abs(stk_ratio.loc[PreSec_PeakDate:Now_SecDate][stk_ratio.loc[PreSec_PeakDate:Now_SecDate] > 0]) < 9) > 0 \
        else 0
    Result_Loc['PostNowSec_COR_Mean'] = round(
        ClsOpen_Ratio.loc[Now_SecDate:][stk_ratio.loc[Now_SecDate:] > 0].mean(), 3) \
        if len(stk_ratio.loc[Now_SecDate:]) > 0 else 0
    Result_Loc['PostNowSec_COR_Std'] = round(
        ClsOpen_Ratio.loc[Now_SecDate:][stk_ratio.loc[Now_SecDate:] > 0].std(), 3) \
        if len(stk_ratio.loc[Now_SecDate:]) > 0 else 0
    Result_Loc['PostNowSec_COR_Und2Poxn'] = round(
        sum(ClsOpen_Ratio.loc[Now_SecDate:][stk_ratio.loc[Now_SecDate:] > 0] < 2) / sum(
            abs(stk_ratio.loc[Now_SecDate:][stk_ratio.loc[Now_SecDate:] > 0]) < 9), 3) \
        if len(stk_ratio.loc[Now_SecDate:]) > 0 \
           and sum(abs(stk_ratio.loc[Now_SecDate:][stk_ratio.loc[Now_SecDate:] > 0]) < 9) > 0 \
        else 0

    # 20230510新增指标
    Aft_BreakMax_ShockDate, _ = identify_ShockDate(stk_data.loc[Break_Max_Date:turn_date])
    if Aft_BreakMax_ShockDate != '-' \
            and stk_data.loc[Section_StartDate:, 'close'].max() >= stk_data.loc[Aft_BreakMax_ShockDate, 'open'] \
            and stk_data.loc[Break_Max_Date, 'close'] > stk_data.loc[PostSecStart_PeakDate:, 'close'].min():
        Result_Loc['Break_TurnShock'] = 'True'
    else:
        Result_Loc['Break_TurnShock'] = '-'

    """20230511新增指标：Section_StartDate, Now_SecDate拟合直线比对"""
    Result_Loc['PostSecWLS_Slope'], Result_Loc['PostSecWLS_R2'], Result_Loc['PostSecWLS_Deviation'] \
        = cal_trend_wls(stk_data.loc[Section_StartDate:])
    # Result_Loc['PostSecWLS_OverMid_Ratio'], Result_Loc['PostSecWLS_OverUp_Ratio'], \
    #     Result_Loc['PostSecWLS_UndLow_Ratio']
    if len(stk_data.loc[Now_SecDate:]) > 2:
        Result_Loc['PostNowSecWLS_Slope'], Result_Loc['PostNowSecWLS_R2'], Result_Loc['PostNowSecWLS_Deviation'] \
            = cal_trend_wls(stk_data.loc[Now_SecDate:])
    if len(stk_data.loc[Result_Loc['PreNow_BottomDate']:]) > 3:
        Result_Loc['PostPreNowBottomWLS_Slope'], Result_Loc['PostPreNowBottomWLS_R2'], \
            Result_Loc['PostPreNowBottomWLS_Deviation'] \
            = cal_trend_wls(stk_data.loc[Result_Loc['PreNow_BottomDate']:])

    """20230524新增指标：PreTurn_Peak,PostTurn_Peak,PreSec_Peak三个区段状态指标"""
    Result_Loc['PreTurn_PeakDate'] = PreTurn_PeakDate
    PostTurn_PeakDate = stk_data.loc[turn_date:, 'close'].idxmax()
    Result_Loc['PostTurn_PeakDate'] = PostTurn_PeakDate
    peak2turn_secrise, _, _ = section_stat(stk_data=stk_data,
                                           start_date=PreTurn_PeakDate,
                                           end_date=turn_date)
    if len(peak2turn_secrise) > 0:
        Result_Loc['Peak2Turn_MaxContiRise'] = round(peak2turn_secrise['sumratio'].max(), 3)
        Result_Loc['Peak2Turn_MaxContiRiseDays'] = peak2turn_secrise.loc[
            peak2turn_secrise['sumratio'].idxmax(), 'lastdays']
    else:
        Result_Loc['Peak2Turn_MaxContiRise'], Result_Loc['Peak2Turn_MaxContiRiseDays'] = 0, 0

    postturn_secdrop = section_d.query('end_date>@turn_date')
    if len(postturn_secdrop) > 0:
        Result_Loc['Turn2Peak_MaxContiDrop'] = round(postturn_secdrop['sumratio'].min(), 3)
        Result_Loc['Turn2Peak_MaxContiDropDays'] = postturn_secdrop.loc[
            postturn_secdrop['sumratio'].idxmin(), 'lastdays']
    else:
        Result_Loc['Turn2Peak_MaxContiDrop'], Result_Loc['Turn2Peak_MaxContiDropDays'] = 0, 0

    Result_Loc['Peak2Sec_SumRatio'], Result_Loc['Peak2Sec_LastDays'], Result_Loc['Peak2Sec_AvgRatio'], \
        Result_Loc['Peak2Sec_MaxContiRise'], Result_Loc['Peak2Sec_MaxContiRiseDays'] = 0, 0, 0, 0, 0
    # if turn_date < Section_StartDate:
    Result_Loc['Peak2Sec_SumRatio'] = round(
        (stk_data.loc[Section_StartDate, 'close'] / max(stk_data.loc[Section_PeakDate, 'close'],
                                                        stk_data.loc[Section_PeakDate, 'pre_close']) - 1) * 100, 3)
    Result_Loc['Peak2Sec_LastDays'] = len(stk_data.loc[Section_PeakDate:Section_StartDate]) - 1
    Result_Loc['Peak2Sec_AvgRatio'] = round(
        Result_Loc['Peak2Sec_SumRatio'] / Result_Loc['Peak2Sec_LastDays'], 3) \
        if Result_Loc['Peak2Sec_LastDays'] != 0 else 0
    Result_Loc['Peak2Sec_Sec_Max_LastDays'] = section_d.query('end_date>@Section_PeakDate')['lastdays'].max() \
        if len(section_d.query('end_date>@Section_PeakDate')) > 0 else 0
    peak2sec_secrise, _, _ = section_stat(stk_data=stk_data,
                                          start_date=Section_PeakDate,
                                          end_date=Section_StartDate)
    if len(peak2sec_secrise) > 0:
        Result_Loc['Peak2Sec_MaxContiRise'] = round(peak2sec_secrise['sumratio'].max(), 3)
        Result_Loc['Peak2Sec_MaxContiRiseDays'] = peak2sec_secrise.loc[
            peak2sec_secrise['sumratio'].idxmax(), 'lastdays']
    Result_Loc['Peak2Turn_COR_Mean'] = round(ClsOpen_Ratio.loc[PreTurn_PeakDate:turn_date].mean(), 3)
    Result_Loc['Peak2Turn_COR_Std'] = round(ClsOpen_Ratio.loc[PreTurn_PeakDate:turn_date].std(), 3)
    Result_Loc['Peak2Turn_COR_Und2Poxn'] = round(sum(ClsOpen_Ratio.loc[PreTurn_PeakDate:turn_date] < 2) / sum(
        abs(stk_ratio.loc[PreTurn_PeakDate:turn_date]) < 9), 3) \
        if sum(abs(stk_ratio.loc[PreTurn_PeakDate:turn_date]) < 9) > 0 else 0
    Result_Loc['Peak2Sec_COR_Mean'] = round(ClsOpen_Ratio.loc[Section_PeakDate:Section_StartDate].mean(), 3)
    Result_Loc['Peak2Sec_COR_Std'] = round(ClsOpen_Ratio.loc[Section_PeakDate:Section_StartDate].std(), 3)
    Result_Loc['Peak2Sec_COR_Und2Poxn'] = round(sum(ClsOpen_Ratio.loc[Section_PeakDate:Section_StartDate] < 2) / sum(
        abs(stk_ratio.loc[Section_PeakDate:Section_StartDate]) < 9), 3) \
        if sum(abs(stk_ratio.loc[Section_PeakDate:Section_StartDate]) < 9) > 0 else 0

    if len(stk_data.loc[PreTurn_PeakDate:turn_date]) > 0:
        P2T_coef = coef_regress(stk_data.loc[PreTurn_PeakDate:turn_date, 'close'], mode='err')
        P2T_std_bias = round(abs(stk_data.loc[turn_date, 'close'] / stk_data.loc[
            PreTurn_PeakDate, 'close'] - 1) * 100 / coef[4], 3) \
            if coef[4] != 0 else 0
        Result_Loc['Peak2Turn_Reg_R2'] = round(P2T_coef[0], 3)
        Result_Loc['Peak2Turn_Reg_Sum2Std'] = round(P2T_std_bias, 3)
    else:
        Result_Loc['Peak2Turn_Reg_R2'], Result_Loc['Peak2Turn_Reg_Sum2Std'] = 0, 0

    if len(stk_data.loc[Section_PeakDate:Section_StartDate]) > 0:
        P2S_coef = coef_regress(stk_data.loc[Section_PeakDate:Section_StartDate, 'close'], mode='err')
        P2S_std_bias = round(abs(stk_data.loc[Section_StartDate, 'close'] / stk_data.loc[
            Section_PeakDate, 'close'] - 1) * 100 / coef[4], 3) \
            if coef[4] != 0 else 0
        Result_Loc['Peak2Sec_Reg_R2'] = round(P2S_coef[0], 3)
        Result_Loc['Peak2Sec_Reg_Sum2Std'] = round(P2S_std_bias, 3)
    else:
        Result_Loc['Peak2Sec_Reg_R2'], Result_Loc['Peak2Sec_Reg_Sum2Std'] = 0, 0

    # 20230530新增指标：PostTurn,PostSecPeak,PostSecStart，回踩至SOS_Date
    # PostTurn
    preturn_shockdate, _ = identify_ShockDate(stk_data.loc[PreTurn_PeakDate:turn_date])
    Result_Loc['PreTurn_ShockDate'] = preturn_shockdate
    Result_Loc['PostTurn_SOSDate'], Result_Loc['PostTurn_StepBackDate'], \
        Result_Loc['PostTurn_StepBack_GapRatio'], Result_Loc['PostTurn_StepBack_LastDays'] = \
        identify_StepBackDate(stk_data=stk_data,
                              section_rise=section_rise_origin,
                              turn_date=turn_date,
                              shock_date=preturn_shockdate)
    # section_turndate = section_rise_origin['end_date'].iloc[-1]
    # section_turndate = Section_PeakDate if Section_StartDate == Now_SecDate \
    #     else section_rise_origin['end_date'].iloc[-1]
    # PostSecPeak
    postpeak_shockdate, _ = identify_ShockDate(stk_data.loc[Section_PeakDate:Section_StartDate])
    Result_Loc['PostSecPeak_ShockDate'] = postpeak_shockdate
    Result_Loc['PostSecPeak_SOSDate'], Result_Loc['PostSecPeak_StepBackDate'], \
        Result_Loc['PostSecPeak_StepBack_GapRatio'], Result_Loc['PostSecPeak_StepBack_LastDays'] = \
        identify_StepBackDate(stk_data=stk_data,
                              section_rise=section_rise_origin,
                              turn_date=Section_StartDate,
                              shock_date=postpeak_shockdate)

    # PostSec
    nowsec_shockdate, _ = identify_ShockDate(stk_data.loc[PreSec_StartDate:Section_StartDate])
    Result_Loc['PreSec_ShockDate'] = nowsec_shockdate
    Result_Loc['PostSec_SOSDate'], Result_Loc['PostSec_StepBackDate'], \
        Result_Loc['PostSec_StepBack_GapRatio'], Result_Loc['PostSec_StepBack_LastDays'] = \
        identify_StepBackDate(stk_data=stk_data,
                              section_rise=section_rise_origin,
                              turn_date=Section_StartDate,
                              shock_date=nowsec_shockdate)

    if Result_Loc['PreSec_ShockDate'] != '-' and len(stk_data.loc[Result_Loc['PreSec_ShockDate']:Now_SecDate]) > 0:
        postshock_mindate = stk_data.loc[Result_Loc['PreSec_ShockDate']:Now_SecDate, 'close'].idxmin()
        if sum(stk_data.loc[postshock_mindate:, 'close'] >
               max(stk_data.loc[Result_Loc['PreSec_ShockDate'], 'close'],
                   stk_data.loc[Result_Loc['PreSec_ShockDate'], 'open'])) > 0:
            postnowsec_max = stk_data.loc[postshock_mindate:, 'close'].max()
            shock_maxcls = max(stk_data.loc[Result_Loc['PreSec_ShockDate'], 'close'],
                               stk_data.loc[Result_Loc['PreSec_ShockDate'], 'open'])
            Result_Loc['PostSec_Max2PreShock_Ratio'] = round((postnowsec_max / shock_maxcls - 1) * 100, 3)

    # 新增指标20230609：PostSecStart的最大下跌幅度和天数
    # postsec_secdrop = section_d.query('start_date>@Section_StartDate').copy()
    # if len(postsec_secdrop) > 0:
    #     for secnum in postsec_secdrop.index:
    #         sec_start = postsec_secdrop.loc[secnum, 'start_date']
    #         sec_end = postsec_secdrop.loc[secnum, 'end_date']
    #         postsec_secdrop.loc[secnum, 'maxdrop'] = round(
    #             (max(stk_data.loc[sec_start, 'close'], stk_data.loc[sec_start, 'pre_close'])/
    #              stk_data.loc[sec_start:sec_end, 'close'].min()-1)*100, 3)
    #     Result_Loc['PostSecStart_MaxContiDrop'] = round(postsec_secdrop['maxdrop'].max(), 3)
    #     Result_Loc['PostSecStart_MaxContiDropDays'] = \
    #         postsec_secdrop.loc[postsec_secdrop['maxdrop'].idxmax(), 'lastdays']

    # 新增指标20230627：增加section的上涨1%或下跌1%所需换手率，
    # 跟踪上行section的turnover2rise，下行section的turnover2drop是否出现较低值
    # if len(section_d.query('start_date<@Section_StartDate')) > 0:
    #     Result_Loc['PreSec_Turnover2Rise'] = section_d.query('start_date<@Section_StartDate')['turnover2rise'].iloc[-1]
    #     Result_Loc['PreSec_Turnover2Drop'] = section_d.query('start_date<@Section_StartDate')['turnover2drop'].iloc[-1]
    # elif len(section_drop.query('start_date<@Section_StartDate')) > 0:
    #     Result_Loc['PreSec_Turnover2Rise'] = section_drop.query(
    #         'start_date<@Section_StartDate')['turnover2rise'].iloc[-1]
    #     Result_Loc['PreSec_Turnover2Drop'] = section_drop.query(
    #         'start_date<@Section_StartDate')['turnover2drop'].iloc[-1]
    if Bottom_Date < Section_PeakDate:
        TO_StartDate = stk_data.loc[Bottom_Date:Section_StartDate, 'close'].idxmax()
    else:
        TO_StartDate = Section_PeakDate
    section_rise_over1D = section_rise_origin.query('lastdays>1')
    section_d_over1D = section_d.query('lastdays>1')
    if len(section_d_over1D.query('start_date<@Section_StartDate & end_date>@TO_StartDate')) > 0:
        Result_Loc['PreSec_Turnover2Rise'] = section_d_over1D.query(
            'start_date<@Section_StartDate')['turnover2change'].iloc[-1]
        Result_Loc['PreSec_Turnover2Drop'] = section_d_over1D.query(
            'start_date<@Section_StartDate')['turnover2change'].iloc[-1]
        last4min_turnover2drop = section_d_over1D.query(
            'start_date<@Section_StartDate')['turnover2change'].iloc[
                                 -min(4, len(section_d_over1D.query('start_date<@Section_StartDate'))):].rank()
        Result_Loc['PreSec_Turnover2Drop_Last4Rank'] = last4min_turnover2drop.iloc[-1]
        fallsec_turnover2drop_quntl = section_d_over1D.query(
            'start_date<@Section_StartDate & end_date>@TO_StartDate')['turnover2change'].quantile(0.33)
        fallsec_turnover2drop_min = section_d_over1D.query(
            'start_date<@Section_StartDate & end_date>@TO_StartDate')['turnover2change'].min()
        postsec_t2d_first = section_d_over1D.query('start_date>@Section_StartDate')['turnover2change'].iloc[0] \
            if len(section_d_over1D.query('start_date>@Section_StartDate')) > 0 else None
        if fallsec_turnover2drop_min < Result_Loc['PreSec_Turnover2Drop'] <= fallsec_turnover2drop_quntl:
            Result_Loc['PreSec_Turnover2Drop_State'] = 1
        elif (section_d_over1D.query('start_date<@Section_StartDate')['turnover2change'].iloc[-2:].min() ==
              fallsec_turnover2drop_min and fallsec_turnover2drop_min < 1) \
                and (postsec_t2d_first is None or Result_Loc['PreSec_Turnover2Drop'] < postsec_t2d_first):
            Result_Loc['PreSec_Turnover2Drop_State'] = 2
        else:
            Result_Loc['PreSec_Turnover2Drop_State'] = 0
    Result_Loc['Now_Turnover2Drop_State'] = 0
    if len(section_d.query('start_date>@Section_StartDate')) > 1:
        postsec_fallsec_TO_max = section_d.query('start_date>@Section_StartDate')['turnover2change'].max()
        if section_d.query('start_date>@Section_StartDate')['turnover2change'].iloc[-1] == postsec_fallsec_TO_max:
            Result_Loc['Now_Turnover2Drop_State'] = 1

    postsec_turnover2rise_quntl1 = section_rise_over1D.query(
        'start_date>@Section_PeakDate')['turnover2change'].quantile(0.5)
    postsec_turnover2rise_quntl2 = section_rise_over1D.query(
        'start_date>@Section_PeakDate')['turnover2change'].quantile(0.33)
    Result_Loc['Now_RiseSec_Turnover2Rise'] = section_rise_origin['turnover2change'].iloc[-1]
    Result_Loc['Now_RiseSec_Turnover2Drop'] = section_rise_origin['turnover2change'].iloc[-1]
    Result_Loc['Now_FallSec_Turnover2Drop'] = section_d['turnover2change'].iloc[-1]
    Result_Loc['Now_RiseSec_LastDays'] = section_rise_origin['lastdays'].iloc[-1]
    if len(section_rise_over1D.query('start_date>@Section_StartDate')) > 0:
        recentsection_turnover2rise = section_rise_origin.query(
            'end_date>@Section_StartDate')['turnover2change'].iloc[-1]
        if postsec_turnover2rise_quntl2 < recentsection_turnover2rise < postsec_turnover2rise_quntl1:
            Result_Loc['Now_Turnover2Rise_State'] = 1
        elif recentsection_turnover2rise <= postsec_turnover2rise_quntl2:
            Result_Loc['Now_Turnover2Rise_State'] = 2
        else:
            Result_Loc['Now_Turnover2Rise_State'] = 0

    # elif len(section_drop.query('start_date<@Section_StartDate')) > 0:
    #     Result_Loc['PreSec_Turnover2Rise'] = section_drop.query(
    #         'start_date<@Section_StartDate')['turnover2rise'].iloc[-1]
    #     Result_Loc['PreSec_Turnover2Drop'] = section_drop.query(
    #         'start_date<@Section_StartDate')['turnover2drop'].iloc[-1]
    #     last4min_turnover2drop = section_drop.query(
    #         'start_date<@Section_StartDate')['turnover2drop'].iloc[
    #                              -min(4, len(section_drop.query('start_date<@Section_StartDate'))):].rank()
    #     Result_Loc['PreSec_Turnover2Drop_Last4Rank'] = last4min_turnover2drop.iloc[-1]

    if len(periodchang.query('start_date<@turn_date & avg_ratio<0')) > 0:
        Result_Loc['PreTurn_Turnover2Rise'] = periodchang.query(
            'start_date<@turn_date & avg_ratio<0')['turnover2change'].iloc[-1]
        Result_Loc['PreTurn_Turnover2Drop'] = periodchang.query(
            'start_date<@turn_date & avg_ratio<0')['turnover2change'].iloc[-1]
        last4turn_min_turnover2drop = periodchang.query(
            'start_date<@turn_date & avg_ratio<0')['turnover2change'].iloc[
                                      -min(4, len(periodchang.query('start_date<@turn_date & avg_ratio<0'))):].rank()
        Result_Loc['PreTurn_Turnover2Drop_Last4Rank'] = last4turn_min_turnover2drop.iloc[-1]

    # 新增指标20230703：调用WLS模型，计算Peak2Sec区间的斜率、R2和Deviation
    Result_Loc['Peak2SecWLS_Slope'], Result_Loc['Peak2SecWLS_R2'], Result_Loc['Peak2SecWLS_Deviation'] \
        = cal_trend_wls(stk_data.loc[Section_PeakDate:Section_StartDate])
    # Result_Loc['Peak2SecWLS_OverMid_Ratio'], Result_Loc['Peak2SecWLS_OverUp_Ratio'], \
    #     Result_Loc['Peak2SecWLS_UndLow_Ratio']
    Result_Loc['PreSecWLS_Slope'], Result_Loc['PreSecWLS_R2'], Result_Loc['PreSecWLS_Deviation'] \
        = cal_trend_wls(stk_data.loc[PreSec_StartDate:Section_StartDate])
    # Result_Loc['PreSecWLS_OverMid_Ratio'], Result_Loc['PreSecWLS_OverUp_Ratio'], \
    #     Result_Loc['PreSecWLS_UndLow_Ratio']

    # 新增指标20230704：Section_StartDate后最高价相对Section_PeakDate的幅度
    Result_Loc['PostSecMax2Peak_Ratio'] = round(
        (stk_data.loc[Section_PeakDate, 'close'] / stk_data.loc[Section_StartDate:, 'close'].max() - 1) * 100, 3)

    # 新增指标20230730：判定是否突破前一Period或Turn_Date至Section_StartDate高点位置
    # Turn2Sec_MaxClose = stk_data.loc[turn_date:Section_StartDate, 'close'].max()
    if len(periodchang.query('start_date<@turn_date & avg_ratio<0')) > 0:
        bf_turn_close = stk_data.loc[
            periodchang.query('start_date<@turn_date & avg_ratio<0')['start_date'].iloc[-1], 'close']
    else:
        bf_turn_close = stk_data.loc[turn_date, 'close']
    Result_Loc['PostTurn_BreakRatio'] = round(
        (stk_data.loc[turn_date:, 'close'].max() / bf_turn_close - 1) * 100, 3)

    # 新增指标20230731：判定是否突破Section_StateDate前下行Section开始价格
    if len(section_rise_origin.query('end_date>@Section_StartDate')) > 0 \
            and len(section_d.query('start_date<@Section_StartDate')) > 0:
        postsec_1stdate = section_rise_origin.query('end_date>@Section_StartDate')['end_date'].iloc[-1]
        presec_lastdate = section_d.query('start_date<@Section_StartDate')['start_date'].iloc[-1]
        Result_Loc['PostSec_1stSec_BreakRatio'] = round(
            (stk_data.loc[postsec_1stdate, 'close'] /
             min(stk_data.loc[presec_lastdate, 'close'], stk_data.loc[presec_lastdate, 'open']) - 1) * 100, 3)
    else:
        Result_Loc['PostSec_1stSec_BreakRatio'] = -100

    # 新增指标20230822：当前Section日均涨跌幅和持续天数
    if len(section_d) > 0 and section_d['end_date'].iloc[-1] > section_rise_origin['end_date'].iloc[-1]:
        Result_Loc['NowSec_AvgRatio'] = section_d['avgratio'].iloc[-1]
        Result_Loc['NowSec_LastDays'] = section_d['lastdays'].iloc[-1]
        Result_Loc['NowSec_Und2ContiDays'] = section_d['und2_contidays'].iloc[-1]
    else:
        Result_Loc['NowSec_AvgRatio'] = section_rise_origin['avgratio'].iloc[-1]
        Result_Loc['NowSec_LastDays'] = section_rise_origin['lastdays'].iloc[-1]
        Result_Loc['NowSec_Und2ContiDays'] = section_rise_origin['und2_contidays'].iloc[-1]
    Result_Loc['Peak2Sec_Und2ContiDays'] = count_und_num(
        stk_data.loc[Result_Loc['Section_PeakDate']:Result_Loc['Section_StartDate'], 'max_ratio'], 2, 1)
    Result_Loc['Peak2Sec_Und2SumDays'] = count_und_num(
        stk_data.loc[Result_Loc['Section_PeakDate']:Result_Loc['Section_StartDate'], 'max_ratio'],
        2, 2)
    Result_Loc['PostSecStart_Und2ContiDays'] = count_und_num(
        stk_data.loc[Result_Loc['Section_StartDate']:, 'max_ratio'], 2, 1)
    Result_Loc['PostSecStart_Und2SumDays'] = count_und_num(
        stk_data.loc[Result_Loc['Section_StartDate']:, 'max_ratio'],
        2, 2)

    # 新增指标20230907：Section_StartDate之后，最大Section涨幅和日均涨幅
    Result_Loc['PostSecStart_Sec_Max_SumRatio'] = round(section_rise_origin.query(
        'end_date>@Section_StartDate')['sumratio'].max(), 3)
    Result_Loc['PostSecStart_Sec_Max_AvgRatio'] = round(section_rise_origin.query(
        'end_date>@Section_StartDate')['avgratio'].max(), 3)
    postsec_max_risedays = section_rise_origin.query('end_date>@Section_StartDate')['lastdays'].max() \
        if len(section_rise_origin.query('end_date>@Section_StartDate')) > 0 else 0
    postsec_max_dropdays = section_d.query('start_date>@Section_StartDate')['lastdays'].max() \
        if len(section_d.query('start_date>@Section_StartDate')) > 0 else 0
    Result_Loc['PostSecStart_Sec_Max_LastDays'] = max(postsec_max_risedays, postsec_max_dropdays)
    
    # 新增指标20250324：Section_StartDate之后，最大Section跌幅和跌幅持续天数
    if len(section_d.query('start_date>@Section_StartDate')) > 0:
        Result_Loc['PostSecStart_Sec_MaxDrop_SumRatio'] = round(
            section_d.query('start_date>@Section_StartDate')['sumratio'].min(), 3) 
        # min_index = section_d.query('start_date>@Section_StartDate')['sumratio'].idxmin()
        Result_Loc['PostSecStart_Sec_MaxDrop_LastDays'] = section_d.query('start_date>@Section_StartDate')['lastdays'].max()
        Result_Loc['PostSecStart_Sec_MaxDrop_AvgRatio'] = round(
            section_d.query('start_date>@Section_StartDate')['avgratio'].min(), 3)
    else:
        Result_Loc['PostSecStart_Sec_MaxDrop_SumRatio'] = 99
        Result_Loc['PostSecStart_Sec_MaxDrop_LastDays'] = 99
        Result_Loc['PostSecStart_Sec_MaxDrop_AvgRatio'] = 99

    # 新增指标20231030:当前日期低于SOSDate开收盘价格较大值
    Result_Loc['Now_StepBack_State'] = 'True' \
        if Result_Loc['PostSecPeak_SOSDate'] != '-' \
           and stk_data['low'].iloc[-1] < max(
        stk_data.loc[Result_Loc['PostSecPeak_SOSDate'], 'close'],
        stk_data.loc[Result_Loc['PostSecPeak_SOSDate'], 'open']) \
           and stk_data['high'].iloc[-2] > min(
        stk_data.loc[Result_Loc['PostSecPeak_SOSDate'], 'close'],
        stk_data.loc[Result_Loc['PostSecPeak_SOSDate'], 'open']) \
           and Result_Loc['NowSec_AvgRatio'] < 0 \
        else '-'

    # 新增指标20231109：当前Section是否对应turn_date及Section_StartDate之后sumratio最大值
    if len(section_d.query('start_date>@turn_date & start_date<@Now_SecDate')) > 0 \
            and section_d.query('start_date>@turn_date & start_date<@Now_SecDate')['cls_diff'].max() == \
            section_d.query('start_date>@turn_date & start_date<@Now_SecDate')['cls_diff'].iloc[-1]:
        Result_Loc['Now_PostTurn_SecDropState'] = 1
        if len(section_d.query('start_date>@turn_date & start_date<@Now_SecDate')) > 1:
            Result_Loc['Now_PostTurn_SecDropState'] = 2
    else:
        Result_Loc['Now_PostTurn_SecDropState'] = -1
    if len(section_d.query('start_date>@Section_StartDate & start_date<@Now_SecDate')) == 1:
        Result_Loc['Now_PostSec_SecDropState'] = 1
    elif len(section_d.query('start_date>@Section_StartDate & start_date<@Now_SecDate')) > 1 \
            and section_d.query('start_date>@Section_StartDate & start_date<@Now_SecDate')['cls_diff'].max() == \
            section_d.query('start_date>@Section_StartDate & start_date<@Now_SecDate')['cls_diff'].iloc[-1]:
        Result_Loc['Now_PostSec_SecDropState'] = 2
    elif len(section_d.query('start_date>@Section_StartDate & start_date<@Now_SecDate')) > 1 \
            and section_d.query('start_date>@Section_StartDate & start_date<@Now_SecDate')['cls_diff'].min() == \
            section_d.query('start_date>@Section_StartDate & start_date<@Now_SecDate')['cls_diff'].iloc[-1]:
        Result_Loc['Now_PostSec_SecDropState'] = 3
    else:
        Result_Loc['Now_PostSec_SecDropState'] = -1

    Result_Loc['Now_PostSec_SecRise_Portion'] = round(section_rise_origin.query(
        'end_date>@Section_StartDate & end_date<@Now_SecDate')['cls_diff'].iloc[-1] /
                                                      abs(stk_data.loc[Section_StartDate:, 'close'].max() -
                                                          min(stk_data.loc[Section_StartDate, 'close'],
                                                              stk_data.loc[Section_StartDate, 'pre_close'])), 3) \
        if len(section_rise_origin.query('end_date>@Section_StartDate & end_date<@Now_SecDate')) > 0 \
           and stk_data.loc[Section_StartDate:, 'close'].max() != min(stk_data.loc[Section_StartDate, 'close'],
                                                                      stk_data.loc[Section_StartDate, 'pre_close']) \
        else 0

    Result_Loc['Now_PostSec_SecRise_ClsDiff'] = round(section_rise_origin.query(
        'end_date>@Section_StartDate & end_date<@Now_SecDate')['cls_diff'].iloc[-1], 3) \
        if len(section_rise_origin.query('end_date>@Section_StartDate & end_date<@Now_SecDate')) > 0 \
        else 0
    Result_Loc['PostSec_SecDrop_MaxClsDiff'] = round(section_d.query(
        'start_date>@Section_StartDate & start_date<@Now_SecDate')['cls_diff'].max(), 3) \
        if len(section_d.query('start_date>@Section_StartDate & start_date<@Now_SecDate')) > 0 else 0

    if (len(section_rise_origin.query('end_date>@Section_StartDate & end_date<@Now_SecDate')) > 1
            and section_rise_origin.query('end_date>@Section_StartDate & end_date<@Now_SecDate')['cls_diff'].max() !=
            section_rise_origin.query('end_date>@Section_StartDate & end_date<@Now_SecDate')['cls_diff'].iloc[-1]):
        Result_Loc['Now_PostSec_SecRiseState'] = 0
    elif (len(section_rise_origin.query('end_date>@Section_StartDate & end_date<@Now_SecDate')) > 1
          and section_rise_origin.query('end_date>@Section_StartDate & end_date<@Now_SecDate')['cls_diff'].max() ==
          section_rise_origin.query('end_date>@Section_StartDate & end_date<@Now_SecDate')['cls_diff'].iloc[-1]) \
            or len(section_rise_origin.query('end_date>@Section_StartDate & end_date<@Now_SecDate')) == 1:
        Result_Loc['Now_PostSec_SecRiseState'] = 1
        if (len(section_rise_origin.query('end_date>@Section_StartDate & end_date<@Now_SecDate')) > 1
            and section_rise_origin.query('end_date>@Section_StartDate & end_date<@Now_SecDate')['cls_diff'].max() ==
            section_rise_origin.query('end_date>@Section_StartDate & end_date<@Now_SecDate')['cls_diff'].iloc[-1]) \
                and Result_Loc['Now_PostSec_SecRise_Portion'] >= 0.5:
            Result_Loc['Now_PostSec_SecRiseState'] = 2
    else:
        Result_Loc['Now_PostSec_SecRiseState'] = -1

    if (len(section_rise_origin.query('end_date>@turn_date & end_date<@Now_SecDate')) > 1
            and section_rise_origin.query('end_date>@turn_date & end_date<@Now_SecDate')['cls_diff'].max() !=
            section_rise_origin.query('end_date>@turn_date & end_date<@Now_SecDate')['cls_diff'].iloc[-1]):
        Result_Loc['Now_PostTurn_SecRiseState'] = 0
    elif (len(section_rise_origin.query('end_date>@turn_date & end_date<@Now_SecDate')) > 1
          and section_rise_origin.query('end_date>@turn_date & end_date<@Now_SecDate')['cls_diff'].max() ==
          section_rise_origin.query('end_date>@turn_date & end_date<@Now_SecDate')['cls_diff'].iloc[-1]) \
            or len(section_rise_origin.query('end_date>@turn_date & end_date<@Now_SecDate')) == 1:
        Result_Loc['Now_PostTurn_SecRiseState'] = 1
        if (len(section_rise_origin.query('end_date>@turn_date & end_date<@Now_SecDate')) > 1
            and section_rise_origin.query('end_date>@turn_date & end_date<@Now_SecDate')['cls_diff'].max() ==
            section_rise_origin.query('end_date>@turn_date & end_date<@Now_SecDate')['cls_diff'].iloc[-1]) \
                and section_rise_origin.query('end_date>@turn_date & end_date<@Now_SecDate')['cls_diff'].iloc[-1] >= 5:
            Result_Loc['Now_PostTurn_SecRiseState'] = 2
    else:
        Result_Loc['Now_PostTurn_SecRiseState'] = -1

    # 新增指标：是否超过前三天最高收盘价
    Result_Loc['Now_Over5Min'] = 'True' \
        if stk_data['close'].iloc[-1] > min(stk_data['close'].iloc[-6:-1].min(), stk_data['open'].iloc[-6:-1].min()) \
        else 'False'
    Result_Loc['Now_Over3Max'] = 'True' \
        if stk_data['close'].iloc[-1] > max(stk_data['close'].iloc[-4:-1].max(), stk_data['open'].iloc[-4:-1].max()) \
        else 'False'
    Result_Loc['Now_Over3Mean'] = 'True' \
        if stk_data['close'].iloc[-1] > (stk_data['close'].iloc[-4:-1].mean() +
                                         stk_data['open'].iloc[-4:-1].mean()) / 2 \
        else 'False'
    Result_Loc['Now_Recent3Mean'] = round((stk_data['close'].iloc[-3:].mean() + stk_data['open'].iloc[-3:].mean()) / 2,
                                          3)
    Result_Loc['Now_Recent3Min'] = round(min(stk_data['close'].iloc[-3:].min(), stk_data['open'].iloc[-3:].min()), 3)
    Result_Loc['Now_Und3Min'] = 'True' \
        if stk_data['close'].iloc[-1] < stk_data['close'].iloc[-4:-1].min() \
        else 'False'
    Result_Loc['Now_Und3Mean'] = 'True' \
        if stk_data['close'].iloc[-1] < (stk_data['close'].iloc[-4:-1].mean() + stk_data['open'].iloc[-4:-1].mean()) / 2 \
        else 'False'

    # 新增指标20231215: PostSecStart的Sec持续天数比值
    postsec_secmaxdrop = section_d.query('start_date>@Section_StartDate')['lastdays'].max() \
        if len(section_d.query('start_date>@Section_StartDate')) > 0 else 0
    postsec_secsumdrop = section_d.query('start_date>@Section_StartDate')['lastdays'].sum() \
        if len(section_d.query('start_date>@Section_StartDate')) > 0 else 0
    postsec_secminsum = abs(section_d.query('start_date>@Section_StartDate')['sumratio'].min()) \
        if len(section_d.query('start_date>@Section_StartDate')) > 0 else 0
    Result_Loc['PostSecStart_Drop2Rise_SecMaxDaysDiv'] = round(
        postsec_secmaxdrop / section_rise_origin.query('end_date>@Section_StartDate')['lastdays'].max(), 3)
    Result_Loc['PostSecStart_Drop2Rise_SecSumDaysDiv'] = round(
        postsec_secsumdrop / section_rise_origin.query('end_date>@Section_StartDate')['lastdays'].sum(), 3) \
        if section_rise_origin.query('end_date>@Section_StartDate')['lastdays'].sum() != 0 else 0
    Result_Loc['PostSecStart_Drop2Rise_SecMaxSumDiv'] = round(
        postsec_secminsum / section_rise_origin.query('end_date>@Section_StartDate')['sumratio'].max(), 3) \
        if section_rise_origin.query('end_date>@Section_StartDate')['sumratio'].max() != 0 else 0

    # 新增指标 20231220: 计算高低价相对移动平均价格偏离度的均值和标准差，识别缓步运行状态
    Result_Loc['PostSecStart_MovAvg_BiasMean'], Result_Loc['PostSecStart_MovAvg_BiasStd'] = \
        cal_movavg_bias(stk_data=stk_data.loc[Section_StartDate:Now_SecDate])

    # 新增指标 20230103：股票波动幅度扩大指标，以开盘收盘幅度的均值和标准差衡量
    if Section_StartDate < PreNow_SecDate:
        Bf_PreNow_threshold = stk_data.loc[Section_StartDate:PreNow_PeakDate, 'clsopen_ratio'].mean() + \
                              stk_data.loc[Section_StartDate:PreNow_PeakDate, 'clsopen_ratio'].std()
        Aft_PreNowPeak_threshold = stk_data.loc[PreNow_PeakDate:, 'clsopen_ratio'].mean() + \
                                   stk_data.loc[PreNow_PeakDate:, 'clsopen_ratio'].std() * 0.5
        filter_stkdata = stk_data.loc[PreNow_PeakDate:]
        Result_Loc['PreNow2Now_Volab_ProP'] = round((filter_stkdata['clsopen_ratio'] > Bf_PreNow_threshold).mean(), 3)
        filter_stkdata = stk_data.loc[Section_StartDate:PreNow_PeakDate]
        Result_Loc['Sec2PreNow_Volab_ProP'] = round((filter_stkdata['clsopen_ratio'] > Bf_PreNow_threshold).mean(), 3)
        index_dates = stk_data.loc[Section_StartDate:][
            stk_data.loc[Section_StartDate:, 'clsopen_ratio'] > Bf_PreNow_threshold].index
        cover_enddate = next(iter(index_dates[index_dates > Now_SecDate]),
                             next(iter(index_dates[index_dates > PreNow_PeakDate]), '-'))
        if cover_enddate != '-' and len(index_dates[index_dates < cover_enddate]) > 0:
            cover_startdate = index_dates[index_dates < cover_enddate][-1]
        else:
            cover_startdate = Section_StartDate
        Result_Loc['UndVolab_LastDays'] = len(stk_data.loc[cover_startdate:cover_enddate]) - 1 \
            if cover_enddate != '-' else 0
        # Result_Loc['Bf_PreNow_Volab_Threshold'] = Bf_PreNow_threshold
        # Result_Loc['Aft_PreNow_Volab_Threshold'] = Aft_PreNow_threshold
        prenow2now_length = len(stk_data.loc[PreNow_SecDate:])
        Result_Loc['Recent5D_BfPreNow_Volab_Num'] = (
                stk_data.loc[:PreNow_SecDate]['clsopen_ratio'].iloc[-5:] > Bf_PreNow_threshold).sum()
        Result_Loc['Recent5D_AftPreNow_Volab_Num'] = (
                stk_data.loc[PreNow_SecDate:]['clsopen_ratio'].iloc[
                -min(5, prenow2now_length):] > Aft_PreNowPeak_threshold).sum()
    else:
        Result_Loc['PreNow2Now_Volab_ProP'], Result_Loc['Sec2PreNow_Volab_ProP'], \
            Result_Loc['UndVolab_LastDays'], Result_Loc['Recent5D_BfPreNow_Volab_Num'], \
            Result_Loc['Recent5D_AftPreNow_Volab_Num'] = 0, 0, 0, 0, 0

    # 新增指标 20230202:Section_StartDate前下行周期是否超过Turn_Date后下行周期天数的Quntl值
    if turn_date < Section_StartDate:
        turnstart_date = stk_data.loc[turn_date:Section_StartDate, 'close'].idxmax()
    elif Bottom_Date < Section_StartDate:
        turnstart_date = stk_data.loc[Bottom_Date:Section_StartDate, 'close'].idxmax()
    else:
        turnstart_date = Section_PeakDate
    if len(section_d.query('end_date>@turnstart_date & start_date<@Section_StartDate')) > 0:
        postturn_dropdays_quntl = section_d.query('end_date>@turnstart_date & start_date<@Section_StartDate'
                                                  )['lastdays'].quantile(0.67)
        postturn_dropavg_quntl = section_d.query('end_date>@turnstart_date & start_date<@Section_StartDate'
                                                 )['avgratio'].abs().quantile(0.5)
        Result_Loc['PreSec_SecDropDays_QuntlRatio'] = round(
            section_d.query('start_date<@Section_StartDate')['lastdays'].iloc[-1] / postturn_dropdays_quntl, 3)
        Result_Loc['PreNowSec_SecDropDays_QuntlRatio'] = round(
            section_d.query('start_date<@Now_SecDate')['lastdays'].iloc[-1] / postturn_dropdays_quntl, 3)
        Result_Loc['PreSec_SecDropAvg_QuntlRatio'] = round(
            section_d.query('start_date<@Section_StartDate')['avgratio'].abs().iloc[-1] / postturn_dropavg_quntl, 3)
        Result_Loc['PreNowSec_SecDropAvg_QuntlRatio'] = round(
            section_d.query('start_date<@Now_SecDate')['avgratio'].abs().iloc[-1] / postturn_dropavg_quntl, 3)
    else:
        Result_Loc['PreSec_SecDropDays_QuntlRatio'], Result_Loc['PreNowSec_SecDropDays_QuntlRatio'], \
            Result_Loc['PreSec_SecDropAvg_QuntlRatio'], Result_Loc['PreNowSec_SecDropAvg_QuntlRatio'] = 0, 0, 0, 0

    # 新增指标 20240302：Section_PeakDate之前覆盖交易日天数
    PreSec_PeakClose = max(stk_data.loc[Section_PeakDate, 'close'], stk_data.loc[Section_PeakDate, 'open'])
    check_enddate = section_rise_origin.query('start_date<@Section_PeakDate')['start_date'].iloc[-1] \
        if len(section_rise_origin.query('start_date<@Section_PeakDate')) > 0 else Section_PeakDate
    check_startdate = stk_data.loc[:check_enddate].query('close>@PreSec_PeakClose').index[-1] \
        if len(stk_data.loc[:check_enddate].query('close>@PreSec_PeakClose')) > 0 else stk_data.index[0]
    Result_Loc['SecPeakConcave_CoverDays'] = max(len(stk_data.loc[check_startdate:Section_PeakDate]) - 1, 0)

    # 新增指标 20240418：PostSecStart_PeakDate前最后一个上行区段的持续天数和累计收益
    section_rise_bf_secpeak = section_rise_origin.query('start_date<@PostSecStart_PeakDate')
    if PostSecStart_PeakDate != Section_StartDate and len(section_rise_bf_secpeak) > 0:
        Result_Loc['PreSecPeak_Sec_LastDays'] = section_rise_bf_secpeak['lastdays'].iloc[-1]
        Result_Loc['PreSecPeak_Sec_SumRatio'] = round(section_rise_bf_secpeak['sumratio'].iloc[-1], 3)
        Result_Loc['PreSecPeak_Sec_AvgRatio'] = round(section_rise_bf_secpeak['avgratio'].iloc[-1], 3)
        Result_Loc['PreSecPeak_Sec_StartDate'] = section_rise_bf_secpeak['start_date'].iloc[-1]
        Result_Loc['PostSecPeak_Over_StartCls_State'] = 'True' \
            if (stk_data.loc[PostSecStart_PeakDate:, 'close'].min() >=
                min(stk_data.loc[section_rise_bf_secpeak['start_date'].iloc[-1], 'close'],
                    stk_data.loc[section_rise_bf_secpeak['start_date'].iloc[-1], 'pre_close'])) \
            else '-'

    # 新增指标 20240613：Section_StartDate之后Section逐级升高状态
    stkdiff_section = stk_data.loc[
        section_rise_origin.query('end_date>@Section_StartDate')['start_date'].values.tolist(), 'close'].diff() \
        if len(section_rise_origin.query('end_date>@Section_StartDate')) > 1 else []
    if len(stkdiff_section) > 1:
        Result_Loc['PostSecStart_SecRiseState'] = round(
            sum(stkdiff_section.iloc[1:] > 0) / (len(stkdiff_section) - 1), 3)
    else:
        Result_Loc['PostSecStart_SecRiseState'] = 0

    # 新增指标 20240618：相对index_data收益率累加最小值对应日期
    index_ratio = index_data.loc[PreNow_PeakDate:, 'close'].pct_change()
    compare_data = pd.merge(stk_ratio.loc[PreNow_PeakDate:], index_ratio, how='left', left_index=True, right_index=True)
    compare_data.columns = ['stk_ratio', 'index_ratio']
    compare_data['cumsum_ratio'] = (compare_data['stk_ratio'] - compare_data['index_ratio']).cumsum()
    Result_Loc['PostPreNowPeak_CumSum2IdxRatio_MinDate'] = compare_data['cumsum_ratio'].idxmin()
    compare_data_1 = pd.merge(stk_ratio.loc[Now_SecDate:], index_ratio.loc[Now_SecDate:],
                              how='left', left_index=True, right_index=True)
    compare_data_1.columns = ['stk_ratio', 'index_ratio']
    compare_data_1['cumsum_ratio'] = (compare_data_1['stk_ratio'] - compare_data_1['index_ratio']).cumsum()
    Result_Loc['PostNowSec_CumSum2IdxRatio_MaxDate'] = compare_data_1['cumsum_ratio'].idxmax()

    # 新增指标 20240627: PostTurn_PeakDate前的上行Section起始日期
    Result_Loc['PreTurnPeak_Sec_StartDate'] = section_rise_origin.query(
        'start_date<@PostTurn_PeakDate')['start_date'].iloc[-1] \
        if len(section_rise_origin.query('start_date<@PostTurn_PeakDate')) > 0 else None

    # 新增指标 20240719：突破Now_SecDate左侧下行Section起始日期
    Result_Loc['BreakPreNowSec_Ratio'] = round(
        (stk_data.loc[Now_SecDate:, 'close'].max() /
         max(stk_data.loc[PreNow_SecDate, 'close'], stk_data.loc[PreNow_SecDate, 'open']) - 1) * 100, 3)
    Result_Loc['BreakPreNowPeak_Ratio'] = round(
        (stk_data.loc[Now_SecDate:, 'close'].max() /
         max(stk_data.loc[PreNow_PeakDate, 'close'], stk_data.loc[PreNow_PeakDate, 'open']) - 1) * 100, 3)
    Result_Loc['PreNowPeak_BottomDate'] = stk_data.loc[PostTurn_PeakDate:Now_SecDate, 'close'].idxmin() \
        if PostTurn_PeakDate < Now_SecDate else Now_SecDate

    # 新增指标 20240809：PreNowSec_Turnover2Drop, Now_Turnover2Ratio, PostNowSec_Turnover2Rise
    Now_SecLagDate = stk_data.loc[Now_SecDate:].index[1] if len(stk_data.loc[Now_SecDate:]) > 1 else Now_SecDate
    Result_Loc['PreNowSec_AvgTurnover2Drop'] = round(
        abs(stk_data.loc[PreNow_SecDate:Now_SecLagDate, 'turnover'].sum() / Result_Loc['PreNowSec_SumRatio']) /
        Result_Loc['PreNowSec_LastDays'], 3)
    Result_Loc['PostNowSec_AvgTurnover2Rise'] = round(
        abs(stk_data.loc[Now_SecDate:, 'turnover'].sum() / Result_Loc['PostNowSec_SumRatio']) /
        Result_Loc['PostNowSec_LastDays'], 3) \
        if Result_Loc['PostNowSec_LastDays'] > 1 and Result_Loc['PostNowSec_SumRatio'] != 0 else 0
    Result_Loc['Now_Turnover2Ratio'] = round(abs(stk_data['turnover'].iloc[-1] / stk_ratio.iloc[-1]), 3) \
        if stk_ratio.iloc[-1] != 0 else 0

    # 新增指标 20241102：自SecPeak或PreSecPeak下行以来超越4%跌幅的次数，恢复次数，占比
    def dailydroprecov_count(ratio, stkdata, peakdate, ratio_threshold=-3.5, count_mode=0):
        ratio = ratio.loc[peakdate:].copy()
        if count_mode == 0 and len(ratio[ratio < ratio_threshold]) == 0:
            count_mode = 3
        if count_mode == 0:
            aft_presecpeak_und4_date = ratio[ratio < ratio_threshold].index
        elif len(ratio[ratio < 0]) > 0:
            ratio = ratio[ratio < 0].sort_values()
            count_mode = min(count_mode, len(ratio))
            aft_presecpeak_und4_date = ratio.iloc[:count_mode].index
        else:
            return 0, 0, 1, 0
        num_count, recov_count, recov_days = 0, 0, []
        if len(aft_presecpeak_und4_date) > 0:
            num_count = len(aft_presecpeak_und4_date)
            for date in aft_presecpeak_und4_date:
                stk_open = stkdata.loc[date, 'open']
                if len(stkdata.loc[date:]) > 1:
                    lag_date = stkdata.loc[date:].index[1]
                    if any(stkdata.loc[lag_date:, 'high'] >= stk_open):
                        recov_count += 1
                        recov_date = stkdata.loc[lag_date:][stkdata.loc[lag_date:, 'high'] >= stk_open].index[0]
                        recov_days.append(len(stkdata.loc[lag_date:recov_date]) - 1)
        return (num_count,
                recov_count,
                round(recov_count / num_count, 3) if num_count > 0 else 1,
                round(np.mean(recov_days), 3) if len(recov_days) > 0 else 0)

    def dailyrisedepres_count(ratio, stkdata, peakdate, ratio_threshold=3.5, count_mode=0):
        ratio = ratio.loc[peakdate:].copy()
        if count_mode == 0:
            aft_presecpeak_over35_date = ratio[ratio >= ratio_threshold].index
        else:
            ratio = ratio.sort_values()
            count_mode = min(count_mode, len(ratio))
            aft_presecpeak_over35_date = ratio.iloc[:count_mode].index
        num_count, depres_count, depres_days = 0, 0, []
        if len(aft_presecpeak_over35_date) > 0:
            num_count = len(aft_presecpeak_over35_date)
            for date in aft_presecpeak_over35_date:
                stk_open = stkdata.loc[date, 'open']
                if len(stkdata.loc[date:]) > 1:
                    lag_date = stkdata.loc[date:].index[1]
                    if any(stkdata.loc[lag_date:, 'low'] <= stk_open):
                        depres_count += 1
                        depres_date = stkdata.loc[lag_date:][stkdata.loc[lag_date:, 'low'] <= stk_open].index[0]
                        depres_days.append(len(stkdata.loc[lag_date:depres_date]) - 1)
        return (num_count,
                depres_count,
                round(depres_count / num_count, 3) if num_count > 0 else 1,
                round(np.mean(depres_days), 3) if len(depres_days) > 0 else 0)

    Result_Loc['Post_PreSecPeak_Und4Count'], Result_Loc['Post_PreSecPeak_RecovCount'], \
        Result_Loc['Post_PreSecPeak_RecovRatio'], Result_Loc['Post_PreSecPeak_MeanRecovDays'] = \
        dailydroprecov_count(stk_ratio, stk_data, Section_PeakDate, ratio_threshold=-3.5, count_mode=0)

    Result_Loc['Post_PreSecPeak_Over35Count'], Result_Loc['Post_PreSecPeak_DepresCount'], \
        Result_Loc['Post_PreSecPeak_DepresRatio'], Result_Loc['Post_PreSecPeak_MeanDepresDays'] = \
        dailyrisedepres_count(stk_ratio, stk_data, Section_PeakDate, ratio_threshold=3.5, count_mode=0)

    Result_Loc['Post_SecPeak_Und4Count'], Result_Loc['Post_SecPeak_RecovCount'], \
        Result_Loc['Post_SecPeak_RecovRatio'], Result_Loc['Post_SecPeak_MeanRecovDays'] = \
        dailydroprecov_count(stk_ratio, stk_data, PostSecStart_PeakDate, ratio_threshold=-3.5, count_mode=0)

    _, Result_Loc['PostSecStart_DailyDropRecov_Count'], Result_Loc['PostSecStart_DailyDropRecov_RecovRatio'], \
        Result_Loc['PostSecStart_DailyDropRecov_MeanRecovDays'] = \
        dailydroprecov_count(stk_ratio, stk_data, Section_StartDate, ratio_threshold=-2, count_mode=0)

    # 计算指标SecConcave相关
    PostSecStart_MaxClose = stk_data.loc[Section_StartDate:, 'close'].max()
    PostSecStart_MinClose = stk_data.loc[Section_StartDate:, 'close'].min()
    secconcave_threshold = 0.05

    def get_seccave_breakdate(section_serie, stk_data, max_close, min_close, threshold):
        """获取区间震荡的首个日期"""
        upper_threshold = max_close * (1 + threshold)
        lower_threshold = min_close * (1 - threshold)
        sec_first_index = stk_data.query('close>@upper_threshold').index[-1] \
            if len(stk_data.query('close>@upper_threshold')) > 0 else stk_data.index[0]
        for i in range(1, len(section_serie)):
            check_enddate = section_serie['end_date'].iloc[-i]
            check_startdate = section_serie['start_date'].iloc[-i]
            if i < len(section_serie) and \
                    (stk_data.loc[check_enddate, 'close'] > upper_threshold or
                     stk_data.loc[check_startdate, 'close'] < lower_threshold):
                if stk_data.loc[check_enddate, 'close'] > upper_threshold:
                    lag_check_enddate = section_serie['start_date'].iloc[-i + 1] \
                        if i > 1 else stk_data.index[-1]
                    sec_first_index = stk_data.loc[check_enddate:lag_check_enddate].query('close>@max_close').index[-1]
                elif stk_data.loc[check_startdate, 'close'] < lower_threshold:
                    sec_first_index = stk_data.loc[check_startdate:check_enddate].query('close<@min_close').index[-1]
                break
        return sec_first_index

    if Section_StartDate < Now_SecDate \
            and len(stk_data.loc[:Section_StartDate].query('close>@PostSecStart_MaxClose')) > 0:
        # and Result_Loc['Break_PreSec'] == "True":
        Break_SecCave_Date = get_seccave_breakdate(section_rise_origin.query('end_date<@Section_StartDate'),
                                                   stk_data,
                                                   PostSecStart_MaxClose,
                                                   PostSecStart_MinClose,
                                                   secconcave_threshold)
        secconcave_bottom = stk_data.loc[Break_SecCave_Date:, 'close'].idxmin()
    else:
        PreSecStart_PeakClose = stk_data.loc[Section_PeakDate, 'close']
        PreSecStart_MinClose = stk_data.loc[Section_PeakDate:, 'close'].min()
        Break_SecCave_Date = get_seccave_breakdate(section_rise_origin.query('end_date<@Section_PeakDate'),
                                                   stk_data,
                                                   PreSecStart_PeakClose,
                                                   PreSecStart_MinClose,
                                                   secconcave_threshold)
        secconcave_bottom = stk_data.loc[Section_PeakDate:, 'close'].idxmin()
    Result_Loc['SecConcave_StartDate'] = Break_SecCave_Date
    Result_Loc['SecConcave_LastDays'] = len(stk_data.loc[Break_SecCave_Date:]) - 1
    Result_Loc['SecConcave_BfBottom_LastDays'] = len(stk_data.loc[Break_SecCave_Date:secconcave_bottom]) - 1
    Result_Loc['SecConcave_AftBottom_LastDays'] = len(stk_data.loc[secconcave_bottom:]) - 1
    Result_Loc['SecConcave_BfBottom_TO_Sum'] = round(
        stk_data.loc[Break_SecCave_Date:secconcave_bottom, 'turnover'].sum(), 3)
    Result_Loc['SecConcave_AftBottom_TO_Sum'] = round(stk_data.loc[secconcave_bottom:, 'turnover'].sum(), 3)
    Result_Loc['SecConcave_TO_Sum'] = round(
        stk_data.loc[Break_SecCave_Date:, 'turnover'].sum(), 3)
    Result_Loc['SecConcave_RatioBand'] = round(
        (stk_data.loc[Break_SecCave_Date:, 'close'].max() /
         stk_data.loc[Break_SecCave_Date:, 'close'].min() - 1) * 100, 3)

    # Now_SecDate前后状态
    PreNow_BottomDate = Result_Loc['PreNow_BottomDate']
    PostNowSec_MaxClose = stk_data.loc[PreNow_BottomDate:, 'close'].max()
    if len(stk_data.loc[:PreNow_BottomDate].query('close>@PostNowSec_MaxClose')) > 0:
        Break_NowSecCave_Date = stk_data.loc[:PreNow_BottomDate].query('close>@PostNowSec_MaxClose').index[-1]
        Result_Loc['NowSec_SecConcave_StartDate'] = stk_data.loc[Break_NowSecCave_Date:].index[1]
        Result_Loc['NowSec_SecConcave_LastDays'] = len(stk_data.loc[Break_NowSecCave_Date:]) - 1
        Result_Loc['NowSec_SecConcave_TO_Sum'] = round(stk_data.loc[Break_NowSecCave_Date:, 'turnover'].sum(), 3)
    else:
        Result_Loc['NowSec_SecConcave_StartDate'] = stk_data.index[0]
        Result_Loc['NowSec_SecConcave_LastDays'] = len(stk_data) - 1
        Result_Loc['NowSec_SecConcave_TO_Sum'] = round(stk_data['turnover'].sum(), 3)

    # 新增指标 20241121： 下行和上行区段缓步运行判定
    prenowpeak2now_perioddata = calculate_deviation(df=stk_data.loc[PreNow_PeakDate:Now_SecDate]) \
        if len(stk_data.loc[PreNow_PeakDate:Now_SecDate]) > 2 else {}
    postnowbottom_perioddata = calculate_deviation(df=stk_data.loc[Result_Loc['PreNow_BottomDate']:]) \
        if len(stk_data.loc[Result_Loc['PreNow_BottomDate']:]) > 2 else {}
    postnowsec_perioddata = calculate_deviation(df=stk_data.loc[Now_SecDate:]) \
        if len(stk_data.loc[Now_SecDate:]) > 2 else {}
    Result_Loc['PreNowPeak2NowSec_SlowTrend_Deviation'], Result_Loc['PostPreNowBottom_SlowTrend_Deviation'], \
        Result_Loc['PostNowSec_SlowTrend_Deviation'] = 10, 10, 10

    Result_Loc['PostPreNowBottom_LastDays'] = len(stk_data.loc[Result_Loc['PreNow_BottomDate']:]) - 1

    if len(prenowpeak2now_perioddata) > 0 \
            and abs(prenowpeak2now_perioddata['sec_avgratio']) < 2 \
            and (prenowpeak2now_perioddata['upper_deviation'] < 3
                 or (prenowpeak2now_perioddata['upper_deviation'] < 10 and
                     abs(prenowpeak2now_perioddata['sec_avgratio']) < 1)) \
            and abs(prenowpeak2now_perioddata['lower_deviation']) < 10:
        Result_Loc['PreNowPeak2NowSec_SlowTrend_Deviation'] = abs(prenowpeak2now_perioddata['lower_deviation'])

    if len(postnowbottom_perioddata) > 0 \
            and abs(postnowbottom_perioddata['sec_avgratio']) < 2 \
            and postnowbottom_perioddata['upper_deviation'] < 10 \
            and abs(postnowbottom_perioddata['lower_deviation']) < postnowbottom_perioddata['sec_sumratio'] / 2:
        Result_Loc['PostPreNowBottom_SlowTrend_Deviation'] = postnowbottom_perioddata['upper_deviation']

    if len(postnowsec_perioddata) > 0 \
            and abs(postnowsec_perioddata['sec_avgratio']) < 2 \
            and postnowsec_perioddata['upper_deviation'] < 10 \
            and abs(postnowsec_perioddata['lower_deviation']) < 3:
        Result_Loc['PostNowSec_SlowTrend_Deviation'] = postnowsec_perioddata['upper_deviation']

    prenowpeak2prenow_perioddata = calculate_deviation(df=stk_data.loc[PreNow_PeakDate:PreNow_SecDate]) \
        if PreNow_PeakDate < PreNow_SecDate and len(stk_data.loc[PreNow_PeakDate:PreNow_SecDate]) > 2 else {}
    postnowbottom2prenow_perioddata = calculate_deviation(
        df=stk_data.loc[Result_Loc['PreNow_BottomDate']:PreNow_SecDate]) \
        if Result_Loc['PreNow_BottomDate'] < PreNow_SecDate \
           and len(stk_data.loc[Result_Loc['PreNow_BottomDate']:PreNow_SecDate]) > 2 else {}

    if len(prenowpeak2prenow_perioddata) > 0 \
            and abs(prenowpeak2prenow_perioddata['sec_avgratio']) < 2 \
            and (prenowpeak2prenow_perioddata['upper_deviation'] < 3
                 or (prenowpeak2prenow_perioddata['upper_deviation'] < 10 and
                     abs(prenowpeak2prenow_perioddata['sec_avgratio']) < 1)) \
            and abs(prenowpeak2prenow_perioddata['lower_deviation']) < 10:
        Result_Loc['PreNowPeak2PreNowSec_SlowTrend_Deviation'] = abs(prenowpeak2prenow_perioddata['lower_deviation'])

    if len(postnowbottom2prenow_perioddata) > 0 \
            and abs(postnowbottom2prenow_perioddata['sec_avgratio']) < 2 \
            and postnowbottom2prenow_perioddata['upper_deviation'] < 10 \
            and abs(postnowbottom2prenow_perioddata['lower_deviation']) < \
            postnowbottom2prenow_perioddata['sec_sumratio'] / 2:
        Result_Loc['PostPreNowBottom2PreNowSec_SlowTrend_Deviation'] = postnowbottom2prenow_perioddata[
            'upper_deviation']

    # 新增指标 20241215： 计算Section_StartDate至PreNow_SecDate日收益率相对归属行业指数的beta系数
    # 计算Section_StartDate至PreNow_SecDate日收益率相对归属行业指数的beta系数
    sw_index_data = get_swindex_data(start_date=Section_StartDate, end_date=PreNow_SecDate)
    sw_index_data = sw_index_data.sort_index()
    if len(stk_data.loc[Section_StartDate:PreNow_SecDate]) > 5 and Result_Loc['industry'] in sw_index_data.columns:
        # 计算个股日收益率
        stk_beta_ratio = stk_ratio.loc[Section_StartDate:PreNow_SecDate]
        # 计算行业指数日收益率
        indus_beta_ratio = sw_index_data.loc[Section_StartDate:PreNow_SecDate,
                           Result_Loc['industry']].pct_change() * 100
        # 去除首行的NaN值
        stk_beta_ratio = stk_beta_ratio.dropna()
        indus_beta_ratio = indus_beta_ratio.dropna()
        # 计算beta系数
        beta = calculate_beta(stk_beta_ratio, indus_beta_ratio)
        Result_Loc['PostSecStart2PreNowSec_Beta'] = beta
    else:
        Result_Loc['PostSecStart2PreNowSec_Beta'] = np.nan

    # 新增指标 20241220：计算股票价格在上行趋势高于移动平均线的天数占比，和下行趋势低于移动平均线的天数占比
    stk_data['mean_price'] = (stk_data['close'] + stk_data['open']) / 2
    stk_data['close_rollavg'] = stk_data['close'].rolling(window=3, closed='left').mean()
    stk_data['over_movavg'] = stk_data.apply(lambda x: 1 if x['mean_price'] >= x['close_rollavg'] else 0, axis=1)
    stk_data['under_movavg'] = stk_data.apply(lambda x: 1 if x['mean_price'] <= x['close_rollavg'] else 0, axis=1)
    stk_data['mean2movavg_rate'] = round((stk_data['mean_price'] / stk_data['close_rollavg'] - 1) * 100, 3)
    Result_Loc['Peak2Sec_Und3MovAvg_Prop'] = round(stk_data.loc[Section_PeakDate:Section_StartDate].query(
        'mean_price<close_rollavg').shape[0] / len(stk_data.loc[Section_PeakDate:Section_StartDate]), 3) \
        if len(stk_data.loc[Section_PeakDate:Section_StartDate]) > 3 else 0
    Result_Loc['PostSecStart_Over3MovAvg_Prop'] = round(stk_data.loc[Section_StartDate:].query(
        'mean_price>close_rollavg').shape[0] / len(stk_data.loc[Section_StartDate:]), 3) \
        if len(stk_data.loc[Section_StartDate:]) > 3 else 0
    stk_data['over_movavg_group'] = (stk_data['over_movavg'] != stk_data['over_movavg'].shift()).cumsum()
    stk_data['under_movavg_group'] = (stk_data['under_movavg'] != stk_data['under_movavg'].shift()).cumsum()
    if len(stk_data.loc[Section_StartDate:]) > 3:
        # 对每个分组计算连续天数，只保留over_movavg=1的组
        over_movavg_consecutive = stk_data.loc[Section_StartDate:].groupby(
            ['over_movavg', 'over_movavg_group']).size().reset_index(name='consecutive_days')
        
        # 筛选出over_movavg=1的组，并找出最大连续天数
        max_consecutive_days = over_movavg_consecutive[over_movavg_consecutive['over_movavg'] == 1]['consecutive_days'].max() \
            if len(over_movavg_consecutive[over_movavg_consecutive['over_movavg'] == 1]) > 0 else 0
            
        Result_Loc['PostSecStart_Over3MovAvg_ContiDays'] = max_consecutive_days
        Result_Loc['PostSecStart_Mean2MovAvg_MaxRate'] = round(stk_data.loc[Section_StartDate:]['mean2movavg_rate'].max(), 3)
        
    else:
        Result_Loc['PostSecStart_Over3MovAvg_ContiDays'] = 0
        Result_Loc['PostSecStart_Mean2MovAvg_MaxRate'] = 0
    
    if len(stk_data.loc[Section_PeakDate:Section_StartDate]) > 3:
        under_movavg_consecutive = stk_data.loc[Section_PeakDate:Section_StartDate].groupby(
            ['under_movavg', 'under_movavg_group']).size().reset_index(name='consecutive_days')
        max_consecutive_days = under_movavg_consecutive[under_movavg_consecutive['under_movavg'] == 1]['consecutive_days'].max() \
            if len(under_movavg_consecutive[under_movavg_consecutive['under_movavg'] == 1]) > 0 else 0
        Result_Loc['Peak2Sec_Und3MovAvg_ContiDays'] = max_consecutive_days
        Result_Loc['Peak2Sec_Und3MovAvg_MaxRate'] = round(abs(stk_data.loc[Section_PeakDate:Section_StartDate]['mean2movavg_rate'].min()), 3)
    else:
        Result_Loc['Peak2Sec_Und3MovAvg_ContiDays'] = 0
        Result_Loc['Peak2Sec_Und3MovAvg_MaxRate'] = 0
    
    # 新增指标 20250624： 计算PreNow_PeakDate前最近一个Section，Over3MovAvg_ContiDays和Prop
    if len(stk_data.loc[:PreNow_PeakDate]) > 3:
        prenow_peak_sec_startdate = section_rise_origin.loc[section_rise_origin['start_date'] < PreNow_PeakDate]['start_date'].iloc[-1]
        stk_data_prenow = stk_data.loc[prenow_peak_sec_startdate:PreNow_PeakDate]
        
        Result_Loc['PreNowPeak_PreSec_Over3MovAvg_Prop'] = round(stk_data_prenow.query(
        'mean_price>close_rollavg').shape[0] / len(stk_data_prenow), 3) \
        if len(stk_data_prenow) > 3 else 0
        # 对每个分组计算连续天数，只保留over_movavg=1的组
        prenowpeak_over_movavg_consecutive = stk_data_prenow.groupby(
            ['over_movavg', 'over_movavg_group']).size().reset_index(name='consecutive_days')
        
        # 筛选出over_movavg=1的组，并找出最大连续天数
        prenowpeak_max_consecutive_days = prenowpeak_over_movavg_consecutive[prenowpeak_over_movavg_consecutive['over_movavg'] == 1
                                                                  ]['consecutive_days'].max() \
            if len(prenowpeak_over_movavg_consecutive[prenowpeak_over_movavg_consecutive['over_movavg'] == 1]) > 0 else 0
            
        Result_Loc['PreNowPeak_PreSec_Over3MovAvg_ContiDays'] = prenowpeak_max_consecutive_days
        Result_Loc['PreNowPeak_PreSec_Mean2MovAvg_MaxRate'] = round(stk_data_prenow['mean2movavg_rate'].max(), 3)
    else:
        Result_Loc['PreNowPeak_PreSec_Over3MovAvg_Prop'] = 0
        Result_Loc['PreNowPeak_PreSec_Over3MovAvg_ContiDays'] = 0
        Result_Loc['PreNowPeak_PreSec_Mean2MovAvg_MaxRate'] = 0
    
    Result_Loc['PreNowPeak2NowSec_Und3MovAvg_MaxRate'] = round(abs(stk_data.loc[PreNow_PeakDate:Now_SecDate]['mean2movavg_rate'].min()), 3)
    
    # 新增指标 20250624： 计算Now_SecDate至今Over3MovAvg_ContiDays和Prop
    if len(stk_data.loc[Now_SecDate:]) > 3:
        stk_data_postnow = stk_data.loc[Now_SecDate:]
        Result_Loc['PostNowSec_Over3MovAvg_Prop'] = round(stk_data_postnow.query(
        'mean_price>close_rollavg').shape[0] / len(stk_data_postnow), 3) \
        if len(stk_data_postnow) > 3 else 0
        # 对每个分组计算连续天数，只保留over_movavg=1的组
        postnow_over_movavg_consecutive = stk_data_postnow.groupby(
            ['over_movavg', 'over_movavg_group']).size().reset_index(name='consecutive_days')
        
        # 筛选出over_movavg=1的组，并找出最大连续天数
        postnow_max_consecutive_days = postnow_over_movavg_consecutive[postnow_over_movavg_consecutive['over_movavg'] == 1
                                                                       ]['consecutive_days'].max() \
            if len(postnow_over_movavg_consecutive[postnow_over_movavg_consecutive['over_movavg'] == 1]) > 0 else 0
        Result_Loc['PostNowSec_Over3MovAvg_ContiDays'] = postnow_max_consecutive_days
        Result_Loc['PostNowSec_Mean2MovAvg_MaxRate'] = round(stk_data_postnow['mean2movavg_rate'].max(), 3)
    else:
        Result_Loc['PostNowSec_Over3MovAvg_Prop'] = 0
        Result_Loc['PostNowSec_Over3MovAvg_ContiDays'] = 0
        Result_Loc['PostNowSec_Mean2MovAvg_MaxRate'] = 0
    
    if len(section_rise_origin.query('start_date<@PreNow_SecDate')) > 0:
        prenowsec_startdate = section_rise_origin.query('start_date<@PreNow_SecDate')['start_date'].iloc[-1]
        stk_data_prenowsec_sec = stk_data.loc[prenowsec_startdate:PreNow_SecDate].copy()
        Result_Loc['PreNowSec_PreSec_Over3MovAvg_Prop'] = round(stk_data_prenowsec_sec.query(
            'mean_price>close_rollavg').shape[0] / len(stk_data_prenowsec_sec), 3) \
            if len(stk_data_prenowsec_sec) > 0 else 0
        prenowsec_over_movavg_consecutive = stk_data_prenowsec_sec.groupby(
            ['over_movavg', 'over_movavg_group']).size().reset_index(name='consecutive_days')
        prenowsec_max_consecutive_days = prenowsec_over_movavg_consecutive[prenowsec_over_movavg_consecutive['over_movavg'] == 1
                                                                           ]['consecutive_days'].max() \
            if len(prenowsec_over_movavg_consecutive[prenowsec_over_movavg_consecutive['over_movavg'] == 1]) > 0 else 0
        Result_Loc['PreNowSec_PreSec_Over3MovAvg_ContiDays'] = prenowsec_max_consecutive_days
        Result_Loc['PreNowSec_PreSec_Over3MovAvg_MaxRate'] = round(stk_data_prenowsec_sec['mean2movavg_rate'].max(), 3)
    else:
        Result_Loc['PreNowSec_PreSec_Over3MovAvg_Prop'] = 0
        Result_Loc['PreNowSec_PreSec_Over3MovAvg_ContiDays'] = 0
        Result_Loc['PreNowSec_PreSec_Over3MovAvg_MaxRate'] = 0
    
    Result_Loc['PreNowSec_PostSec_Und3MovAvg_MaxRate'] = round(abs(stk_data.loc[PreNow_SecDate:Now_SecDate]['mean2movavg_rate'].min()), 3)
        
    # 新增指标 20241224：计算股票三天移动平均涨跌幅ratio_movavg，并计算PreNow_PeakDate至Now_SecDate的最小值及其对应日期，
    # 以及Now_SecDate恢复到Now_SecDate前三天最高开盘价所需天数
    stk_data['ratio_movavg'] = stk_data['stk_ratio'].rolling(window=3).mean()
    if len(stk_data.loc[PreNow_PeakDate:].query('stk_ratio<0')) > 0:
        Now_SecDate_DropDate = stk_data.loc[PreNow_PeakDate:].query('stk_ratio<0').index[-1]
        stk_data_peak2sec = stk_data.loc[PreNow_PeakDate:Now_SecDate_DropDate].copy()
        if len(stk_data_peak2sec) > 0:
            stk_data_peak2sec['ratio_movavg_rank'] = stk_data_peak2sec['ratio_movavg'].rank(na_option='bottom')
            Result_Loc['PreNowPeak2NowSec_MinRatioMovAvg'] = stk_data_peak2sec['ratio_movavg'].min()
            Result_Loc['PreNowPeak2NowSec_MinRatioMovAvg_Date'] = stk_data_peak2sec['ratio_movavg'].idxmin()
            Result_Loc['PreNowPeak2NowSec_RatioMovAvg_NowSecRank'] = stk_data_peak2sec['ratio_movavg_rank'].iloc[-1] \
                if not pd.isna(stk_data_peak2sec['ratio_movavg'].iloc[-1]) else 999
    else:
        print('PreNow_PeakDate至Now_SecDate间无下跌日期:', Result_Loc['ts_code'], Result_Loc['PreNow_PeakDate'],
              Result_Loc['Now_SecDate'])

    # 计算Now_SecDate恢复到Now_SecDate前2天最高开盘价所需天数
    Now_SecDate_TopOpen = max(stk_data.loc[:Now_SecDate].iloc[-2:]['open'].max(), 
                              stk_data.loc[:Now_SecDate].iloc[-2:]['close'].max())
    Now_SecDate_TopOpen_Cls = stk_data.loc[stk_data.loc[:Now_SecDate].iloc[-2:]['open'].idxmax(), 'close']
    # 找到首个最高价超越Now_SecDate_TopOpen的日期
    if len(stk_data.loc[Now_SecDate:]) > 1:
        recover_dates = stk_data.loc[Now_SecDate:].iloc[1:].query('close>=@Now_SecDate_TopOpen').index
        Result_Loc['PostNowSec_Recover_TopOpen_Days'] = len(stk_data.loc[Now_SecDate:recover_dates[0]]) - 1 \
        if len(recover_dates) > 0 else -1
        Result_Loc['PostNowSec_Recover_TopOpen_Date'] = recover_dates[0] if len(recover_dates) > 0 else '-'
        Result_Loc['PostNowSec_Recover_TopOpen_Close'] = Now_SecDate_TopOpen_Cls
        Result_Loc['PostNowSec_Recover_TopOpen_Open'] = Now_SecDate_TopOpen

    # 新增指标 20241225：计算Section_StartDate后最近LastDays超过10天的Sec开始日期
    # 筛选条件
    rise_sec_filter = (section_rise_origin['end_date'] > Section_StartDate) & \
                      (section_rise_origin['lastdays'] >= 10) & \
                      (section_rise_origin['avgratio'] < 3)

    # 获取符合条件的最后一个start_date
    Result_Loc['PostSecStart_RiseSecOver10Days_StartDate'] = \
        section_rise_origin.loc[rise_sec_filter, 'start_date'].iloc[-1] \
            if rise_sec_filter.any() else '-'
    Result_Loc['PostSecStart_RiseSecOver10Days_EndDate'] = \
        section_rise_origin.loc[rise_sec_filter, 'end_date'].iloc[-1] \
            if rise_sec_filter.any() else '-'
    # 筛选条件
    drop_sec_filter = (section_d['start_date'] < Now_SecDate) & \
                      (section_d['lastdays'] >= 10) & \
                      (section_d['avgratio'] > -2)

    # 获取符合条件的最后一个start_date
    Result_Loc['PreNowSec_DropSecOver10Days_StartDate'] = \
        section_d.loc[drop_sec_filter, 'start_date'].iloc[-1] \
            if drop_sec_filter.any() else '-'
    Result_Loc['PreNowSec_DropSecOver10Days_EndDate'] = \
        section_d.loc[drop_sec_filter, 'end_date'].iloc[-1] \
            if drop_sec_filter.any() else '-'

    # 新增指标 20241225：计算PostSecStart_PeakDate前一个Sec的Turnover合计值，以及PostSecStart_PeakDate至最新日期的Turnover合计值
    if PostSecStart_PeakDate != '-' and pd.notna(PostSecStart_PeakDate):
        sec_prepeak_startdate = \
        section_rise_origin.loc[section_rise_origin['start_date'] < PostSecStart_PeakDate]['start_date'].iloc[-1]
        Result_Loc['Pre_PostSecPeak_Sec_SumTO'] = round(
            stk_data.loc[sec_prepeak_startdate:PostSecStart_PeakDate, 'turnover'].iloc[1:].sum(), 3) \
            if len(stk_data.loc[sec_prepeak_startdate:PostSecStart_PeakDate]) > 1 else 0
        Result_Loc['PostSecPeak2Now_SumTO'] = round(stk_data.loc[PostSecStart_PeakDate:, 'turnover'].iloc[1:].sum(), 3) \
            if len(stk_data.loc[PostSecStart_PeakDate:]) > 1 else 0
        pre_postsecpeak_support_price = stk_data.loc[:PostSecStart_PeakDate, 'open'].iloc[-4:].min()
        breach_date = stk_data.loc[PostSecStart_PeakDate:].query('close<@pre_postsecpeak_support_price').index[0] \
            if len(stk_data.loc[PostSecStart_PeakDate:].query('close<@pre_postsecpeak_support_price')) > 0 else '-'
        Result_Loc['PostSecPeak_Support_Breach_CostDays'] = len(stk_data.loc[PostSecStart_PeakDate:breach_date]) - 1 \
            if breach_date != '-' else -1

    # 新增指标 20250101： 计算PreTurn_PeakDate至Period_TurnDate，以及Period_TurnDate至最新日期, 平均Section持续天数
    # 计算PreTurn_PeakDate至Period_TurnDate间的Section统计
    if turn_date != '-' and turn_date != '-' and pd.notna(turn_date) and pd.notna(turn_date):
        # 获取区间内的section
        peak2turn_sections = section_d.query('end_date>=@PreTurn_PeakDate & start_date<=@turn_date').copy()

        # 计算区间内section数量
        Peak2Turn_SecNum = len(peak2turn_sections)

        # 计算区间总天数
        total_days = len(stk_data.loc[PreTurn_PeakDate:turn_date])

        # 计算平均每个section占用天数
        Result_Loc['Peak2Turn_AvgSecDays'] = round(total_days / len(peak2turn_sections), 2) if len(
            peak2turn_sections) > 0 else 0
    else:
        Result_Loc['Peak2Turn_AvgSecDays'] = 0

    # 计算turn_date至最新日期的Section平均占用天数
    if turn_date != '-' and pd.notna(turn_date):
        # 获取区间内的section
        turn2now_sections = section_rise_origin.query('end_date>=@turn_date & start_date<=@Now_SecDate').copy()

        # 计算区间总天数
        total_days = len(stk_data.loc[turn_date:])

        # 计算平均每个section占用天数
        Result_Loc['Turn2Now_AvgSecDays'] = round(total_days / len(turn2now_sections), 2) if len(
            turn2now_sections) > 0 else 0
    else:
        Result_Loc['Turn2Now_AvgSecDays'] = 0

    # 新增指标 20250106：分别计算Section_PeakDate至Section_StartDate，Section_StartDate至Now_SecDate，
    # 以及PreNow_BottomDate至PostNowSec_PeakDate的slowtrend特征
    Peak2Sec_SlowTrend = cal_periodtrend_deviation(
        stk_data, start_date=Section_PeakDate, end_date=Section_StartDate)
    Result_Loc['Peak2Sec_STrend'] = 1 \
        if Peak2Sec_SlowTrend['Check_Trend_ConsecuState'] == 'True' or \
           Peak2Sec_SlowTrend['Check_TrendCOR_ConsecuState'] == 'True' else 0
    SecStart2Now_SlowTrend = cal_periodtrend_deviation(
        stk_data, start_date=Section_StartDate, end_date=None)
    Result_Loc['SecStart2Now_STrend'] = 1 \
        if SecStart2Now_SlowTrend['Check_Trend_ConsecuState'] == 'True' or \
           SecStart2Now_SlowTrend['Check_TrendCOR_ConsecuState'] == 'True' else 0
    PreNowBottom2PostNowPeak_SlowTrend = cal_periodtrend_deviation(
        stk_data, start_date=Result_Loc['PreNow_BottomDate'], end_date=Result_Loc['PostNowSec_PeakDate'])
    Result_Loc['PreNowBottom2PostNowPeak_STrend'] = 1 \
        if PreNowBottom2PostNowPeak_SlowTrend['Check_Trend_ConsecuState'] == 'True' and \
           PreNowBottom2PostNowPeak_SlowTrend['Check_TrendCOR_ConsecuState'] == 'True' else 0
    Result_Loc['PreNowBottom2PostNowPeak_STrend_UpDev'] = \
        PreNowBottom2PostNowPeak_SlowTrend['Check_Trend_UpperDeviation']
    Result_Loc['PreNowBottom2PostNowPeak_STrend_LowDev'] = \
        PreNowBottom2PostNowPeak_SlowTrend['Check_Trend_LowerDeviation']

    # 新增指标 20250110：统计section中trend_consistency和channel_stability
    # Result_Loc['PostSecStart_TrendConsis_Num'] = section_rise_origin.query('end_date>@Section_StartDate')['trend_consistency'].sum()
    Result_Loc['PostSecStart_SectionStable_Num'] = section_rise_origin.query('end_date>@Section_StartDate')[
                                                       'section_stability'].sum() + \
                                                   section_drop.query('start_date>@Section_StartDate')[
                                                       'section_stability'].sum()
    # 最近section_stability为True的section距离Now_SecDate数量
    risesec_stable_section_enddate = section_rise_origin.query('section_stability==True')['end_date'].iloc[-1] \
        if len(section_rise_origin.query('section_stability==True')) > 0 else '-'
    Result_Loc['RiseSec_SectionStable2NowSec_Days'] = len(stk_data.loc[risesec_stable_section_enddate:Now_SecDate]) - 1 \
        if risesec_stable_section_enddate != '-' and risesec_stable_section_enddate < Now_SecDate else -1
    dropsec_stable_section_enddate = section_drop.query('section_stability==True')['end_date'].iloc[-1] \
        if len(section_drop.query('section_stability==True')) > 0 else '-'
    Result_Loc['DropSec_SectionStable2NowSec_Days'] = len(stk_data.loc[dropsec_stable_section_enddate:Now_SecDate]) - 1 \
        if dropsec_stable_section_enddate != '-' and dropsec_stable_section_enddate < Now_SecDate else -1

    # 新增指标20250115：PreSec的SetionStable状态
    Result_Loc['PreNowSec_Trend_Consistency'] = section_d.query('start_date<@Now_SecDate')['trend_consistency'].iloc[-1] \
        if len(section_d.query('start_date<@Now_SecDate')) > 0 else False
    Result_Loc['PreNowSec_Channel_Stability'] = section_d.query('start_date<@Now_SecDate')['section_stability'].iloc[-1] \
        if len(section_d.query('start_date<@Now_SecDate')) > 0 else False

    # 新增指标20250122：计算Now_SecDate前一下行Section的Turnover2Change的排名位置
    Result_Loc['PreNowSec_Turnover2Change'] = round(
        section_d.query('start_date<@Now_SecDate')['turnover2change'].iloc[-1], 3) \
        if len(section_d.query('start_date<@Now_SecDate')) > 0 else 0
    if Section_StartDate == Now_SecDate:
        section_drop_data = section_d.query('start_date<@Now_SecDate & start_date>=@Section_PeakDate').copy()
        if len(section_drop_data) > 0:
            section_drop_data['turnover2change_rank'] = section_drop_data['turnover2change'].rank(method='first',
                                                                                                  ascending=False)
            Result_Loc['PreNowSec_Turnover2Change_Rank'] = section_drop_data['turnover2change_rank'].iloc[-1]
        else:
            Result_Loc['PreNowSec_Turnover2Change_Rank'] = 99
    elif Now_SecDate < PostSecStart_PeakDate:
        section_drop_data = section_d.query('start_date<@Now_SecDate & start_date>=@Section_StartDate').copy()
        if len(section_drop_data) > 0:
            section_drop_data['turnover2change_rank'] = section_drop_data['turnover2change'].rank(method='first',
                                                                                                  ascending=False)
            Result_Loc['PreNowSec_Turnover2Change_Rank'] = section_drop_data['turnover2change_rank'].iloc[-1]
        else:
            Result_Loc['PreNowSec_Turnover2Change_Rank'] = 99
    else:
        Result_Loc['PreNowSec_Turnover2Change_Rank'] = 99

    # 新增指标 20250129：计算PreNow_PeakDate至Now_SecDate的日收益率均值与上方标准差之和，以及PreNow_BottomDate至最新日期的日收益率与下方标准差之差
    prenowpeak2now_riseindex = stk_data.loc[PreNow_SecDate:Now_SecDate].iloc[1:].query('stk_ratio>0').index
    if len(prenowpeak2now_riseindex) > 0:
        Result_Loc['PreNowSec2NowSec_MeanStdSum'] = round(
            stk_data.loc[PreNow_SecDate:Now_SecDate, 'stk_ratio'].iloc[1:].mean() +
            stk_data.loc[prenowpeak2now_riseindex, 'stk_ratio'].std(), 2)
    else:
        Result_Loc['PreNowSec2NowSec_MeanStdSum'] = round(
            stk_data.loc[PreNow_SecDate:Now_SecDate, 'stk_ratio'].iloc[1:].mean(), 2)
    nowbottom2now_dropindex = stk_data.loc[Result_Loc['PreNow_BottomDate']:].iloc[1:].query('stk_ratio<0').index
    if len(nowbottom2now_dropindex) > 0:
        Result_Loc['PreNowBottom2Now_MeanStdDiff'] = round(
            stk_data.loc[Result_Loc['PreNow_BottomDate']:, 'stk_ratio'].iloc[1:].mean() -
            stk_data.loc[nowbottom2now_dropindex, 'stk_ratio'].std(), 2)
    else:
        Result_Loc['PreNowBottom2Now_MeanStdDiff'] = round(
            stk_data.loc[Result_Loc['PreNow_BottomDate']:, 'stk_ratio'].iloc[1:].mean(), 2)
    
    # 新增指标 20250226：计算PreNow_SecDate至当前日期的最小日收益率对应日期，及恢复至该日开盘价以上的日期
    Result_Loc['PostPreNow_MinDailyRatio_Recover_Date'] = '-'
    Result_Loc['PostPreNow_MinDailyRatio_Recover_Days'] = 99
    Result_Loc['PostPreNow_MinDailyRatio_Recover_State'] = 0
    if PreNow_SecDate != '-':
        min_dailyratio_date = stk_data.loc[PreNow_SecDate:]['stk_ratio'].idxmin()
        Result_Loc['PostPreNow_MinDailyRatio_Date'] = min_dailyratio_date
        Result_Loc['PostPreNow_MinDailyRatio'] = stk_data.loc[min_dailyratio_date, 'stk_ratio']
        mindailyratio_open = stk_data.loc[min_dailyratio_date, 'open']
        if len(stk_data.loc[Now_SecDate:]) > 1:
            max_close = stk_data.loc[Now_SecDate:]['close'].max()
            if max_close > mindailyratio_open:
                Result_Loc['PostPreNow_MinDailyRatio_Recover_Date'] = stk_data.loc[Now_SecDate:].query(
                    'close>@mindailyratio_open').index[0]
                Result_Loc['PostPreNow_MinDailyRatio_Recover_Days'] = len(
                    stk_data.loc[min_dailyratio_date:Result_Loc['PostPreNow_MinDailyRatio_Recover_Date']]) - 1
                if Result_Loc['PostPreNow_MinDailyRatio_Recover_Date'] == stk_data.index[-1]:
                    Result_Loc['PostPreNow_MinDailyRatio_Recover_State'] = 1
    # 新增指标 20250226：计算Section_PeakDate至当前日期的最小日收益率对应日期，及恢复至该日开盘价以上的日期
    Result_Loc['PostSecPeak_MinDailyRatio_Recover_Date'] = '-'
    Result_Loc['PostSecPeak_MinDailyRatio_Recover_Days'] = 99
    Result_Loc['PostSecPeak_MinDailyRatio_Recover_State'] = 0
    if Section_PeakDate != '-':
        min_dailyratio_date = stk_data.loc[Section_PeakDate:]['stk_ratio'].idxmin()
        Result_Loc['PostSecPeak_MinDailyRatio_Date'] = min_dailyratio_date
        Result_Loc['PostSecPeak_MinDailyRatio'] = stk_data.loc[min_dailyratio_date, 'stk_ratio']
        mindailyratio_open = stk_data.loc[min_dailyratio_date, 'open']
        if len(stk_data.loc[Now_SecDate:]) > 1:
            max_close = stk_data.loc[Now_SecDate:]['close'].max()
            if max_close > mindailyratio_open:
                Result_Loc['PostSecPeak_MinDailyRatio_Recover_Date'] = stk_data.loc[Now_SecDate:].query(
                    'close>@mindailyratio_open').index[0]
                Result_Loc['PostSecPeak_MinDailyRatio_Recover_Days'] = len(
                    stk_data.loc[min_dailyratio_date:Result_Loc['PostSecPeak_MinDailyRatio_Recover_Date']]) - 1
                if Result_Loc['PostSecPeak_MinDailyRatio_Recover_Date'] == stk_data.index[-1]:
                    Result_Loc['PostSecPeak_MinDailyRatio_Recover_State'] = 1

    # 新增指标 20250304：计算Now_SecDate下轨覆盖天数
    Result_Loc['NowSec_LowBand_CoverDays'] = 0
    Result_Loc['NowSec2SecStart_Ratio'] = 0
    if Now_SecDate != '-':
        nowsec_close = stk_data.loc[Now_SecDate, 'close']
        stk_data_bfnowsec = stk_data.loc[:Now_SecDate].iloc[:-1].copy()
        under_nowsec_date = stk_data_bfnowsec.query('close<@nowsec_close').index[-1] \
            if len(stk_data_bfnowsec.query('close<@nowsec_close')) > 0 else stk_data_bfnowsec.index[0]
        Result_Loc['NowSec_LowBand_CoverDays'] = len(stk_data.loc[under_nowsec_date:Now_SecDate]) - 1
        Result_Loc['NowSec2SecStart_Ratio'] = round(
            (stk_data.loc[Now_SecDate, 'close'] / stk_data.loc[Section_StartDate, 'close'] - 1) * 100, 3) \
            if Section_StartDate != '-' and Section_StartDate != '-' \
                and pd.notna(Section_StartDate) and pd.notna(Section_StartDate) else 0
    
    # 新增指标 20250310：计算近三天最高收盘价向前覆盖天数和近三天最低收盘价向前覆盖天数
    postnowsec_maxclose = stk_data.loc[Now_SecDate:, 'close'].max()
    postnowsec_maxclose_index = stk_data.loc[Now_SecDate:, 'close'].idxmax()
    postnowsec_minclose = stk_data.loc[Now_SecDate:, 'close'].min()
    postnowsec_minclose_index = stk_data.loc[Now_SecDate:, 'close'].idxmin()
    if len(stk_data.loc[:postnowsec_maxclose_index].query('close>@postnowsec_maxclose')) > 0:
        cover_index = stk_data.loc[:postnowsec_maxclose_index].query('close>@postnowsec_maxclose').index[-1]
        Result_Loc['NowSec_MaxClose_UpCoverDays'] = len(stk_data.loc[cover_index:]) - 1
    else:
        Result_Loc['NowSec_MaxClose_UpCoverDays'] = len(stk_data) - 1
    if len(stk_data.loc[:postnowsec_minclose_index].query('close<@postnowsec_minclose')) > 0:
        cover_index = stk_data.loc[:postnowsec_minclose_index].query('close<@postnowsec_minclose').index[-1]
        Result_Loc['NowSec_MinClose_DownCoverDays'] = len(stk_data.loc[cover_index:]) - 1
    else:
        Result_Loc['NowSec_MinClose_DownCoverDays'] = len(stk_data) - 1
    
    postsec_maxclose = stk_data.loc[Section_StartDate:, 'close'].max()
    postsec_maxclose_index = stk_data.loc[Section_StartDate:, 'close'].idxmax()
    postsec_minclose = stk_data.loc[Section_StartDate:, 'close'].min()
    postsec_minclose_index = stk_data.loc[Section_StartDate:, 'close'].idxmin()
    if len(stk_data.loc[:postsec_maxclose_index].query('close>@postsec_maxclose')) > 0:
        cover_index = stk_data.loc[:postsec_maxclose_index].query('close>@postsec_maxclose').index[-1]
        Result_Loc['SecStart_MaxClose_UpCoverDays'] = len(stk_data.loc[cover_index:]) - 1
    else:
        Result_Loc['SecStart_MaxClose_UpCoverDays'] = len(stk_data) - 1
    if len(stk_data.loc[:postsec_minclose_index].query('close<@postsec_minclose')) > 0:
        cover_index = stk_data.loc[:postsec_minclose_index].query('close<@postsec_minclose').index[-1]
        Result_Loc['SecStart_MinClose_DownCoverDays'] = len(stk_data.loc[cover_index:]) - 1
    else:
        Result_Loc['SecStart_MinClose_DownCoverDays'] = len(stk_data) - 1
    
    Result_Loc['PostSecPeak_DropSec_Num'] = len(section_drop.query('end_date>@PostSecStart_PeakDate')) if \
        pd.notnull(PostSecStart_PeakDate) and PostSecStart_PeakDate != '-' else 0
    
    stk_data['min_opencls'] = stk_data[['open', 'close']].min(axis=1)
    stk_data['max_opencls'] = stk_data[['open', 'close']].max(axis=1)
    stk_data['pre_min_opencls'] = stk_data['min_opencls'].shift(1)
    stk_data['pre_max_opencls'] = stk_data['max_opencls'].shift(1)
    stk_data_peak2sec = stk_data.loc[Section_PeakDate:Section_StartDate].query('close>pre_max_opencls').copy()
    if len(stk_data_peak2sec) > 1:
        stk_data_peak2sec = stk_data_peak2sec.iloc[1:].copy()
        Result_Loc['Peak2Sec_OverPre1Day_Days'] = len(stk_data_peak2sec)
        Result_Loc['Peak2Sec_OverPre1Day_Prop'] = round(
            len(stk_data_peak2sec)/len(stk_data.loc[Section_PeakDate:Section_StartDate]), 3)
        stk_data_peak2sec['over_pre_max_opencls_ratio'] = round(abs(
            stk_data_peak2sec['close'] / stk_data_peak2sec['pre_max_opencls'] - 1) * 100, 3)
        Result_Loc['Peak2Sec_OverPre1Day_MeanRatio'] = round(stk_data_peak2sec['over_pre_max_opencls_ratio'].mean(), 3)
    else:
        Result_Loc['Peak2Sec_OverPre1Day_Days'] = 0
        Result_Loc['Peak2Sec_OverPre1Day_Prop'] = 0
        Result_Loc['Peak2Sec_OverPre1Day_MeanRatio'] = 0
    
    stk_data_prenowpeak2now = stk_data.loc[PreNow_PeakDate:Now_SecDate].query('close>pre_max_opencls').copy()
    if len(stk_data_prenowpeak2now) > 1:
        stk_data_prenowpeak2now = stk_data_prenowpeak2now.iloc[1:].copy()
        Result_Loc['PreNowPeak2Now_OverPre1Day_Days'] = len(stk_data_prenowpeak2now)
        Result_Loc['PreNowPeak2Now_OverPre1Day_Prop'] = round(
            len(stk_data_prenowpeak2now)/len(stk_data.loc[PreNow_PeakDate:Now_SecDate]), 3)
        stk_data_prenowpeak2now['over_pre_max_opencls_ratio'] = round(abs(
            stk_data_prenowpeak2now['close'] / stk_data_prenowpeak2now['pre_max_opencls'] - 1) * 100, 3)
        Result_Loc['PreNowPeak2Now_OverPre1Day_MeanRatio'] = round(stk_data_prenowpeak2now['over_pre_max_opencls_ratio'].mean(), 3)
    else:
        Result_Loc['PreNowPeak2Now_OverPre1Day_Days'] = 0
        Result_Loc['PreNowPeak2Now_OverPre1Day_Prop'] = 0
        Result_Loc['PreNowPeak2Now_OverPre1Day_MeanRatio'] = 0
    
    stk_data_postsecstart = stk_data.loc[Section_StartDate:].query('close<pre_min_opencls').copy()
    if len(stk_data_postsecstart) > 1:
        stk_data_postsecstart = stk_data_postsecstart.iloc[1:].copy()
        Result_Loc['PostSecStart_BelowPre1Day_Days'] = len(stk_data_postsecstart)
        Result_Loc['PostSecStart_BelowPre1Day_Prop'] = round(
            len(stk_data_postsecstart)/len(stk_data.loc[Section_StartDate:]), 3)
        stk_data_postsecstart['below_pre_min_opencls_ratio'] = round(abs(
            stk_data_postsecstart['close'] / stk_data_postsecstart['pre_min_opencls'] - 1) * 100, 3)
        Result_Loc['PostSecStart_BelowPre1Day_MeanRatio'] = round(stk_data_postsecstart['below_pre_min_opencls_ratio'].mean(), 3)
    else:
        Result_Loc['PostSecStart_BelowPre1Day_Days'] = 0
        Result_Loc['PostSecStart_BelowPre1Day_Prop'] = 0
        Result_Loc['PostSecStart_BelowPre1Day_MeanRatio'] = 0
    
    stk_data_postnowsec = stk_data.loc[Now_SecDate:].query('close<pre_min_opencls').copy()
    if len(stk_data_postnowsec) > 1:
        stk_data_postnowsec = stk_data_postnowsec.iloc[1:].copy()
        Result_Loc['PostNowSec_BelowPre1Day_Days'] = len(stk_data_postnowsec)
        Result_Loc['PostNowSec_BelowPre1Day_Prop'] = round(
            len(stk_data_postnowsec)/len(stk_data.loc[Now_SecDate:]), 3)
        stk_data_postnowsec['below_pre_min_opencls_ratio'] = round(abs(
            stk_data_postnowsec['close'] / stk_data_postnowsec['pre_min_opencls'] - 1) * 100, 3)
        Result_Loc['PostNowSec_BelowPre1Day_MeanRatio'] = round(stk_data_postnowsec['below_pre_min_opencls_ratio'].mean(), 3)
    else:
        Result_Loc['PostNowSec_BelowPre1Day_Days'] = 0
        Result_Loc['PostNowSec_BelowPre1Day_Prop'] = 0
        Result_Loc['PostNowSec_BelowPre1Day_MeanRatio'] = 0
    
    section_drop_postsec = section_d.query('start_date>=@Section_StartDate').copy()
    section_drop_prenowsec = section_d.query('start_date<@Now_SecDate').iloc[-1] if len(
        section_d.query('start_date<@Now_SecDate')) > 0 else None
    if section_drop_postsec is not None and len(section_drop_postsec) > 0 and \
            section_drop_prenowsec is not None :
        Result_Loc['SecAvgRatio_PreNowSec2PostSecDrop_Ratio'] = round(
            abs(section_drop_prenowsec['avgratio']) / abs(section_drop_postsec['avgratio']).mean(), 3)
    else:
        Result_Loc['SecAvgRatio_PreNowSec2PostSecDrop_Ratio'] = 0
    
    section_drop_postsecpeak = section_d.query('start_date>=@Section_PeakDate & start_date<=@Section_StartDate').copy()
    section_drop_presec = section_d.query('start_date<@Section_StartDate').iloc[-1] if len(
        section_d.query('start_date<@Section_PeakDate')) > 0 else None
    if section_drop_postsecpeak is not None and len(section_drop_postsecpeak) > 0 and \
            section_drop_presec is not None:
        Result_Loc['SecAvgRatio_PreSec2PostSecPeakDrop_Ratio'] = round(
            abs(section_drop_presec['avgratio']) / abs(section_drop_postsecpeak['avgratio']).mean(), 3)
    else:
        Result_Loc['SecAvgRatio_PreSec2PostSecPeakDrop_Ratio'] = 0

    if len(stk_data.loc[Section_StartDate:]) > 5:
        turnpoint_date, absolut_change, relative_change, postturnpoint_days = \
            cal_turnpoint(df_data=stk_data.loc[Section_StartDate:],
                          stk_indicator='turnover',
                          show_info=False)
        Result_Loc['PostSec_TO_TurnP_Date'] = turnpoint_date
        Result_Loc['PostSec_TO_TurnP_AbsChange'] = round(absolut_change, 3)
        Result_Loc['PostSec_TO_TurnP_RelaChange'] = round(relative_change, 3)
        Result_Loc['PostSec_TO_PostTurnP_LastDays'] = postturnpoint_days
    
    if len(stk_data.loc[Section_PeakDate:Section_StartDate]) > 5:
        turnpoint_date, absolut_change, relative_change, postturnpoint_days = \
            cal_turnpoint(df_data=stk_data.loc[Section_PeakDate:Section_StartDate],
                          stk_indicator='turnover',
                          show_info=False)
        Result_Loc['Peak2Sec_TO_TurnP_Date'] = turnpoint_date
        Result_Loc['Peak2Sec_TO_TurnP_AbsChange'] = round(absolut_change, 3)
        Result_Loc['Peak2Sec_TO_TurnP_RelaChange'] = round(relative_change, 3)
        Result_Loc['Peak2Sec_TO_PostTurnP_LastDays'] = postturnpoint_days
    
    # 计算section_peakdate前上行区段持续天数
    section_peak_pre = section_rise_origin.query('start_date<@Section_PeakDate').copy()
    Section_BottomDate = stk_data.loc[:Section_PeakDate, 'close'].idxmin()

    if len(section_peak_pre) > 0:
        lnum = -2
        while len(section_peak_pre) > 1 and abs(lnum) <= len(section_peak_pre) \
                and stk_data.loc[section_peak_pre['end_date'].iloc[lnum + 1], 'close'
        ] > stk_data.loc[section_peak_pre['end_date'].iloc[lnum], 'close']:
            lnum -= 1
        Section_BottomDate = section_peak_pre['start_date'].iloc[lnum + 1] \
            if len(section_peak_pre) > 1 else section_peak_pre['start_date'].iloc[-1]
    # elif len(section_drop) > 0:
    #     secPeak_drop = section_drop.query('start_date<@Section_StartDate')
    #     if len(secPeak_drop) > 1:
    #         lnum = -2
    #         while len(secPeak_drop) > 1 and abs(lnum) + 1 < len(secPeak_drop) \
    #                 and stk_data.loc[secPeak_drop['start_date'].iloc[lnum], 'close'
    #         ] > stk_data.loc[secPeak_drop['start_date'].iloc[lnum + 1], 'close']:
    #             lnum -= 1
    #         Section_PeakDate = secPeak_drop['start_date'].iloc[lnum + 1] \
    #             if len(secPeak_drop) > 1 else secPeak_drop['start_date'].iloc[-1]
    Result_Loc['Section_BottomDate'] = Section_BottomDate
    Result_Loc['SectionPeak_PreSecRise_LastDays'] = len(stk_data.loc[Section_BottomDate:Section_PeakDate]) - 1
    Result_Loc['SectionPeak_PreSecRise_SumRatio'] = round(
        (stk_data.loc[Section_PeakDate, 'close'] / stk_data.loc[Section_BottomDate, 'close'] - 1) * 100, 3)
    Result_Loc['SectionPeak_PreSecRise_AvgRatio'] = round(
        Result_Loc['SectionPeak_PreSecRise_SumRatio'] / Result_Loc['SectionPeak_PreSecRise_LastDays'], 3) \
        if Result_Loc['SectionPeak_PreSecRise_LastDays'] != 0 else 0
    
    Result_Loc['SectionPeak_PrePostDays_Ratio'] = round(
        Result_Loc['Peak2Sec_LastDays'] / Result_Loc['SectionPeak_PreSecRise_LastDays'], 3) \
        if Result_Loc['SectionPeak_PreSecRise_LastDays'] != 0 else 0
    Result_Loc['SectionPeak_PrePostSum_Ratio'] = round(abs(
        Result_Loc['Peak2Sec_SumRatio'] / Result_Loc['SectionPeak_PreSecRise_SumRatio']), 3) \
        if Result_Loc['SectionPeak_PreSecRise_SumRatio'] != 0 else 0
    Result_Loc['SectionPeak_PrePostAvg_Ratio'] = round(abs(
        Result_Loc['Peak2Sec_AvgRatio'] / Result_Loc['SectionPeak_PreSecRise_AvgRatio']), 3) \
        if Result_Loc['SectionPeak_PreSecRise_AvgRatio'] != 0 else 0
    
    sec2prenowpeak_days = len(stk_data.loc[Section_StartDate:PreNow_PeakDate]) - 1 if Section_StartDate<PreNow_PeakDate else 0
    sec2prenowpeak_sumratio = (stk_data.loc[Section_StartDate:Now_SecDate, 'close'].max() / 
                               stk_data.loc[Section_StartDate, 'close'] - 1) * 100 if Section_StartDate<PreNow_PeakDate else 0
    sec2prenowpeak_avgratio = sec2prenowpeak_sumratio / sec2prenowpeak_days if sec2prenowpeak_days != 0 else 0
    Result_Loc['PreNowPeak_PrePostDays_Ratio'] = round(
        Result_Loc['PreNowPeak2NowSec_LastDays'] / sec2prenowpeak_days , 3) \
        if sec2prenowpeak_days != 0 else 0
    Result_Loc['PreNowPeak_PrePostSum_Ratio'] = round(abs(
        Result_Loc['PreNowPeak2Now_SumRatio'] / sec2prenowpeak_sumratio), 3) \
        if sec2prenowpeak_sumratio != 0 and sec2prenowpeak_days != 0 else 0
    Result_Loc['PreNowPeak_PrePostAvg_Ratio'] = round(abs(
        Result_Loc['PreNowPeak2Now_AvgRatio']/ sec2prenowpeak_avgratio), 3) \
        if sec2prenowpeak_avgratio != 0 else 0
        
    Result_Loc['PostNowSec_MaxCls_Percentile_PostTurn'] = calculate_extreme_percentile_band(
                        stk_data.loc[Now_SecDate:, 'close'].max(),
                        stk_data.loc[turn_date:Now_SecDate, 'close'])
    
    return Result_Loc


def calculate_beta(stock_returns, index_returns):
    """计算单只股票相对行业指数的beta系数
    
    参数:
    stock_returns: 股票日收益率序列
    index_returns: 行业指数日收益率序列
    
    返回:
    beta: beta系数
    """
    import numpy as np
    # 对齐数据
    common_dates = stock_returns.index.intersection(index_returns.index)
    if len(common_dates) < 2:  # 至少需要2个数据点才能计算beta
        return np.nan

    # 按日期排序
    common_dates = sorted(common_dates)
    stock_returns = stock_returns[common_dates]
    index_returns = index_returns[common_dates]

    # 移除缺失值
    valid_mask = ~(np.isnan(stock_returns) | np.isnan(index_returns))
    stock_returns = stock_returns[valid_mask]
    index_returns = index_returns[valid_mask]

    if len(stock_returns) < 2:  # 再次检查有效数据点
        return np.nan

    # 计算beta
    try:
        covariance = np.cov(stock_returns, index_returns)[0, 1]
        index_variance = np.var(index_returns)
        beta = covariance / index_variance if index_variance != 0 else np.nan
        return beta
    except:
        return np.nan


def cal_periodtrend_deviation(stk_data, start_date=None, end_date=None):
    """计算指定区间的deviation数据
    
    主要功能:
    - 计算指定区间内的偏离度指标
    - 判断股价走势是否维持缓步上行或下行
    - 统计连续上涨/下跌天数和区间特征
    
    参数:
    stk_data: 股票历史行情数据
    start_date: 起始日期,可选
    end_date: 结束日期,可选
    
    返回:
    dict: 包含deviation计算结果的字典"""
    trend_result = {
        'Check_Trend_ConsecuState': '-',
        'Check_TrendCOR_ConsecuState': '-',
        'Check_Trend_Ma3Prop': 0,
        'Check_TrendCOR_Prop': 0,
        'Check_Trend_UpperDeviation': 0,
        'Check_Trend_LowerDeviation': 0,
    }

    if start_date is None:
        start_date = stk_data.index[0]
    if end_date is None:
        end_date = stk_data.index[-1]

    min_threshold = min(5, len(stk_data) - 1)

    if len(stk_data) > 0:
        # 判断是上行还是下行趋势
        first_price = stk_data.loc[start_date, 'close']
        last_price = stk_data.loc[end_date, 'close']
        is_uptrend = last_price > first_price

        period_deviation = calculate_deviation(stk_data.copy(), start_date=start_date, end_date=end_date)
        trend_consecutive = cal_trend_consecutive(stk_data.copy(), start_date=start_date, end_date=end_date)

        if len(period_deviation) > 0:
            if is_uptrend:
                # 上行缓步运行判定条件
                if (trend_consecutive['ma3_max_consecutive_days'] >= min_threshold
                        and trend_consecutive['ma3_prop'] >= 0.8):
                    trend_result['Check_Trend_ConsecuState'] = 'True'
            else:
                # 下行缓步运行判定条件
                if (trend_consecutive['ma3_max_consecutive_days'] >= min_threshold
                        and trend_consecutive['ma3_prop'] >= 0.8):
                    trend_result['Check_Trend_ConsecuState'] = 'True'

        if len(trend_consecutive) > 0:
            if is_uptrend:
                # 上行趋势判定
                if (trend_consecutive['max_cor_consecutive_days'] >= min_threshold
                        and trend_consecutive['trend_cor_prop'] >= 0.8):
                    trend_result['Check_TrendCOR_ConsecuState'] = 'True'
            else:
                # 下行趋势判定
                if (trend_consecutive['max_cor_consecutive_days'] >= min_threshold
                        and trend_consecutive['trend_cor_prop'] >= 0.8):
                    trend_result['Check_TrendCOR_ConsecuState'] = 'True'

        trend_result['Check_Trend_Ma3Prop'] = trend_consecutive['ma3_prop']
        trend_result['Check_Trend_COR_Prop'] = trend_consecutive['trend_cor_prop']
        trend_result['Check_Trend_UpperDeviation'] = period_deviation['upper_deviation']
        trend_result['Check_Trend_LowerDeviation'] = period_deviation['lower_deviation']
    return trend_result


def cal_trend_consecutive(stk_data=None, ts_code=None, start_date=None, end_date=None):
    """统计价格序列中连续天数的特征指标。
    
    主要功能:
    - 计算日均价格变动幅度低于阈值(2%)的连续天数
    - 统计收盘价高于/低于开盘价的连续天数
    - 计算两种特征的占比
    - 计算收盘价高于/低于前5天平均收盘价的最大连续天数和占比
    
    参数:
        stk_data (DataFrame): 股票行情数据,包含OHLC价格,可选
        ts_code (str): 股票代码,stk_data为None时必填
        start_date (str): 起始日期,stk_data为None时必填
        end_date (str): 结束日期,stk_data为None时必填
        
    返回:
        dict: 包含以下指标:
            - max_consecutive_days (int): 日均价格变动幅度低于阈值的最大连续天数
            - narrow_prop (float): 日均价格变动幅度低于阈值的天数占比
            - max_cor_consecutive_days (int): 收盘价高于/低于开盘价的最大连续天数
            - rise_cor_prop (float): 收盘价高于开盘价的天数占比
            - ma3_max_consecutive_days (int): 收盘价高于/低于5日均线的最大连续天数
            - ma3_prop (float): 收盘价高于/低于5日均线的天数占比
    """
    if stk_data is None:
        stk_data = get_stock_data(start_date=start_date, end_date=end_date, stk_code=ts_code)
    if start_date is None:
        start_date = stk_data.index[0]
    if end_date is None:
        end_date = stk_data.index[-1]
    if stk_data.loc[end_date, 'close'] > stk_data.loc[start_date, 'close']:
        is_uptrend = True
    else:
        is_uptrend = False
    stk_data['avg_price'] = round(stk_data[['close', 'open', 'high', 'low']].mean(axis=1), 3)
    stk_data['daily_ratio'] = round(stk_data['avg_price'].pct_change() * 100, 3)
    # 避免除0错误
    stk_data['clsopen_ratio'] = round((stk_data['close'] / stk_data['open'].replace(0, np.nan) - 1) * 100, 3)
    stk_data['ratio_signal'] = abs(stk_data['daily_ratio']) <= 2
    stk_data['ratio_signal_cor'] = stk_data['clsopen_ratio'] >= 0 \
        if is_uptrend else stk_data['clsopen_ratio'] <= 0

    # 计算5日均线(不足5天时取已有天数均值)
    stk_data['ma3'] = stk_data['close'].expanding().mean()
    stk_data.loc[stk_data.index[4:], 'ma3'] = stk_data['close'].rolling(window=5).mean()[4:]

    if is_uptrend:
        stk_data['ma3_signal'] = stk_data['close'] > stk_data['ma3'].shift(1)
    else:
        stk_data['ma3_signal'] = stk_data['close'] < stk_data['ma3'].shift(1)
    
    if start_date is not None and end_date is not None:
        stk_data = stk_data.loc[start_date:end_date].copy()

    count = 0
    consecutive_days = 0
    max_consecutive_days = 0
    for index in stk_data.index:
        if stk_data.loc[index, 'ratio_signal']:
            consecutive_days += 1
        else:
            count += 1
            max_consecutive_days = consecutive_days \
                if max_consecutive_days < consecutive_days else max_consecutive_days
            consecutive_days = 0
    max_consecutive_days = consecutive_days \
        if max_consecutive_days < consecutive_days else max_consecutive_days

    count_cor = 0
    consecutive_days_cor = 0
    max_consecutive_days_cor = 0
    for index in stk_data.index:
        if stk_data.loc[index, 'ratio_signal_cor']:
            consecutive_days_cor += 1
        else:
            count_cor += 1
            max_consecutive_days_cor = consecutive_days_cor \
                if max_consecutive_days_cor < consecutive_days_cor else max_consecutive_days_cor
            consecutive_days_cor = 0
    max_consecutive_days_cor = consecutive_days_cor \
        if max_consecutive_days_cor < consecutive_days_cor else max_consecutive_days_cor

    # 计算ma3信号的最大连续天数
    consecutive_days_ma3 = 0
    max_consecutive_days_ma3 = 0
    for index in stk_data.index:
        if stk_data.loc[index, 'ma3_signal']:
            consecutive_days_ma3 += 1
        else:
            max_consecutive_days_ma3 = consecutive_days_ma3 \
                if max_consecutive_days_ma3 < consecutive_days_ma3 else max_consecutive_days_ma3
            consecutive_days_ma3 = 0
    max_consecutive_days_ma3 = consecutive_days_ma3 \
        if max_consecutive_days_ma3 < consecutive_days_ma3 else max_consecutive_days_ma3

    # 计算各种占比,避免除0错误
    data_len = len(stk_data.dropna())
    if data_len > 0:
        narrow_prop = round(sum(stk_data['ratio_signal']) / data_len, 3)
        trend_cor_prop = round(sum(stk_data['ratio_signal_cor']) / data_len, 3)
        ma3_prop = round(sum(stk_data['ma3_signal'].dropna()) / data_len, 3)
    else:
        narrow_prop = 0
        trend_cor_prop = 0
        ma3_prop = 0

    return {'max_consecutive_days': max_consecutive_days,
            'narrow_prop': narrow_prop,
            'max_cor_consecutive_days': max_consecutive_days_cor,
            'trend_cor_prop': trend_cor_prop,
            'ma3_max_consecutive_days': max_consecutive_days_ma3,
            'ma3_prop': ma3_prop}


def cal_trend_wls(stk_data, draw_pic=False):
    """使用WLS模型，计算价格通道偏离指标,评估股票价格是否在一条斜直线通道内运行"""
    if len(stk_data) > 5:
        stk_data_wls = stk_data.copy()
        # 使用收盘价和开盘价的均值作为价格
        stk_data_wls['Mean'] = round((stk_data_wls['close'] + stk_data_wls['open']) / 2, 3)
        stk_data_train = stk_data_wls
        train_nums = np.arange(0, len(stk_data_train))
        train_dates = sm.add_constant(train_nums)
        train_prices = stk_data_train['Mean'].values

        # 先使用OLS模型计算残差得到WLS的权重
        OLS_model = sm.OLS(train_prices, train_dates)
        OLS_result = OLS_model.fit()

        # 根据OLS残差设置WLS权重
        if all(OLS_result.resid) != 0:
            # 残差越小权重越大,使拟合更贴近主要趋势
            WLS_weight = 1 / abs(OLS_result.resid)
        else:
            # 如果残差全为0,则根据时间设置权重,近期数据权重更高
            weights = np.diag(np.linspace(0.1, 1, len(train_prices)))
            for i in range(len(train_prices)):
                weights[i, i] = (i + 1) / len(train_prices)
            WLS_weight = weights.diagonal()

        # 创建WLS模型并拟合
        WLS_model = sm.WLS(train_prices, train_dates, weights=WLS_weight)
        wls_res = WLS_model.fit()

        # 计算实际价格与拟合线的偏离度
        deviation = np.abs(train_prices - wls_res.fittedvalues)
        max_deviation_price = train_prices[deviation.argmax()]

        # 计算价格通道的上下轨
        upper_intercept = max_deviation_price + deviation.max()
        lower_intercept = max_deviation_price - deviation.max()

        # 计算通道宽度
        dev_ratio = round((upper_intercept - lower_intercept) / max_deviation_price * 100, 3)

        # 计算斜率(每日涨跌幅)
        slope_ratio = round((wls_res.fittedvalues[-1] / wls_res.fittedvalues[0] - 1) * 100 / len(train_prices), 3)
        # 拟合优度
        r2 = round(wls_res.rsquared, 3)
        # 最大偏离度占价格比例
        # dev_ratio = round((deviation.max() / max_deviation_price) * 100, 3)

        # 计算价格相对预测价格高低比例
        # over_middle_ratio = round(sum(test_prices - pred_prices > 0) / len(test_prices), 3)
        # over_upper_ratio = round(sum(test_prices - pred_upper > 0) / len(test_prices), 3)
        # under_lower_ratio = round(sum(test_prices - pred_lower < 0) / len(test_prices), 3)

        # 绘制拟合直线及上下轨直线
        if draw_pic:
            # 计算上下轨直线的价格
            upper_band = wls_res.predict(train_dates) + (upper_intercept - max_deviation_price)
            lower_band = wls_res.predict(train_dates) + (lower_intercept - max_deviation_price)

            # 绘制价格和拟合的直线
            plt.scatter(train_nums, train_prices, label='Prices')
            plt.plot(train_nums, wls_res.fittedvalues, color='red', label='WLS Fit')

            # 绘制上轨和下轨
            plt.plot(train_nums, upper_band, '--', color='green', label='Upper Band')
            plt.plot(train_nums, lower_band, '--', color='green', label='Lower Band')

            # 设置图例和标题
            plt.legend()
            plt.title('Stock Price Trend')
            plt.show()
    else:
        slope_ratio, r2, dev_ratio, over_middle_ratio, over_upper_ratio, under_lower_ratio = 0, 0, 0, 0, 0, 0
    # , over_middle_ratio, over_upper_ratio, under_lower_ratio
    return slope_ratio, r2, dev_ratio


def update_result_column(end_date=None, mode='First_Half', multi_pro=True, freqdata_source='api'):
    """更新筛选结果的前半部分"""
    if mode.lower() not in ['first_half', 'second_half']:
        print('请输入正确的mode参数(first_half 或 second_half)！')
        return
    start_time = datetime.now()
    print('start_time:', start_time)
    from function_ai.StkPick_Func_V7 import get_result_3
    result_store = get_result_3(end_date=end_date, mode=mode)
    if len(result_store) == 0:
        print('数据库无', end_date, '对应指标数据')
        return
    result_store = result_store.drop(columns={'id'})
    # store_columns = result_store.columns.tolist()
    _, gapvalue_column_names = set_resultindexs()
    signal = True
    if mode.lower() == 'first_half':
        result_update, _ = stksfit_result_3(end_date=end_date, cal_mode='All')
        # index = store_columns.index('PostSecStart_MaxPeakGap')
        # storepick_columns = store_columns[index:]
        # storepick_columns.append('ts_code')
        # delete_columns = store_columns[index:-1]
        # result_update_1 = result_update.drop(columns=gapvalue_column_names)
        # gapvalue_column_names.append('ts_code')
        # result_combin = pd.merge(
        #     result_update_1, result_store[gapvalue_column_names], on='ts_code', how='left')
    elif mode.lower() == 'second_half':
        result_common = get_result_3(end_date=end_date, mode='First_Half')
        result_combin = gap_multiprocess(Result_GapValue=result_store, Result_Common=result_common, cal_date=end_date,
                                         multi_pro=multi_pro, data_source=freqdata_source)
        if all(pd.isnull(result_combin['Now_ValleyGapValue'])):
            print('Second_Half数据未更新！')
            signal = False
    elif mode.lower() == 'second_single':
        result_common = get_result_3(end_date=end_date, mode='First_Half')
        result_combin = gap_multiprocess(Result_GapValue=result_store, Result_Common=result_common, cal_date=end_date,
                                         multi_pro=multi_pro, cal_indexnum='single',
                                         data_source=freqdata_source)
        if all(pd.isnull(result_combin['Now_ValleyGapValue'])):
            print('Second_Half数据未更新！')
            signal = False
    else:
        print('请输入正确的mode参数(First_Half 或 Second_Half)！')
        return
    if signal and len(result_combin) > 0:
        from sqlalchemy import create_engine, text
        import config.config_Ali as config
        conf = config.configModel()
        engine = create_engine(
            'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
                conf.DC_DB_PORT) + '/stocksfit')
        Sesstion = sessionmaker(bind=engine)
        session = Sesstion()
        
        if mode.lower() == 'first_half':
            table_name = 'stk_results_common'
        elif mode.lower() == 'second_half':
            table_name = 'stk_results_gapvalue'
        else:
            print('请输入正确的mode参数以设定table_name(first_half 或 second_half)！')
            return
        try:
            delete_sql = text(f"delete from stocksfit.{table_name} where Cal_Date= :end_date")
            with session.begin():
                session.execute(delete_sql, {"end_date": end_date})
            session.commit()
        except Exception as e:
            session.rollback()
            print('An error occured:', e)
        try:
            pd.io.sql.to_sql(result_combin, table_name, engine, index=False, schema='stocksfit',
                             if_exists='append')
            print('数据更新成功！已存储')
        except:
            pdb.set_trace()
        session.close()
        engine.dispose()
    end_time = datetime.now()
    print('end_time: ', end_time)
    print('耗时： ', end_time - start_time)
    return result_combin


def cal_period_pricetrend(stk_data=None, stk_code=None, start_date=None, end_date=None):
    """计算股票逐日均价变动数据"""
    if stk_data is None:
        stock_data = get_stock_data(stk_code=stk_code, start_date=start_date, end_date=end_date)
    else:
        stock_data = stk_data.copy()
    mode = 'rise' if stock_data['close'].iloc[-1] > stock_data['close'].iloc[0] else 'drop'
    stock_data['mean_price'] = round(
        (stock_data['high'] + stock_data['low'] + stock_data['close'] + stock_data['open']) / 4, 2)
    stock_data['mean_diff'] = round((stock_data['mean_price'] / stock_data['mean_price'].shift(1) - 1) * 100, 3)
    if mode == 'rise':
        trend_avgratio = round(stock_data.query('mean_diff>0')['mean_diff'].mean(), 3)
        trend_std = round(stock_data.query('mean_diff>0')['mean_diff'].std(), 3)
        reverse_avgratio = round(stock_data.query('mean_diff<=0')['mean_diff'].mean(), 3)
        reverse_std = round(stock_data.query('mean_diff<=0')['mean_diff'].std(), 3)
    else:
        trend_avgratio = round(stock_data.query('mean_diff<0')['mean_diff'].mean(), 3)
        trend_std = round(stock_data.query('mean_diff<0')['mean_diff'].std(), 3)
        reverse_avgratio = round(stock_data.query('mean_diff>=0')['mean_diff'].mean(), 3)
        reverse_std = round(stock_data.query('mean_diff>=0')['mean_diff'].std(), 3)
    trend_prop = round(len(stock_data.query('mean_diff<0')) / len(stock_data), 3) \
        if mode == 'drop' else round(len(stock_data.query('mean_diff>0')) / len(stock_data), 3)
    output = {'trend_avgratio': trend_avgratio, 'trend_std': trend_std,
              'adverse_avgratio': reverse_avgratio, 'adverse_std': reverse_std,
              'trend_prop': trend_prop,
              'period_avg_chg': round((stock_data['close'].iloc[-1] /
                                       stock_data['close'].iloc[0] - 1) * 100 / (len(stock_data) - 1), 3)}
    return output


def calculate_deviation(df, start_date=None, end_date=None):
    """
    计算区间内股票价格相对拟合直线的上方和下方偏离幅度。

    参数：
    df: pd.DataFrame，包含列['date', 'open', 'close', 'high', 'low']

    返回：
    dict，包括 'upper_deviation' 和 'lower_deviation'
    """
    # # 确保数据按日期排序
    # stk_df = stk_df.sort_values(by='trade_date')
    stk_df = df.copy()
    if start_date is not None and end_date is not None:
        stk_df = stk_df.loc[start_date:end_date].copy()
    # 计算首尾均价
    start_avg_price = stk_df.iloc[0][['open', 'close', 'high', 'low']].mean()
    end_avg_price = stk_df.iloc[-1][['open', 'close', 'high', 'low']].mean()
    max_idx = stk_df['close'].idxmax()
    max_price = stk_df.loc[max_idx, 'close']
    start_price = stk_df['close'].iloc[0]

    # 拟合直线的系数
    start_date = 0
    end_date = len(stk_df) - 1
    slope = (end_avg_price - start_avg_price) / (end_date - start_date) \
        if end_date - start_date > 0 else end_avg_price - start_avg_price
    intercept = start_avg_price  # 在起点的价格

    # 计算直线上的价格
    stk_df['fitted_price'] = slope * np.arange(len(stk_df)) + intercept

    # 计算偏离幅度
    # stk_df['upper_deviation'] = (stk_df['high'] - stk_df['fitted_price']) / stk_df['fitted_price']
    # stk_df['lower_deviation'] = (stk_df['low'] - stk_df['fitted_price']) / stk_df['fitted_price']
    stk_df['close_deviation'] = (stk_df['close'] - stk_df['fitted_price']) / stk_df['fitted_price']

    # 返回最大上方偏离和最小下方偏离
    # upper_deviation = stk_df['upper_deviation'].max()
    # lower_deviation = stk_df['lower_deviation'].min()
    upper_deviation = stk_df['close_deviation'].max()
    lower_deviation = stk_df['close_deviation'].min()

    return {
        'sec_sumratio': round((end_avg_price / start_avg_price - 1) * 100, 3),
        'sec_avgratio': round(slope / start_avg_price * 100, 3),
        'sec_maxavgratio': round((max_price / start_price - 1) * 100 / (len(stk_df.loc[:max_idx]) - 1), 3)
        if len(stk_df.loc[:max_idx]) - 1 > 0 else 0,
        'upper_deviation': round(upper_deviation * 100, 3),
        'lower_deviation': round(lower_deviation * 100, 3),
        'sec_lastdays': len(stk_df) - 1
    }


def transfer_data_to_sql(date=None):
    """将原表中数据切割后分别存储到两个新表"""
    import config.config_Ali as config
    conf = config.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf.DC_DB_USER + ':' + conf.DC_DB_PASS + '@' + conf.DC_DB_URL + ':' + str(
            conf.DC_DB_PORT) + '/stocksfit')
    if date is not None:
        sql = f"""
                 select * from stocksfit.stk_results_3 where Cal_Date = '{date}'
              """
        origin_data = pd.read_sql(sql, engine)
        if len(origin_data) == 0:
            print('所选日期无数据！')
            return
        common_columns, gapvalue_columns = set_resultindexs()
        gapvalue_columns = ['ts_code'] + gapvalue_columns
        gapvalue_data = origin_data[gapvalue_columns].copy()
        common_columns = list(set(origin_data.columns) - (set(gapvalue_columns) - set(['ts_code', 'Cal_Date'])))
        result_new = get_result_3(end_date='2025-06-24', mode='all')
        new_columns = result_new.columns
        common_columns = list(set(common_columns) & set(new_columns))
        common_data = origin_data[common_columns].copy()
        try:
            pd.io.sql.to_sql(common_data, 'stk_results_common', engine, index=False, schema='stocksfit',
                             if_exists='append')
            print('Common数据更新成功！已存储')
        except:
            pdb.set_trace()
        try:
            pd.io.sql.to_sql(gapvalue_data, 'stk_results_gapvalue', engine, index=False, schema='stocksfit',
                             if_exists='append')
            print('GapValue数据更新成功！已存储')
        except:
            pdb.set_trace()
    else:
        print('请输入正确的日期！')
    engine.dispose()
    return



if __name__ == '__main__':
    stk_temp, _  = stksfit_result_3(end_date='2025-05-16', cal_mode='ADJ', stk_code='300204.SZ',freqdata_source='api', storemode=False)

    # end_date, stk_code = '2022-10-17', '000721.SZ'
    # resultB = stksfit_result_3(end_date=end_date, mode='ADJ', stk_code=stk_code)
    # resultB = stksfit_result_3(end_date='2023-02-02', mode='ADJ', stk_code='603439.SH')
    # resultB = stksfit_result_3(end_date='2024-04-17', mode='ADJ', stk_code='600297.SH')
    # from function_ai.StkPick_Func_V7 import get_result_3
    # # result = get_result_3(start_date='2024-07-23', end_date='2024-07-31', stk_list=['600000.SH'])
    # result = get_result_3(start_date='2024-08-20', end_date='2024-08-30', stk_list=['600000.SH'])
    # result_temp = result[result['Now_PostSecPeak_PGV_RollAvg_MinCoverDays'].isnull()].copy()
    # date_list = result.sort_values(by='Cal_Date', ascending=True)['Cal_Date'].values.tolist()
    # date_list.sort(reverse=True)
    # print('计算日期：', date_list)
    # for date in date_list:
    #     if len(result.query('Cal_Date==@date'))==0 or \
    #             pd.isnull(result.query('Cal_Date==@date')['Now_PostSecPeak_PGV_RollAvg_MinCoverDays'].iloc[-1]):
    #         resultB = stksfit_result_3(end_date=date, mode='pick')
    #     else:
    #         resultB = update_result_column(end_date=date, mode='Second_Half')
    #     print('finish date:', date)
