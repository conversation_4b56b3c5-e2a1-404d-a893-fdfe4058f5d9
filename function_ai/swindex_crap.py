# import pdb
from datetime import datetime as dt
from urllib.parse import quote_plus as urlquote
from pathlib import Path

from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import pandas as pd
import time

from sqlalchemy.orm import sessionmaker

from tqdm import tqdm
from sqlalchemy import create_engine, text

import os
import sys

# 获取项目根目录路径
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if root_dir not in sys.path:
    sys.path.append(root_dir)

from config import config_Ali as config_Ali
from function_ai.Func_Base import get_trade_date
from pathlib import Path
import datetime as dt


def get_max_swdate():
    """获取sw数据库最大日期"""
    conf_a = config_Ali.configModel()
    engine_ali = create_engine(
        'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
            conf_a.DC_DB_PORT) + '/stocksfit')
    sql = """select max(trade_date) as max_date from stocksfit.swindex_data """
    for _ in range(3):
        try:
            max_date = pd.read_sql_query(sql=sql, con=engine_ali)
        except:
            time.sleep(3)
        else:
            break
    trade_dates = get_trade_date()
    new_dates = trade_dates[trade_dates>max_date['max_date'].iloc[-1]]
    if len(new_dates) > 0:
        return new_dates[0]
    else:
        return None


def crap_swindex_from_web(end_date=None, mode='history'):
    """从网站爬取申万行业指数收盘数据"""
    # 初始化Selenium Chrome浏览器
    chromedriver_path = Path("/Users/<USER>/PycharmProjects/ChromeDriver/chromedriver")
    chrome_service = ChromeService(executable_path=chromedriver_path)
    driver = webdriver.Chrome(service=chrome_service)

    # 打开初始网址
    base_url = "https://www.swsresearch.com"
    driver.get(base_url + "/institute_sw/allIndex/releasedIndex")

    # 等待数据加载完成
    time.sleep(30)

    # 点击"一级行业"
    industry_button = driver.find_element(By.ID, "rc-tabs-0-tab-2")
    industry_button.click()

    # 等待数据加载完成
    wait = WebDriverWait(driver, 10)

    if mode.lower() == 'history':
        # 点击“历史行情”
        historical_button = driver.find_element(By.CSS_SELECTOR,
                                                "#root > div > div:nth-child(2) > div > div.rightBox___3WH44 > "
                                                "div:nth-child(1) > div > div.content___19Peq > div.top___llIGR > button")
        historical_button.click()

        wait = WebDriverWait(driver, 10)

        # 更改日期范围
        # if start_date is not None:
        #     driver.find_element(By.ID, "basic_range").send_keys(start_date)
        #     driver.find_element(By.CSS_SELECTOR, "#basic > div:nth-child(2) > div.ant-col.ant-col-13 > "
        #                                          "button.ant-btn.ant-btn-primary").click()

        # 等待历史行情表格加载完成
        # wait.until(EC.presence_of_element_located((By.CLASS_NAME, "ant-table-container")))
        # time.sleep(5)

        # 初始化存储数据的列表
        data_list = []

        # 等待历史行情表格加载完成
        wait.until(EC.presence_of_element_located((By.CLASS_NAME, "bottom___2KKdk")))

        # 获取分页页码
        page_list = driver.find_element(By.CSS_SELECTOR,
                                        "#root > div > div:nth-child(2) > div > div.rightBox___3WH44 > "
                                        "div:nth-child(1) > div > div.content___19Peq > div.found_content___1hC9C > "
                                        "div:nth-child(2) > div.bottom___2KKdk > ul")

        page_nums = page_list.find_elements(By.TAG_NAME, 'li')
        pages = [page_num.text.strip() for page_num in page_nums]

        # 获取分页数据
        page_button = driver.find_element(
            By.CSS_SELECTOR,
            '#root > div > div:nth-child(2) > div > div.rightBox___3WH44 > div:nth-child(1) > div > '
            'div.content___19Peq > div.found_content___1hC9C > div:nth-child(2) > '
            'div.bottom___2KKdk > ul > li.ant-pagination-next > button')

        # 遍历每个分页链接并提取数据
        for num in range(0, len(pages)):
            time.sleep(5)  # 等待数据加载
            table = driver.find_element(By.CLASS_NAME, "ant-table-container")
            rows = table.find_elements(By.TAG_NAME, "tr")
            for row in rows[2:]:  # 跳过表头行
                cells = row.find_elements(By.TAG_NAME, "td")
                data_row = [cell.text.strip() for cell in cells]
                data_list.append(data_row)
            page_button.click()

        first_row = rows[0]
        first_cells = first_row.find_elements(By.TAG_NAME, "th")
        columns = [first_cell.text.strip() for first_cell in first_cells]

    elif mode.lower() == 'today':
        # 初始化存储数据的列表
        data_list = []

        # 等待today行情表格加载完成
        wait.until(EC.presence_of_element_located((By.CLASS_NAME, "bottom___2KKdk")))

        # 获取分页页码
        page_list = driver.find_element(By.CSS_SELECTOR,
                                        "#root > div > div:nth-child(2) > div > div.rightBox___3WH44 > "
                                        "div:nth-child(1) > div > div.content___19Peq > div.content___2GcQD > "
                                        "div > div.bottom___2KKdk")

        page_nums = page_list.find_elements(By.TAG_NAME, 'li')
        pages = [page_num.text.strip() for page_num in page_nums]

        # 获取分页数据
        page_button = driver.find_element(
            By.CSS_SELECTOR,
            '#root > div > div:nth-child(2) > div > div.rightBox___3WH44 > div:nth-child(1) > div > '
            'div.content___19Peq > div.content___2GcQD > div > div.bottom___2KKdk > ul > '
            'li.ant-pagination-next > button')

        # 遍历每个分页链接并提取数据
        for num in range(0, len(pages)):
            time.sleep(5)  # 等待数据加载
            table = driver.find_element(By.CLASS_NAME, "ant-table-container")
            rows = table.find_elements(By.TAG_NAME, "tr")
            for row in rows[2:]:  # 跳过表头行
                cells = row.find_elements(By.TAG_NAME, "td")
                data_row = [cell.text.strip() for cell in cells]
                data_list.append(data_row)
            page_button.click()

        first_row = rows[0]
        first_cells = first_row.find_elements(By.TAG_NAME, "th")
        columns = [first_cell.text.strip() for first_cell in first_cells]

    else:
        print('参数错误，退出')
        return

    # 创建一个Pandas DataFrame
    # columns = ["指数代码", "指数名称", "昨收盘", "今开盘", "最新价", "成交额(百万元)", "成交量(百万股)", "最高价", "最低价"]  # 根据实际表格列数调整
    if columns[-1] == '':
        columns[-1] = '最低价'
    df = pd.DataFrame(data_list, columns=columns)
    df = df.query('指数代码!=""')
    df_column = df.columns
    for num in range(3, len(df_column)):
        df[df_column[num]] = df[df_column[num]].astype(float)
    # df['成交额(百万元)'] = df['成交额(百万元)'] / 100
    # df['成交量(百万股)'] = df['成交量(百万股)'] / 100
    # df = df.rename(columns={'今开盘': '开盘指数',
    #                         '最高价': '最高指数',
    #                         '最低价': '最低指数',
    #                         '最新价': '收盘指数'})
    # df = df.drop(columns=['昨收盘'])

    # update_date = dt.date.today().strftime('%Y-%m-%d')
    # df['发布日期'] = update_date
    if '发布日期' in df_column:
        update_dates = df['发布日期'].unique().tolist()
    else:
        df['发布日期'] = end_date
        update_dates = [end_date]

    # 检测df中是否存在空值，如存在空值则报错并退出
    if df.isnull().values.any():
        raise ValueError("爬取数据中存在空值，请检查数据源是否发生变化！")
    if len(df['指数名称'].unique()) < 31:
        print("爬取数据存在缺失，缺失", 31-len(df['指数名称'].unique()), "个行业数据，需重新运行更新程序！")

    # 保存数据到Excel文件
    excel_filename = Path('/Users/<USER>/PycharmProjects/AI_Stock/index_study/申万指数历史行情_1.xls')
    df.to_excel(excel_filename, index=False)
    print('完成数据更新：', '/'.join(update_dates), '\n',
          "数据已保存到Excel文件:", excel_filename)

    # 关闭浏览器
    driver.quit()
    return df


def initialize_level1():
    import config.config_fof as config_fof
    conf_a = config_fof.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + urlquote(
            conf_a.DC_DB_PASS) + '@' + conf_a.DC_DB_URL + ':' + str(
            conf_a.DC_DB_PORT) + '/fof')
    sw_newfile_1 = Path('/Users/<USER>/PycharmProjects/Ai_Stock/index_study/申万指数历史行情_1.xls')
    sw_newdata = pd.read_excel(sw_newfile_1)
    sw_newdata = sw_newdata.rename(columns={'指数名称': 'index_name', '指数代码': 'index_code'})
    index_names = sw_newdata['index_name'].unique()
    index_codes = sw_newdata['index_code'].unique()
    start_date = '2012-01-01'
    end_date = dt.now().strftime('%Y-%m-%d')
    sw_data = pd.DataFrame()
    for indexnum, index_code in tqdm(enumerate(index_codes)):
        sql_code = """
                    select InnerCode, ChiName, SecuCode from fof.secumain where SecuCode=%(index_code)s 
                   """
        for _ in range(3):
            try:
                SWCode = pd.read_sql_query(sql=sql_code, con=engine, params={'index_code': index_code})
            except:
                time.sleep(3)
            else:
                break
        if len(SWCode) > 0:
            innercode = SWCode['InnerCode'].iloc[0]
        else:
            print('未获取到申万行业innercode：', index_code)
            return
        sql_quote = """select ClosePrice as close, TradingDay as trade_date 
                       from fof.qt_sywgindexquote
                       where InnerCode=%(innercode)s and TradingDay>=%(start_date)s and TradingDay<=%(end_date)s
                    """
        for _ in range(3):
            try:
                sw_close = pd.read_sql_query(sql=sql_quote,
                                             con=engine,
                                             params={'innercode': innercode,
                                                     'start_date': start_date,
                                                     'end_date': end_date})
            except:
                time.sleep(3)
            else:
                break
        sw_close['trade_date'] = sw_close['trade_date'].apply(lambda fn: fn.strftime('%Y-%m-%d'))
        sw_close = sw_close.sort_values('trade_date', ascending=True)
        df = pd.DataFrame(
            {'trade_date': sw_close['trade_date'].values.tolist(),
             index_names[indexnum]: sw_close['close'].values.tolist()},
            index=range(0, len(sw_close)))
        if len(sw_data) == 0:
            sw_data = df
        else:
            sw_data = pd.merge(sw_data, df, how='outer', on='trade_date')
    # pd.io.sql.to_sql(sw_data, 'swindex_data', engine, index=False, schema='stocksfit', if_exists='append')
    engine.dispose()
    return sw_data


def level1_update(mode='update'):
    conf_a = config_Ali.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + urlquote(
            conf_a.DC_DB_PASS) + '@' + conf_a.DC_DB_URL + ':' + str(
            conf_a.DC_DB_PORT) + '/stocksfit', connect_args={'connect_timeout': 60})

    sw_newfile_1 = Path('/Users/<USER>/PycharmProjects/Ai_Stock/index_study/申万指数历史行情_1.xls')
    sw_newdata = pd.read_excel(sw_newfile_1)
    if '最新价' in sw_newdata.columns:
        sw_newdata = sw_newdata.rename(columns={'最新价': '收盘指数'})
    if '发布日期' not in sw_newdata.columns:
        today = dt.now().strftime('%Y-%m-%d')
        sw_newdata['发布日期'] = today
    sw_newdata = sw_newdata.rename(columns={'指数名称': 'index_name', '收盘指数': 'close', '发布日期': 'trade_date'})
    # sw_newdata['index_name'] = sw_newdata.apply(lambda fn: fn['index_name'][:-4], axis=1)
    if isinstance(sw_newdata['trade_date'].iloc[-1], dt.date):
        sw_newdata['trade_date'] = sw_newdata['trade_date'].apply(lambda fn: fn.strftime('%Y-%m-%d'))
    index_names = sw_newdata['index_name'].unique()
    sql = """select trade_date from stocksfit.swindex_data"""
    for _ in range(3):
        try:
            sw_data = pd.read_sql_query(sql=sql, con=engine)
        except Exception as e:
            time.sleep(3)
        else:
            break
    store_dates = sw_data['trade_date'].unique()
    newdata_dates = sw_newdata['trade_date'].unique()
    if mode.lower() == 'update':
        new_dates = list(set(newdata_dates) - set(store_dates))
        if len(new_dates) > 0:
            sw_newdata = sw_newdata.query('trade_date in @new_dates')
        else:
            sw_newdata = pd.DataFrame()
    elif mode.lower() == 'replace':
        new_dates = list(set(newdata_dates) & set(store_dates))
        if len(new_dates) > 0:
            Sesstion = sessionmaker(bind=engine)
            session = Sesstion()
            try:
                delete_sql = text("delete from stocksfit.swindex_data where trade_date in :end_date")
                with session.begin():
                    session.execute(delete_sql, {"end_date": tuple(new_dates)})
                session.commit()
            except Exception as e:
                session.rollback()
                print('An error occured:', e)
    # 识别sw_newdata中是否有空值，如有则提醒并退出
    if sw_newdata.isnull().any().any():
        print('Excel表格中的申万行业指数数据中存在空值或数据为空！')
        return

    sw_data_adj = pd.DataFrame()
    if len(sw_newdata) > 0:
        for indexnum, index_name in enumerate(index_names):
            temp_data = sw_newdata.query('index_name==@index_name').sort_values('trade_date', ascending=True)
            df = pd.DataFrame(
                {'trade_date': temp_data['trade_date'].values.tolist(),
                 index_names[indexnum]: temp_data['close'].values.tolist()},
                index=range(0, len(temp_data)))
            if len(sw_data_adj) == 0:
                sw_data_adj = df
            else:
                sw_data_adj = pd.merge(sw_data_adj, df, how='outer', on='trade_date')
        sw_data_adj = sw_data_adj.drop_duplicates(subset=['trade_date'], keep='last')
        pd.io.sql.to_sql(sw_data_adj, 'swindex_data', engine, index=False, schema='stocksfit', if_exists='append')
    engine.dispose()
    if mode.lower() == 'update' and len(sw_data_adj) > 0:
        print('完成申万行业指数数据获取：', '/ '.join(sw_data_adj['trade_date'].values.tolist()))
    elif mode.lower() == 'replace' and len(sw_data_adj) > 0:
        print('完成申万行业指数数据替换：', '/ '.join(sw_data_adj['trade_date'].values.tolist()))
    elif len(sw_newdata) == 0:
        print('申万行业指数数据已有最新日期数据！无需更新。')
    else:
        print('未能更新申万行业指数数据，未获取到最新日期数据！')
    return sw_data_adj


def level2_update():
    sw_newfile_2 = Path('/Users/<USER>/PycharmProjects/AI_Stock/index_study/申万指数历史行情_2.xls')
    sw_file_2 = Path('/index_study/SW_data_L2.csv')
    sw_newdata = pd.read_excel(sw_newfile_2)
    sw_newdata = sw_newdata.rename(columns={'指数名称': 'index_name', '收盘指数': 'close', '发布日期': 'trade_date'})
    sw_data = pd.read_csv(sw_file_2)

    column_list = sw_data.columns.to_list()[1:]

    index_names = sw_newdata['index_name'].unique()

    sw_newdata_adj = pd.DataFrame()
    for indexnum, index_name in enumerate(index_names):
        temp_data = sw_newdata.query('index_name==@index_name').sort_values('trade_date', ascending=True)
        df = pd.DataFrame(
            {'trade_date': temp_data['trade_date'].values.tolist(), column_list[indexnum]: temp_data['close'].values.tolist()},
            index=range(0, len(temp_data)))
        if len(sw_newdata_adj) == 0:
            sw_newdata_adj = df
        else:
            sw_newdata_adj = pd.merge(sw_newdata_adj, df, how='outer', on='trade_date')

    sw_data = pd.concat([sw_data, sw_newdata_adj], ignore_index=True)

    sw_data = sw_data.drop_duplicates(subset=['trade_date'], keep='last')

    sw_data.to_csv(sw_file_2, index=False, encoding='utf-8-sig')
    return sw_data


def check_swindex_missingdate(check_startdate):
    """自check_startdate开始，对比申万指数数据日期与交易日期，检查申万指数数据是否缺失，并返回缺失的日期列表"""
    trade_dates = get_trade_date(start_date=check_startdate)
    conf_a = config_Ali.configModel()
    engine = create_engine(
        'mysql+pymysql://' + conf_a.DC_DB_USER + ':' + conf_a.DC_DB_PASS + '@' + conf_a.DC_DB_URL + ':' + str(
            conf_a.DC_DB_PORT) + '/stocksfit')
    sql = f"""select trade_date from stocksfit.swindex_data where trade_date>='{check_startdate}'"""
    sw_dates = pd.read_sql_query(sql, con=engine)
    engine.dispose()
    missing_dates = list(set(trade_dates) - set(sw_dates['trade_date'].values.tolist()))
    missing_dates.sort(reverse=True)
    return missing_dates


if __name__ == '__main__':
    # new_date = get_max_swdate()
    new_date = '2025-03-25'
    crap_swindex_from_web(end_date=new_date, mode='today')

